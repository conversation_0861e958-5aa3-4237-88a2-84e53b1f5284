#include "pch.h"
#include "MyPylon.h"
#include "opencv2/opencv.hpp"

#include <algorithm>
#include <vector>
#include "CirclePatternDetection.h"
#include <ctime>
#include <sstream>
#include <fstream>
#include <iostream>
#include <cmath>
#include <chrono>  
#include <random>

using namespace cv;
using namespace std;

int imageLineFind(cv::Mat& img, std::vector<cv::Vec4i>& lines, int value, int maxLen);
cv::Vec4f fitLineRANSAC(const std::vector<cv::Point2f>& points, float distanceThreshold, int maxIterations);
double visualizeLineFitting(cv::Mat& image, const std::vector<cv::Point2f>& points,
    const cv::Vec4f& line, double slope, double& angle, cv::Scalar color);

// 计算两条直线之间的距离（简单示例方法，可根据实际情况优化调整）
double distanceBetweenTwoLines(const cv::Vec4f& line1, const cv::Vec4f& line2, double &diff, double &angle) {

    double cos_theta = line1[0];
    double sin_theta = line1[1];
    double x0 = line1[2];
    double y0 = line1[3];
    double k0 = sin_theta / (cos_theta+0.00001);

    cos_theta = line2[0];
    sin_theta = line2[1];
    double x1 = line2[2];
    double y1 = line2[3];
    double k = sin_theta / (cos_theta + 0.00001);

    double tan_theta = std::abs((k - k0) / (1 + k0 * k));
    double theta_radian = std::atan(tan_theta);
    angle = theta_radian * 180 / CV_PI;

    diff = (y0 + k0 * (1500 - x0))-(y1 + k * (1500 - x1));

    return 0;
}

double angleBetweenTwoLines(const cv::Vec4f& line1, const cv::Vec4f& line2) {
    // 获取直线1的方向向量
    cv::Point2f dir1(line1[0], line1[1]);
    // 获取直线2的方向向量
    cv::Point2f dir2(line2[0], line2[1]);

    // 计算向量点积
    float dotProduct = dir1.x * dir2.x + dir1.y * dir2.y;
    // 计算向量模长乘积
    float magnitudeProduct = std::sqrt(dir1.x * dir1.x + dir1.y * dir1.y) * std::sqrt(dir2.x * dir2.x + dir2.y * dir2.y);

    // 使用acos函数结合点积和模长计算夹角余弦值，再通过atan2获取角度（弧度制）
    double cosTheta = dotProduct / magnitudeProduct;
    return std::atan2(std::sqrt(1 - cosTheta * cosTheta), cosTheta);
}

double calculateGrayMean(cv::Mat& image, int xStart, int yStart, int width, int height) {
    double sum = 0;
    int pixelCount = 0;

    // 遍历ROI区域内的像素
    for (int y = yStart; y < yStart + height; ++y) {
        for (int x = xStart; x < xStart + width; ++x) {
            sum += image.at<uchar>(y, x);
            pixelCount++;
        }
    }

    return static_cast<double>(sum) / pixelCount;
}

double calculateMeanV(cv::Rect& roi, cv::Mat& image, int x0, int y0, int x1) {
    static int id = 0;

    std::string name = "roi_" + std::to_string(id) + ".png";
    //imwrite(name, image(roi));
    id++;
    // 限制随机数生成范围在ROI内，获取宽度和高度
    int roi_width = roi.width;
    int roi_height = roi.height;

    // 设置随机数种子，确保每次运行生成的随机序列不同（基于当前时间）
    std::srand(static_cast<unsigned int>(std::time(nullptr)));

    // 用于存储挑选的点的像素灰度值
    std::vector<int> pixel_values;

    // 在ROI区域内随机挑选20个点并获取其像素灰度值
    for (int i = 0; i < 50; ++i) {
        int random_x = roi.x + std::rand() % roi_width;
        int random_y = roi.y + std::rand() % roi_height;
        int pixel_value = image.at<uchar>(random_y, random_x);
        pixel_values.push_back(pixel_value);
    }

    // 计算像素灰度均值
    int sum = 0;
    for (int value : pixel_values) {
        sum += value;
    }
    double mean = static_cast<double>(sum) / pixel_values.size();

    std::cout << "roi average pixel: " << mean << std::endl;

    return mean;
}

double calculateMeanVV(cv::Rect& roi, cv::Mat& image, int x0, int y0, int x1) {

    double sum = 0;
    cv::Mat grayImage = image(roi);
    int numPixels = grayImage.rows * grayImage.cols;

    // 遍历图像的每一个像素，累加灰度值
    for (int row = 0; row < grayImage.rows; ++row) {
        for (int col = 0; col < grayImage.cols; ++col) {
            sum += grayImage.at<uchar>(row, col);
        }
    }

    double meanVal = sum / numPixels;

    std::cout << "roi average pixel: " << meanVal << std::endl;

    return meanVal;
}

double getXValueAtMidY(cv::Point p1, cv::Point p2)
{
    // 先判断直线是否垂直于x轴（即两点的x坐标相同）
    if (p1.x == p2.x)
    {
        return p1.x;
    }
    // 计算直线斜率k
    double k = static_cast<double>(p2.y - p1.y) / (p2.x - p1.x);
    // 计算截距b
    double b = p1.y - k * p1.x;
    // 计算y=(y1+y2)/2处的x值
    double midY = static_cast<double>(p1.y + p2.y) / 2;
    double x = (midY - b) / k;
    return x;
}

double getAcuteAngle(double theta1, double theta2)
{
    double diff = fabs(theta1 - theta2);
    double angle1 = diff;
    double angle2 = std::_Pi - diff;
    return min(angle1, angle2) * 180 / std::_Pi;
}

double pointToLineDistance(double x0, double y0, double A, double B, double C) {
    return std::fabs(A * x0 + B * y0 + C) / std::sqrt(A * A + B * B);
}

// 根据两点坐标求直线的一般式方程系数
void getLineCoefficients(double x1, double y1, double x2, double y2, double& A, double& B, double& C) {
    A = y1 - y2;
    B = x2 - x1;
    C = x1 * (y2 - y1) - y1 * (x2 - x1);
}

bool isROIValid(const cv::Mat& img, const cv::Rect& roi) {
    cv::Rect validROI = roi & cv::Rect(0, 0, img.cols, img.rows);
    return (validROI.width == roi.width && validROI.height == roi.height);
}

// 处理图像中的直线相关操作，包括直线检测、筛选水平直线、计算夹角等
cv::Mat imageLineHandle(cv::Mat& img, double& diff, double& angle, int x_offset) {
    cv::Mat blurredImage;
    cv::GaussianBlur(img, blurredImage, cv::Size(5, 5), 1);

    //cv::Mat thresh;
    //cv::threshold(blurredImage, thresh, 150, 255, cv::THRESH_BINARY);
    Mat kernel = cv::getStructuringElement(MORPH_RECT, Size(3, 3));
    Mat dilation_dst;
    dilate(blurredImage, dilation_dst, kernel, Point(-1, -1), 2);    

    // 腐蚀操作
    Mat erosion_dst;
    erode(dilation_dst, erosion_dst, kernel, Point(-1, -1), 2);

    cv::Mat edges;
    cv::Canny(erosion_dst, edges, 50, 100);
    std::vector<double> firstLine;
    std::vector<cv::Vec4i> lines;
    cv::Mat imgColor;
    cv::cvtColor(img, imgColor, cv::COLOR_GRAY2BGR);

    std::vector<cv::Vec4i> horizontalLines;
    std::vector<double> lineTop;
    //cv::HoughLinesP(edges, lines, 1, CV_PI / 180, 100, 500, 80);
    int times = 0;
    while (times < 6) {
        //std::cout << "$$$$$$$$$$$$$$$$$$$$$$$$$$$$$" << " times:" << times << std::endl;
        lines.clear();
        horizontalLines.clear();
        lineTop.clear();
        int ret = imageLineFind(edges, lines, 150 - times * 20, 300 - times * 30);
        times++;
        if (ret != 0)
            continue;        

        if (!lines.empty()) {
            for (const auto& line : lines) {
                int x1 = line[0];
                int y1 = line[1];
                int x2 = line[2];
                int y2 = line[3];
                std::cout << x1 << " " << y1 << " " << x2 << " " << y2 << std::endl;

                //cv::line(imgColor, cv::Point(x1, y1), cv::Point(x2, y2), cv::Scalar(0, 0, 255), 2);

                double slope;
                if (x2 - x1 == 0) {
                    slope = std::numeric_limits<double>::infinity();
                }
                else {
                    slope = static_cast<double>(y2 - y1) / (x2 - x1);
                }

                if (-0.15 <= slope && slope <= 0.15) {
                    int y_min = (y1 + y2) / 2; // = std::max(y1, y2);
                    int x_min = (x1 + x2) / 2;
                    //cv::Rect roi(x1, y_max + 5, x2 - x1, 5);
                    //
                    cv::Rect roi(x_min, y_min + 3, 5, 5);
                    if (!isROIValid(img, roi))
                        continue;
                    double mean_v = calculateMeanVV(roi, img, x1, y_min, x2);

                    cv::Rect roi_top(x_min, y_min - x_offset, 10, x_offset);
                    if (!isROIValid(img, roi_top))
                        continue;
                    double mean_v_top = calculateMeanVV(roi_top, img, x1, y_min, x2);

                    //if (mean_v < 60 && mean_v_top - mean_v>50)
                        //continue;

                    //cv::line(imgColor, cv::Point(x1, y1), cv::Point(x2, y2), cv::Scalar(0, 0, 255), 2);
                    horizontalLines.push_back(line);
                    lineTop.push_back(mean_v);
                }
            }
        }
        else
        {
            std::cout << "imageLineHandleT empty " << std::endl;
            continue;
        }

        std::vector<std::vector<double>> linesss;
        for (const auto& line : horizontalLines) {
            double x1 = line[0];
            double y1 = line[1];
            double x2 = line[2];
            double y2 = line[3];

            double rho = (y1 + y2) / 2;
            double theta = std::atan2(y2 - y1, x2 - x1);
            linesss.push_back({ rho, theta });
        }
        
        cv::Vec4i v1;
        int pos = -1;
        double topMean;
        for (const auto& line : horizontalLines) {
            double mean_v;
            pos++;
            int x1 = line[0];
            int y1 = line[1];
            int x2 = line[2];
            int y2 = line[3];
            double min_x = (x1 + y1) / 2;
            double min_y = std::min(y1, y2);
            double yy = min_y - 10;
            if (yy < 0)
                yy = 0;
            cv::Rect roi(min_x, yy, 10, 8);
            if (!isROIValid(img, roi))
                continue;
            mean_v = calculateMeanVV(roi, img, x1, y1, x2);
            std::cout << "calculateMeanVV  " << mean_v << std::endl;

            //if (mean_v > 50)
                //continue;

            if (firstLine.empty() || linesss[pos][0] < firstLine[0]) {
                v1 = line;
                firstLine = linesss[pos];
                topMean = lineTop[pos];
            }
        }

        std::cout << "imageLineHandleT 3 " << std::endl;
        if (firstLine.empty()) {
            std::cerr << "can not find first line" << std::endl;
            continue;
        }

        double A2, B2, C2;
        getLineCoefficients(v1[0], v1[1], v1[2], v1[3], A2, B2, C2);

        pos = -1;
        std::vector<double> secondLine;
        cv::Vec4i v2;
        diff = 1000;
        std::cout << "imageLineHandleT 3_" << pos << std::endl;
        std::cout << "topMean:" << topMean << std::endl;
        for (const auto& line : horizontalLines) {
            pos++;

            std::cout << "imageLineHandleT 3_" << pos << std::endl;
            std::cout << "linesss[pos][0] - firstLine[0]" << linesss[pos][0] - firstLine[0] << std::endl;
            std::cout << "lineTop[pos] - topMean" << lineTop[pos] - topMean << std::endl;

            std::cout << "lineRight[pos]" << lineTop[pos] << std::endl;

            double A1, B1, C1;
            getLineCoefficients(line[0], line[1], line[2], line[3], A1, B1, C1);

            // 计算直线l2上一点(x3, y3)到直线l1的距离，即两条直线的距离
            double diff1 = pointToLineDistance(v1[0], v1[1], A1, B1, C1);

            if (diff1 < 5 && ((lineTop[pos] - topMean) < 50 || (lineTop[pos] - topMean) > -50))
                continue;

            if (secondLine.empty() || diff1 < diff)
            {
                diff = diff1;
                std::cout << "pos:" << pos << std::endl;
                v2 = line;
                secondLine = linesss[pos];
            }
        }

        std::cout << "imageLineHandleT 4 " << std::endl;
        int x11 = v1[0];
        int y11 = v1[1];
        int x22 = v1[2];
        int y22 = v1[3];

        cv::line(imgColor, cv::Point(x11, y11), cv::Point(x22, y22), cv::Scalar(0, 255, 0), 2, 16);
        if (secondLine.empty()) {
            diff = 0;
            angle = 0;
        }
        else
        {
            Point point1 = cv::Point(v2[0], v2[1]);
            Point point2 = cv::Point(v2[2], v2[3]);
            float x = img.cols / 2;
            double slope = 1.0*(point2.y - point1.y) / (point2.x - point1.x);
            double intercept = point1.y - slope * point1.x;

            double y = slope * x + intercept;
            diff = pointToLineDistance(x, y, A2, B2, C2);

            std::cout << "两条直线的距离为: " << diff << std::endl;
            cv::line(imgColor, point1, point2, cv::Scalar(0, 0, 255), 2, 16);
            //diff = secondLine[0] - firstLine[0];
            angle = getAcuteAngle(secondLine[1], firstLine[1]);
        }

        break;
    }
    
    std::cout << "imageLineHandleT 5 " << std::endl;
    if (firstLine.empty()) {
        return cv::Mat();
    }

    return imgColor;
}

cv::Mat imageLineHandleB(cv::Mat& img, double& diff, double& angle, int x_offset) {
    cv::Mat blurredImage;
    cv::GaussianBlur(img, blurredImage, cv::Size(3, 3), 1);

    //cv::Mat thresh;
    //cv::threshold(blurredImage, thresh, 150, 255, cv::THRESH_BINARY);

    cv::Mat edges;
    cv::Canny(blurredImage, edges, 10, 50);
    //imwrite("edges.jpg", edges);

    std::vector<cv::Vec4i> lines;
    //cv::HoughLinesP(edges, lines, 1, CV_PI / 180, 100, 500, 80);
    int times = 0;
    while (times < 6) {
        int ret = imageLineFind(edges, lines, 60 - times * 10, 100 - times * 10);
        if (ret == 0)
            break;
        times++;
    }
    cv::Mat imgColor;
    cv::cvtColor(img, imgColor, cv::COLOR_GRAY2BGR);

    std::vector<cv::Vec4i> horizontalLines;
    std::vector<double> lineBottom;

    if (!lines.empty()) {
        for (const auto& line : lines) {
            int x1 = line[0];
            int y1 = line[1];
            int x2 = line[2];
            int y2 = line[3];
            std::cout << x1 << " " << y1 << " " << x2 << " " << y2 << std::endl;

            //cv::line(imgColor, cv::Point(x1, y1), cv::Point(x2, y2), cv::Scalar(0, 0, 255), 2);

            double slope;
            if (x2 - x1 == 0) {
                slope = std::numeric_limits<double>::infinity();
            }
            else {
                slope = static_cast<double>(y2 - y1) / (x2 - x1);
            }

            if (-0.3 <= slope && slope <= 0.3) {
                int y_min = std::min(y1, y2);
                int yy = y_min- x_offset;
                if (y_min - x_offset < 0)
                    yy = 0;

                //cv::line(imgColor, cv::Point(line[0], line[1]), cv::Point(line[2], line[3]), cv::Scalar(0, 0, 255), 2, 16);
                //cv::Rect roi(x1, y_max + 5, x2 - x1, 5);
                cv::Rect roi(x1, yy, 20, 5);
                if (!isROIValid(img, roi))
                    continue;
                double mean_v = calculateMeanV(roi, img, x1, yy, x2);

                cv::Rect roi_bottom(x1, std::max(y1, y2), 10, 5);
                if (!isROIValid(img, roi_bottom))
                    continue;
                double mean_v_bottom = calculateMeanV(roi_bottom, img, x1, yy, x2);
                if (mean_v < 30)
                    continue;
                if(mean_v_bottom- mean_v>150)
                    continue;
                
                horizontalLines.push_back(line);
                lineBottom.push_back(mean_v);
            }
        }
    }
    else
    {
        std::cout << "imageLineHandleB empty " << std::endl;
        return cv::Mat();
    }

    std::cout << "imageLineHandleT 2 " << std::endl;

    std::vector<std::vector<double>> linesss;
    for (const auto& line : horizontalLines) {
        double x1 = line[0];
        double y1 = line[1];
        double x2 = line[2];
        double y2 = line[3];

        double rho = 0;
        if (x1 == x2) {
            rho = x1;
        }
        else
        {
            double k = (y2 - y1) / (x2 - x1);
            // 计算截距b
            double b = y1 - k * x1;
            // 计算y=0时的x值
            rho = b;
        }
        double theta = std::atan2(y2 - y1, x2 - x1);
        linesss.push_back({ rho, theta });
    }

    std::vector<double> firstLine;
    cv::Vec4i v1;
    int pos = -1;
    double bottomMean;
    for (const auto& line : horizontalLines) {
        double mean_v;
        pos++;
        int x1 = line[0];
        int y1 = line[1];
        int x2 = line[2];
        int y2 = line[3];
        double min_x = (x1 + y1) / 2;
        double max_y = std::max(y1, y2);
        double yy = max_y + 10;

        cv::Rect roi(min_x, yy, 10, 8);
        if (!isROIValid(img, roi))
            continue;
        mean_v = calculateMeanVV(roi, img, x1, y1, x2);
        std::cout << "calculateMeanVV  " << mean_v << std::endl;

        if (mean_v > 80)
            continue;

        if (firstLine.empty() || linesss[pos][0] > firstLine[0]) {
            v1 = line;
            firstLine = linesss[pos];
            bottomMean = lineBottom[pos];
        }
    }

    std::cout << "imageLineHandleB 3 " << std::endl;
    if (firstLine.empty()) {
        std::cerr << "can not find first line" << std::endl;
        return cv::Mat();
    }
    pos = -1;
    std::vector<double> secondLine;
    cv::Vec4i v2;

    std::cout << "imageLineHandleB 3_" << pos << std::endl;
    for (const auto& line : horizontalLines) {
        pos++;

        std::cout << "imageLineHandleT 3_" << pos << std::endl;
        std::cout << "linesss[pos][0] - firstLine[0]" << linesss[pos][0] - firstLine[0] << std::endl;
        std::cout << "lineBottom[pos] - topMean" << lineBottom[pos] - bottomMean << std::endl;

        std::cout << "lineBottom[pos]" << lineBottom[pos] << std::endl;

        if (lineBottom[pos] - bottomMean > 30 || lineBottom[pos] - bottomMean < -30) {
            if (firstLine[0]- linesss[pos][0] < 5 && (lineBottom[pos] - bottomMean) < 60 && (lineBottom[pos] - bottomMean) > -60)
                continue;
            if (secondLine.empty() || linesss[pos][0] > secondLine[0])
            {
                v2 = line;
                secondLine = linesss[pos];
            }
        }
    }

    std::cout << "imageLineHandleB 4 " << std::endl;
    int x11 = v1[0];
    int y11 = v1[1];
    int x22 = v1[2];
    int y22 = v1[3];

    cv::line(imgColor, cv::Point(x11, y11), cv::Point(x22, y22), cv::Scalar(0, 255, 0), 2, 16);
    if (secondLine.empty()) {
        diff = 0;
        angle = 0;
    }
    else
    {
        double A1, B1, C1;
        getLineCoefficients(v2[0], v2[1], v2[2], v2[3], A1, B1, C1);

        double A2, B2, C2;
        getLineCoefficients(v1[0], v1[1], v1[2], v1[3], A2, B2, C2);

        // 计算直线l2上一点(x3, y3)到直线l1的距离，即两条直线的距离
        diff = pointToLineDistance(v1[0], v1[1], A1, B1, C1);
        std::cout << "两条直线的距离为: " << diff << std::endl;
        cv::line(imgColor, cv::Point(v2[0], v2[1]), cv::Point(v2[2], v2[3]), cv::Scalar(0, 0, 255), 2, 16);
        //diff = secondLine[0] - firstLine[0];
        angle = getAcuteAngle(secondLine[1], firstLine[1]);
    }


    std::cout << "imageLineHandleB 5 " << std::endl;

    return imgColor;
}

int fitSuitableLine(cv::Vec4i& v, cv::Mat& img, cv::Mat& imgColor, double& A, double& B, double& C, Point& p1, int flag) {
    int min_y = std::min(v[1], v[3]);
    int max_y = std::max(v[1], v[3]);

    int min_x = std::min(v[0], v[2]);
    int max_x = std::max(v[0], v[2]);
    double mean_v = 0;

    if (flag == 1)
    {
        min_x = std::max(0, min_x - 10);
        max_x = std::min(max_x + 5, img.cols);
    }
        
    if (flag == 2)
    {
        for (int i = 1; i < 8; i++)
        {
            uchar pixel_value = img.at<uchar>(v[1], v[0]-i);
            mean_v += pixel_value;
        }
        mean_v = mean_v / 7;
        min_x = std::max(0, min_x - 5);
        max_x = std::min(max_x + 10, img.cols);
    }
        
    vector<Point> points;

    if (flag == 1) {
        for (int y = min_y; y < max_y; ++y) {
            for (int x = min_x; x < max_x; ++x) {
                uchar pixel_value = img.at<uchar>(y, x);

                if (pixel_value > 80) {
                    points.push_back(Point(x, y));
                    break; 

                }
            }
        }
    }

    if (flag == 2) {
        for (int y = min_y; y < max_y; ++y) {
            int low_80_flag = 0;
            for (int x = max_x; x > min_x; --x) {
                uchar pixel_value = img.at<uchar>(y, x);

                if (pixel_value > mean_v-50 && low_80_flag==0) {
                    continue;
                }
                if (pixel_value < mean_v-50)
                    low_80_flag = 1;
                if (pixel_value > mean_v-50 && low_80_flag == 1) {
                    points.push_back(Point(x, y));
                    break; // 停止当前行的扫描
                }
            }
        }
    }

    // 检查是否有足够的点进行拟合
    if (points.size() < 2) {
        cout << "Not enough points to fit a line!" << endl;
        return -1;
    }

    // 使用fitLine函数拟合直线
    Vec4f line;
    cv::fitLine(points, line, DIST_L2, 0, 0.01, 0.01);

    // 提取直线参数
    float vx = line[0];
    float vy = line[1];
    float x0 = line[2];
    float y0 = line[3];

    if (vx == 0)
    {
        p1.x = x0;
        p1.y = img.rows/2;
    }
    else
    {
        float m = vy / vx;
        float c = y0 - m * x0;

        p1.y = img.rows / 2;
        p1.x = (p1.y - c) / m;        
    }
    // 计算直线的两个端点（用于绘制）
    Point lefty((int)(x0 - 1000 * vx), (int)(y0 - 1000 * vy));
    Point righty((int)(x0 + 1000 * vx), (int)(y0 + 1000 * vy));

    cv::circle(imgColor, Point(x0, y0), 5, cv::Scalar(0, 0, 255), -1);
    // 绘制直线
    cv::line(imgColor, lefty, righty, Scalar(0, 255, 0), 2, LINE_AA);

    getLineCoefficients(v[0], v[1], v[2], v[3], A, B, C);

    return 0;
}

// 处理图像中的直线相关操作，包括直线检测、筛选直线、计算夹角等
cv::Mat imageLineHandleL(cv::Mat& img, double& diff, double& angle, int x_offset) {
    cv::Mat blurredImage;
    cv::GaussianBlur(img, blurredImage, cv::Size(5, 5), 1);

    //cv::Mat binaryImage;
    //cv::threshold(img, binaryImage, 100, 255, THRESH_BINARY);
    //cv::Mat binaryImage;
    //cv::threshold(img, binaryImage, 200, 255, THRESH_BINARY);
    
    cv::Mat edges;
    cv::Canny(blurredImage, edges, 15, 50);

    std::vector<cv::Vec4i> lines;

    int times = 0;
    std::cout << "imageLineHandleL 1 " << std::endl;
    cv::Mat imgColor;
    cv::cvtColor(img, imgColor, cv::COLOR_GRAY2BGR);
    std::vector<cv::Vec4i> verticalLines;

    std::vector<double> lineRight;
    std::vector<double> firstLine;

    while (times < 6) {
        verticalLines.clear();
        lines.clear();
        lineRight.clear();
        int ret = imageLineFind(edges, lines, 100 - times * 20, 100 - times * 10);
        times++;
        if (ret != 0)
            continue;

        if (!lines.empty()) {
            for (const auto& line : lines) {
                int x1 = line[0];
                int y1 = line[1];
                int x2 = line[2];
                int y2 = line[3];
                std::cout << x1 << " " << y1 << " " << x2 << " " << y2 << std::endl;
                //cv::line(imgColor, cv::Point(x1, y1), cv::Point(x2, y2), cv::Scalar(0, 0, 255), 2, 16);

                if (-60 <= x2 - x1 && x2 - x1 <= 60) {
                    int x_max = std::max(x1, x2);
                    //cv::Rect roi(x_max+x_offset, std::min(y1, y2), 5, std::abs(y2-y1));
                    int y = std::min(y1, y2);
                    if (y == y1)
                        x_max = x1;
                    else
                        x_max = x2;

                    double xx = getXValueAtMidY(cv::Point(x1, y1), cv::Point(x2, y2));
                    cv::Rect roi(xx + 5, (y1 + y2) / 2, std::abs(x_offset), 10);
                    if (!isROIValid(img, roi))
                        continue;
                    double mean_v = calculateMeanVV(roi, img, x1, y1, x2);
                    //if (mean_v < 30)
                        //continue;

                    //cv::line(imgColor, cv::Point(x1, y1), cv::Point(x2, y2), cv::Scalar(0, 0, 255), 2, 16);
                    std::cout << "mean_v:" << mean_v << " " << x1 << " " << y1 << " " << x2 << " " << y2 << std::endl;

                    verticalLines.push_back(line);
                    lineRight.push_back(mean_v);
                }
            }
        }
        else
        {
            std::cout << "imageLineHandleL empty " << std::endl;
            continue;
        }
        /*
        cv::namedWindow("Contours Image", cv::WINDOW_NORMAL);
        cv::imshow("Contours Image", imgColor);
        cv::waitKey(0);
        cv::destroyAllWindows();
        */
        std::cout << "imageLineHandleL 2 " << std::endl;

        std::vector<std::vector<double>> linesss;
        for (const auto& line : verticalLines) {
            double x1 = line[0];
            double y1 = line[1];
            double x2 = line[2];
            double y2 = line[3];

            double rho = (x1 + x2) / 2;

            double theta = std::atan2(y2 - y1, x2 - x1);
            linesss.push_back({ rho, theta });
        }
        
        cv::Vec4i v1;
        int pos = -1;
        double rightMean;
        for (const auto& line : verticalLines) {
            double mean_v;
            pos++;
            int x1 = line[0];
            int y1 = line[1];
            int x2 = line[2];
            int y2 = line[3];
            double min_x = std::min(x1, x2);
            double min_y = std::min(y1, y2);
            double xx = min_x - 20;
            cv::Rect roi(xx, min_y, 15, 10);
            if (!isROIValid(img, roi))
                continue;
            mean_v = calculateMeanVV(roi, img, x1, y1, x2);
            

            if (mean_v > 50)
                continue;

            if (firstLine.empty() || linesss[pos][0] < firstLine[0]) {
                std::cout << "calculateMeanVV  " << mean_v << std::endl;
                v1 = line;
                firstLine = linesss[pos];
                rightMean = lineRight[pos];
            }
        }

        std::cout << "imageLineHandleL 3 " << std::endl;
        if (firstLine.empty()) {
            std::cerr << "can not find first line" << std::endl;
            continue;
        }

        int hpos;
        for (hpos = 1; hpos < 10; hpos++)
        {
            if (img.at<uchar>(v1[1], v1[0] - hpos) < 100)
                break;
        }
        v1[0] = v1[0] - hpos + 1;
        for (hpos = 1; hpos < 10; hpos++)
        {
            if (img.at<uchar>(v1[3], v1[2] - hpos) < 100)
                break;
        }
        v1[2] = v1[2] - hpos + 1;
        pos = -1;
        std::vector<double> secondLine;
        cv::Vec4i v2;

        double first_line_y = (v1[1] + v1[3]) / 2;
        double A2, B2, C2;
        getLineCoefficients(v1[0], v1[1], v1[2], v1[3], A2, B2, C2);        
        diff = 1000;
        std::cout << "imageLineHandleLR 3_" << pos << std::endl;
        for (const auto& line : verticalLines) {
            pos++;

            std::cout << "imageLineHandleLR 3_" << pos << std::endl;
            std::cout << "linesss[pos][0] - firstLine[0]" << linesss[pos][0] - firstLine[0] << std::endl;
            std::cout << "lineRight[pos] - rightMean" << lineRight[pos] - rightMean << std::endl;

            std::cout << "lineRight[pos]" << lineRight[pos] << std::endl;

            angle = getAcuteAngle(linesss[pos][1], firstLine[1]);
            std::cout << "angle:" << angle << std::endl;

            double near_thresh = 5;
            double second_line_y = (line[1] + line[3]) / 2;
            std::cout << "first_line_y:" << first_line_y << " second_line_y:" << second_line_y << std::endl;
            double A1, B1, C1;
            getLineCoefficients(line[0], line[1], line[2], line[3], A1, B1, C1);

            double diff1 = pointToLineDistance(v1[0], v1[1], A1, B1, C1);
            std::cout << "diff1:" << diff1 << std::endl;
            if (second_line_y - first_line_y > 80 || second_line_y - first_line_y < -80)
                near_thresh = 11;
            if (diff1 < near_thresh && ((lineRight[pos] - rightMean) < 60 || (lineRight[pos] - rightMean) > -60))
                continue;

            if (angle > 2)
                continue;

            if (secondLine.empty() || diff1 < diff)
            {
                diff = diff1;
                v2 = line;
                secondLine = linesss[pos];
                std::cout << "pos:" << pos << std::endl;
            }

            continue;
        }

        std::cout << "imageLineHandleLR 4 " << std::endl;
        int x11 = v1[0];
        int y11 = v1[1];
        int x22 = v1[2];
        int y22 = v1[3];

        //cv::line(imgColor, cv::Point(x11, y11), cv::Point(x22, y22), cv::Scalar(0, 255, 0), 2, 16);
        if (secondLine.empty()) {
            diff = 0;
            angle = 0;
        }
        else
        {       
            double A, B, C;
            Point p1;
            int res = fitSuitableLine(v1, img, imgColor, A, B, C, p1, 1);
            if (res != 0)
                return imgColor;

            getLineCoefficients(v2[0], v2[1], v2[2], v2[3], A, B, C);
            //Point p3;
            //res = fitSuitableLine(v2, img, imgColor, A, B, C, p3, 2);
            //if (res != 0)
                //return imgColor;

            diff = pointToLineDistance(p1.x, p1.y, A, B, C);

            std::cout << "两条直线的距离为: " << diff << std::endl;
            cv::line(imgColor, cv::Point(v2[0], v2[1]), cv::Point(v2[2], v2[3]), cv::Scalar(0, 255, 0), 2, 16);
            //diff = secondLine[0] - firstLine[0];
            angle = getAcuteAngle(secondLine[1], firstLine[1]);
        }
        break;
    }
    std::cout << "imageLineHandleLR 5 " << std::endl;

    if (firstLine.empty()) {
        return cv::Mat();
    }

    return imgColor;
}

int fitSuitableLineR(cv::Vec4i& v, cv::Mat& img, cv::Mat& imgColor, double& A, double& B, double& C, Point& p1, int flag) {
    int min_y = std::min(v[1], v[3]);
    int max_y = std::max(v[1], v[3]);

    int min_x = std::min(v[0], v[2]);
    int max_x = std::max(v[0], v[2]);
    double mean_v = 0;

    if (flag == 1)
    {
        min_x = std::max(0, min_x - 5);
        max_x = std::min(img.cols, max_x + 10);
    }
        
    if (flag == 2)
    {
        for (int i = 1; i < 8; i++)
        {
            uchar pixel_value = img.at<uchar>(v[1], v[0] + i);
            mean_v += pixel_value;
        }
        for (int i = 1; i < 8; i++)
        {
            uchar pixel_value = img.at<uchar>(v[3], v[2] + i);
            mean_v += pixel_value;
        }
        mean_v = mean_v / 14;
        min_x = std::max(0, min_x - 5);
        max_x = std::min(max_x + 5, img.cols);
        cout << "mean_v:" << mean_v << endl;
    }
    
    vector<Point> points;

    if (flag == 1) {
        for (int y = min_y; y < max_y; ++y) {
            for (int x = max_x; x > min_x; --x) {
                uchar pixel_value = img.at<uchar>(y, x);
                if (pixel_value > 80) {
                    points.push_back(Point(x, y));
                    break; 

                }
            }
        }
    }
    
    if (flag == 2) {
        for (int y = min_y; y < max_y; ++y) {
            int low_80_flag = 0;
            for (int x = min_x; x < max_x; ++x) {
                uchar pixel_value = img.at<uchar>(y, x);

                if (pixel_value > mean_v - 50 && low_80_flag == 0) {
                    continue;
                }
                if (pixel_value < mean_v - 50)
                    low_80_flag = 1;
                if (pixel_value > mean_v - 50 && low_80_flag == 1) {
                    points.push_back(Point(x, y));
                    break; // 停止当前行的扫描
                }
            }
        }
    }

    // 检查是否有足够的点进行拟合
    if (points.size() < 2) {
        cout << "Not enough points to fit a line!" << endl;
        return -1;
    }

    // 使用fitLine函数拟合直线
    Vec4f line;
    cv::fitLine(points, line, DIST_L2, 0, 0.01, 0.01);

    // 提取直线参数
    float vx = line[0];
    float vy = line[1];
    float x0 = line[2];
    float y0 = line[3];

    if (vx == 0)
    {
        p1.x = x0;
        p1.y = img.rows / 2;
    }
    else
    {
        float m = vy / vx;
        float c = y0 - m * x0;

        p1.y = img.rows / 2;
        p1.x = (p1.y - c) / m;

    }
    // 计算直线的两个端点（用于绘制）
    Point lefty((int)(x0 - 1000 * vx), (int)(y0 - 1000 * vy));
    Point righty((int)(x0 + 1000 * vx), (int)(y0 + 1000 * vy));

    cv::circle(imgColor, Point(x0, y0), 5, cv::Scalar(0, 0, 255), -1);
    // 绘制直线
    cv::line(imgColor, lefty, righty, Scalar(0, 255, 0), 2, LINE_AA);

    getLineCoefficients(v[0], v[1], v[2], v[3], A, B, C);

    return 0;
}

cv::Mat imageLineHandleR(cv::Mat& img, double& diff, double& angle, int x_offset) {
    cv::Mat blurredImage;
    cv::GaussianBlur(img, blurredImage, cv::Size(9, 9), 1);

    //cv::Mat binaryImage;
    //cv::threshold(img, binaryImage, 180, 255, THRESH_BINARY);
    //cv::Mat binaryImage;
    //cv::threshold(img, binaryImage, 200, 255, THRESH_BINARY);

    cv::Mat edges;
    cv::Canny(blurredImage, edges, 50, 100);

    //imwrite("edges.jpg", edges);

    std::vector<cv::Vec4i> lines;
    cv::Mat imgColor;
    cv::cvtColor(img, imgColor, cv::COLOR_GRAY2BGR);

    std::vector<cv::Vec4i> verticalLines;
    std::vector<double> lineLeft;
    std::vector<double> firstLine;

    //cv::HoughLinesP(edges, lines, 1, CV_PI / 180, 100, 500, 80);
    int times = 0;
    while (times < 6) {
        times++;
        lines.clear();
        verticalLines.clear();
        lineLeft.clear();
        int ret = imageLineFind(edges, lines, 100 - times * 20, 100 - times * 10);
        if (ret != 0)
            continue;
        
        //std::cout << "@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@ times " << times << std::endl;
        if (!lines.empty()) {
            for (const auto& line : lines) {
                int x1 = line[0];
                int y1 = line[1];
                int x2 = line[2];
                int y2 = line[3];
                std::cout << x1 << " " << y1 << " " << x2 << " " << y2 << std::endl;
                //cv::line(imgColor, cv::Point(x1, y1), cv::Point(x2, y2), cv::Scalar(0, 0, 255), 2, 16);
                if (-60 <= x2 - x1 && x2 - x1 <= 60) {
                    int x_max = std::max(x1, x2);
                    //cv::Rect roi(x_max+x_offset, std::min(y1, y2), 5, std::abs(y2-y1));
                    int y = std::min(y1, y2);
                    if (y == y1)
                        x_max = x1;
                    else
                        x_max = x2;

                    double xx = getXValueAtMidY(cv::Point(x1, y1), cv::Point(x2, y2));

                    cv::Rect roi(xx - 5 - std::abs(x_offset), (y1 + y2) / 2, std::abs(x_offset), 10);

                    if (!isROIValid(img, roi))
                        continue;
                    
                    double mean_v = calculateMeanVV(roi, img, x1, y1, x2);
                    //if (mean_v < 20)
                        //continue;
                    //cv::line(imgColor, cv::Point(line[0], line[1]), cv::Point(line[2], line[3]), cv::Scalar(0, 0, 255), 2, 16);

                    verticalLines.push_back(line);
                    lineLeft.push_back(mean_v);
                }
            }
        }
        else
        {
            std::cout << "2222" << std::endl;
            continue;
        }


        std::cout << "imageLineHandleLR 2 " << std::endl;

        std::vector<std::vector<double>> linesss;
        for (const auto& line : verticalLines) {
            double x1 = line[0];
            double y1 = line[1];
            double x2 = line[2];
            double y2 = line[3];

            double rho = 0;

            rho = (x1 + x2) / 2;

            double theta = std::atan2(y2 - y1, x2 - x1);
            linesss.push_back({ rho, theta });
        }
        
        std::vector<cv::Vec4i> verticalLines_f1;
        cv::Vec4i v1;
        int pos = -1;
        double leftMean;
        for (const auto& line : verticalLines) {
            double mean_v;
            pos++;
            int x1 = line[0];
            int y1 = line[1];
            int x2 = line[2];
            int y2 = line[3];
            double min_x = (x1+x2)/2;
            double min_y = (y1+y2)/2;

            cv::Rect roi(min_x + 30, min_y, 8, 10);
            if (!isROIValid(img, roi))
                continue;
            mean_v = calculateMeanVV(roi, img, x1, y1, x2);
            std::cout << "calculateMeanV  " << mean_v << std::endl;
            if (mean_v > 80)
                continue;
            verticalLines_f1.push_back(line);

            if (firstLine.empty() || linesss[pos][0] > firstLine[0]) {
                v1 = line;
                firstLine = linesss[pos];
                leftMean = lineLeft[pos];
            }
        }
        std::cout << "imageLineHandleLR 3 " << std::endl;
        if (firstLine.empty()) {
            std::cerr << "can not find first line" << std::endl;
            continue;
        }

        int hpos;
        for (hpos = 1; hpos < 10; hpos++)
        {
            if (img.at<uchar>(v1[1], v1[0] + hpos) < 100)
                break;
        }
        v1[0] = v1[0] + hpos - 1;
        for (hpos = 1; hpos < 10; hpos++)
        {
            if (img.at<uchar>(v1[3], v1[2] + hpos) < 100)
                break;
        }
        v1[2] = v1[2] + hpos - 1;

        pos = -1;
        std::vector<double> secondLine;
        cv::Vec4i v2;

        double first_line_y = (v1[1] + v1[3]) / 2;

        double A2, B2, C2;
        getLineCoefficients(v1[0], v1[1], v1[2], v1[3], A2, B2, C2);
        diff = 1000;
         
        std::cout << "imageLineHandleLR 3_" << pos << std::endl;
        for (const auto& line : verticalLines) {
            pos++;

            std::cout << "imageLineHandleLR 3_" << pos << std::endl;
            std::cout << "linesss[pos][0] - firstLine[0]" << linesss[pos][0] - firstLine[0] << std::endl;
            std::cout << "lineRight[pos] - rightMean" << lineLeft[pos] - leftMean << std::endl;

            std::cout << "lineRight[pos]" << lineLeft[pos] << std::endl;


            angle = getAcuteAngle(linesss[pos][1], firstLine[1]);
            std::cout << "angle:" << angle << std::endl;
            double near_thresh = 5;
            double second_line_y = (line[1] + line[3]) / 2;
            std::cout << "first_line_y:" << first_line_y << " second_line_y:" << second_line_y << std::endl;
            double A1, B1, C1;
            getLineCoefficients(line[0], line[1], line[2], line[3], A1, B1, C1);

            double diff1 = pointToLineDistance(v1[0], v1[1], A1, B1, C1);
            std::cout << "diff1:" << diff1 << std::endl;
            std::cout << "first_line_y:" << first_line_y << " second_line_y:" << second_line_y<<std::endl;
            if (second_line_y - first_line_y > 80 || second_line_y - first_line_y < -80)
                near_thresh = 11;
            if (diff1 < near_thresh && ((lineLeft[pos] - leftMean) < 60 || (lineLeft[pos] - leftMean) > -60))
                continue;

            if (angle > 2)
                continue;

            if (secondLine.empty() || diff1 < diff)
            {
                diff = diff1;
                v2 = line;
                secondLine = linesss[pos];
                std::cout << "pos:" << pos << std::endl;
            }

            continue;
            if (lineLeft[pos] - leftMean > 30 || lineLeft[pos] - leftMean < -30) {
                if (firstLine[0] - linesss[pos][0] < 5 && (lineLeft[pos] - leftMean) < 60 && (lineLeft[pos] - leftMean) > -60)
                    continue;
                if (secondLine.empty() || linesss[pos][0] > secondLine[0])
                {
                    v2 = line;
                    secondLine = linesss[pos];
                }
            }
        }

        std::cout << "imageLineHandleLR 4 " << std::endl;
        int x11 = v1[0];
        int y11 = v1[1];
        int x22 = v1[2];
        int y22 = v1[3];

        //cv::line(imgColor, cv::Point(x11, y11), cv::Point(x22, y22), cv::Scalar(0, 255, 0), 2, 16);
        if (secondLine.empty()) {
            diff = 0;
            angle = 0;
        }
        else
        {
            
            double A, B, C;
            Point p1;
            int res = fitSuitableLineR(v1, img, imgColor, A, B, C, p1, 1);
            if (res != 0)
                return imgColor;

            //Point p3;
            //res = fitSuitableLineR(v2, img, imgColor, A, B, C, p3, 2);
            //if (res != 0)
                //return imgColor;
            getLineCoefficients(v2[0], v2[1], v2[2], v2[3], A, B, C);
            diff = pointToLineDistance(p1.x, p1.y, A, B, C);
            
            cv::line(imgColor, cv::Point(v2[0], v2[1]), cv::Point(v2[2], v2[3]), cv::Scalar(0, 255, 0), 2, 16);
            
            std::cout << "两条直线的距离为: " << diff << std::endl;
            angle = getAcuteAngle(secondLine[1], firstLine[1]);
        }
        break;
    }

    std::cout << "imageLineHandleLR 5 " << std::endl;
    
    if (firstLine.empty()) {
        std::cerr << "can not find first line" << std::endl;
        return cv::Mat();
    }

    return imgColor;
}