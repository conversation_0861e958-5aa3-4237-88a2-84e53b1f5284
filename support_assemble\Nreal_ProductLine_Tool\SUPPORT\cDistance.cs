﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Diagnostics;
using System.Threading;
using System.Windows.Forms;
using STIL_NET;

namespace Nreal_ProductLine_Tool.SUPPORT
{
	// Token: 0x0200000B RID: 11
	public class cDistance
	{
		// Token: 0x06000042 RID: 66 RVA: 0x00008054 File Offset: 0x00006254
		private bool SetAdaptableAcquisitionBuffer(long _iSensorFrequency, int _iAveragingValue, ref uint p_iTimeoutAcquisition, ref uint p_iBufferLength, ref uint p_iBufferNumber, ref uint p_iNumberOfPointsBeforeSignal)
		{
			uint num = (0.2 * (double)_iSensorFrequency / (double)_iAveragingValue <= 1.0) ? 1U : ((uint)(0.2 * (double)_iSensorFrequency / (double)_iAveragingValue));
			p_iBufferLength = num;
			uint num2 = 20U;
			p_iBufferNumber = ((p_iBufferNumber >= num2) ? p_iBufferNumber : num2);
			p_iNumberOfPointsBeforeSignal = ((p_iNumberOfPointsBeforeSignal == 0U) ? 0U : ((p_iBufferLength % p_iNumberOfPointsBeforeSignal == 0U) ? p_iNumberOfPointsBeforeSignal : 1U));
			uint num3 = 100U + (uint)(1000f * (float)_iAveragingValue / (float)_iSensorFrequency);
			p_iTimeoutAcquisition = ((p_iTimeoutAcquisition >= num3) ? p_iTimeoutAcquisition : num3);
			return true;
		}

		// Token: 0x06000043 RID: 67 RVA: 0x000080E4 File Offset: 0x000062E4
		public bool Init()
		{
			this.m_dll_chr = new dll_chr();
			bool flag = !this.m_dll_chr.Init();
			bool result;
			if (flag)
			{
				Logs.WriteInfo("dist : Error : DLL Init failed", true);
				result = false;
			}
			else
			{
				Logs.WriteInfo(string.Format("DLL_CHR.DLL :\t\t {0}", this.m_sensor_manager.DllChrVersion), true);
				Logs.WriteInfo(string.Format("STILSensors.DLL :\t {0}", this.m_sensor_manager.DllSensorsVersion), true);
				result = true;
			}
			return result;
		}

		// Token: 0x06000044 RID: 68 RVA: 0x00008160 File Offset: 0x00006360
		public bool Release()
		{
			bool flag = this.m_sensor != null;
			if (flag)
			{
				this.m_sensor.Release();
			}
			this.m_dll_chr.Release();
			return true;
		}

		// Token: 0x06000045 RID: 69 RVA: 0x0000819C File Offset: 0x0000639C
		public bool Open(enSensorType sensorType)
		{
			bool result = true;
			string distance_ip_addr = Configure.distance_ip_addr;
			this.m_sensor = this.m_sensor_manager.OpenEthernetconnection("ZENITH_1A", sensorType, cDistance.ip_address, null);
			bool flag = this.m_sensor != null;
			if (flag)
			{
				bool serial = cDistance.Serial;
				if (serial)
				{
					Console.WriteLine("Configure Prefered link output data in Serial [PLO0]");
					((sensorZENITH_1A)this.m_sensor).PreferedLinkOutputData = 0;
				}
				else
				{
					Console.WriteLine("Configure Prefered link output data in Ethernet [PLO1]");
					((sensorZENITH_1A)this.m_sensor).PreferedLinkOutputData = 1;
				}
				this.m_sensor.OnError += new sensor.ErrorHandler(this.OnError);
				bool flag2 = this.acqParamMeasurement.Init(this.m_sensor) == 1;
				if (flag2)
				{
					this.acqParamMeasurement.EnableBufferTransmittedData.ZENITH_1A_Thickness.Thickness = true;
					this.acqParamMeasurement.EnableBufferTransmittedData.ZENITH_1A_Thickness.Counter = true;
					this.acqParamMeasurement.EnableBufferTransmittedData.ZENITH_1A_Thickness.ComputedData = true;
					this.acqParamMeasurement.EnableEvent.EventEndBuffer = true;
					this.m_sensor.OnEventMeasurement += new sensor.OnEventMeasurementHandler(this.FuncEventMeasurement);
				}
				else
				{
					Console.WriteLine("cExample : Open : DLL Init failed");
					this.Close();
					result = false;
				}
			}
			else
			{
				Console.WriteLine("cExample : Error : Open (No sensor or bad sensor)");
				result = false;
			}
			return result;
		}

		// Token: 0x06000046 RID: 70 RVA: 0x00008300 File Offset: 0x00006500
		private void OnError(object sender, cErrorEventArgs e)
		{
			Console.WriteLine("cExample : OnError : {0} : {1} : 0x{2:X4} : {3}", new object[]
			{
				e.Exception.ErrorType,
				e.Exception.FunctionName,
				e.Exception.ErrorDetail,
				e.Exception.Detail
			});
		}

		// Token: 0x06000047 RID: 71 RVA: 0x00008364 File Offset: 0x00006564
		public bool Close()
		{
			bool result = true;
			bool flag = this.m_sensor != null;
			if (flag)
			{
				this.m_sensor.Close();
			}
			else
			{
				Console.WriteLine("cExample : Error : Close (No sensor or bad sensor)");
				result = false;
			}
			return result;
		}

		// Token: 0x06000048 RID: 72 RVA: 0x000083A8 File Offset: 0x000065A8
		public bool StartAcquisition()
		{
			bool result = true;
			bool flag = this.m_sensor != null;
			if (flag)
			{
				this.m_measurement_event.Reset();
				this.m_exit_event.Reset();
				this.m_exit_event_do.Reset();
				Stopwatch stopwatch = new Stopwatch();
				stopwatch.Start();
				this.sError = this.m_sensor.StartAcquisition_Measurement(this.acqParamMeasurement);
				bool flag2 = this.sError != 1;
				if (flag2)
				{
					Logs.WriteInfo(string.Format("Error : StartAcquisition : {0}", this.sError.ToString()), true);
					MessageBox.Show("位移传感器出错，请重启程序");
					Process.GetCurrentProcess().Kill();
				}
				stopwatch.Stop();
				Logs.WriteInfo(string.Format("StartAcquisition_Measurement elapsed time {0}", stopwatch.ElapsedMilliseconds.ToString()), true);
			}
			else
			{
				Logs.WriteInfo("Error : StartAcquisition (No sensor or bad sensor)", true);
				MessageBox.Show("位移传感器出错，请重启程序");
				Process.GetCurrentProcess().Kill();
				result = false;
			}
			return result;
		}

		// Token: 0x06000049 RID: 73 RVA: 0x000084B4 File Offset: 0x000066B4
		public bool StopAcquisition()
		{
			bool result = true;
			bool flag = this.m_sensor != null;
			if (flag)
			{
				this.m_sensor.StopAcquisition_Measurement();
				this.WaitEndedExecute();
			}
			else
			{
				Console.WriteLine("cExample : Error : StopAcquisition (No sensor or bad sensor)");
				result = false;
			}
			return result;
		}

		// Token: 0x0600004A RID: 74 RVA: 0x000084FC File Offset: 0x000066FC
		public bool SetParameter()
		{
			bool result = true;
			StringEnum stringEnum = new StringEnum(typeof(enFixedScanRates_ZENITH_1A));
			string stringValue = StringEnum.GetStringValue(cDistance.ScanRates);
			int num = int.Parse(stringValue);
			IList listValues = stringEnum.GetListValues();
			List<string> list = new List<string>();
			foreach (object obj in listValues)
			{
				list.Add((string)((DictionaryEntry)obj).Value);
			}
			enFixedScanRates enFixedScanRates = list.IndexOf(this.m_sensor.MinDarkFrequency.ToString());
			Console.WriteLine("Set acquisition frequency : {0}", this.flipFlop ? cDistance.ScanRates : enFixedScanRates);
			this.m_sensor.ScanRate = (this.flipFlop ? cDistance.ScanRates : enFixedScanRates);
			this.flipFlop = !this.flipFlop;
			this.m_sensor.Averaging = cDistance.Average;
			string text = this.m_sensor.SendCommand((this.m_sensor.ConnectionType == 1) ? "GUL2" : "GUL3");
			this.SetAdaptableAcquisitionBuffer((long)this.m_sensor.FreeFrequency, this.m_sensor.Averaging, ref this.acqParamMeasurement.Timeout, ref this.acqParamMeasurement.BufferLength, ref this.acqParamMeasurement.NumberOfBuffers, ref this.acqParamMeasurement.NumberOfPointsBeforeSignal);
			return result;
		}

		// Token: 0x0600004B RID: 75 RVA: 0x00008698 File Offset: 0x00006898
		public bool showMenu()
		{
			Console.WriteLine("\n================= Menu ================= ");
			Console.WriteLine("======================================== ");
			Console.WriteLine("'C' : Switch frequency");
			Console.WriteLine("**********");
			Console.WriteLine("'Q' : Quit");
			return true;
		}

		// Token: 0x0600004C RID: 76 RVA: 0x000086E4 File Offset: 0x000068E4
		public void Monitor()
		{
			ccountercheck ccountercheck = new ccountercheck();
			Logs.WriteInfo("Monitor thread start", true);
			for (;;)
			{
				this.m_exit_event_do.Wait(-1);
				Logs.WriteInfo("m_exit_event_do event set", true);
				bool flag = !this.m_exit_event.Wait(0);
				if (flag)
				{
					Logs.WriteInfo("m_exit_event event not set", true);
					bool flag2 = this.m_sensor != null;
					if (flag2)
					{
						this.m_sensor.StopAcquisition_Measurement();
						Thread.Sleep(100);
					}
				}
				this.Close();
				Thread.Sleep(200);
				bool flag3 = !this.Open(19);
				if (flag3)
				{
					Logs.WriteInfo("open error", true);
					MessageBox.Show("位移传感器出错，重新打开失败，请重启程序");
					Process.GetCurrentProcess().Kill();
				}
				this.SetParameter();
				this.StartAcquisition();
				Thread thread = new Thread(new ThreadStart(this.Execute));
				thread.Start();
			}
		}

		// Token: 0x0600004D RID: 77 RVA: 0x000087F4 File Offset: 0x000069F4
		public void Execute()
		{
			float[] array = null;
			float[] array2 = null;
			float[] array3 = null;
			float[] array4 = null;
			uint[] array5 = null;
			uint num = 0U;
			ccountercheck ccountercheck = new ccountercheck();
			int num2 = 0;
			long num3 = 0L;
			Logs.WriteInfo("Execute", true);
			while (!this.m_exit_event.Wait(10))
			{
				bool flag = this.m_measurement_event.Wait(10);
				if (flag)
				{
					this.sError = this.m_sensor.GetTransmittedData_ZENITH_1A_Thickness(ref array3, ref array2, ref array2, ref array2, ref array2, ref array2, ref array2, ref array2, ref array5, ref array2, ref array, ref array5, ref array5, ref array5, ref array5, ref array5, ref array4, ref num);
					bool flag2 = this.sError == 1;
					if (flag2)
					{
						for (uint num4 = 0U; num4 < num; num4 += 1U)
						{
							bool flag3 = ccountercheck.checkcounter(array[(int)num4]);
							if (flag3)
							{
								Console.WriteLine(string.Format("\ncExample_[jp_nb {0}, jp_sz {1,6:F0}, prev {2,5:F0}, next {3,5:F0}, lp_cnt {4:F0} mes_cnt {5:D11})", new object[]
								{
									ccountercheck.getNbJump(),
									ccountercheck.getstepcounter(),
									ccountercheck.getprevcounter(),
									ccountercheck.getnextcounter(),
									ccountercheck.getloopcounter(),
									ccountercheck.getmeasure_count()
								}));
							}
							GlobalData.distance[0] = (double)array3[(int)num4];
							num3 += 1L;
							bool flag4 = num3 % 100L == 0L;
							if (flag4)
							{
								Logs.WriteInfo(string.Concat(new string[]
								{
									"Thickness:",
									GlobalData.distance[0].ToString(),
									" peak1:",
									GlobalData.distance[1].ToString(),
									" peak2:",
									GlobalData.distance[2].ToString(),
									" len:",
									num.ToString()
								}), true);
								double num5 = (GlobalData.colimatorOrigDataP13[0] + GlobalData.colimatorOrigDataP13[2]) / 2.0;
								double num6 = (GlobalData.colimatorOrigDataP13[1] + GlobalData.colimatorOrigDataP13[3]) / 2.0;
								Logs.WriteInfo(string.Concat(new string[]
								{
									"点13均值x:",
									num5.ToString(),
									", 点13均值y:",
									num6.ToString(),
									", 点2x:",
									GlobalData.colimatorOrigDataP2[0].ToString(),
									", 点2y:",
									GlobalData.colimatorOrigDataP2[1].ToString()
								}), true);
							}
						}
						num2 = 0;
					}
					else
					{
						num2++;
						Logs.WriteInfo("m_measurement_event error", true);
						bool flag5 = num2 > 5;
						if (flag5)
						{
							break;
						}
					}
				}
			}
			Console.WriteLine();
			Logs.WriteInfo("Execute end", true);
			this.m_exit_event_do.Set();
		}

		// Token: 0x0600004E RID: 78 RVA: 0x00008AD8 File Offset: 0x00006CD8
		public bool IsEndedExecute()
		{
			return this.m_exit_event_do.Wait(10);
		}

		// Token: 0x0600004F RID: 79 RVA: 0x00008AF8 File Offset: 0x00006CF8
		public bool WaitEndedExecute()
		{
			while (!this.IsEndedExecute())
			{
			}
			return true;
		}

		// Token: 0x06000050 RID: 80 RVA: 0x00008B1C File Offset: 0x00006D1C
		public bool EndExecute()
		{
			this.m_exit_event.Set();
			return this.WaitEndedExecute();
		}

		// Token: 0x06000051 RID: 81 RVA: 0x00008B40 File Offset: 0x00006D40
		public void FuncEventMeasurement(sensor.enSensorAcquisitionEvent ev)
		{
			Console.WriteLine(string.Format(" Event : {0}", ev.ToString()));
			switch (ev)
			{
			case 0:
				this.m_measurement_event.Set();
				break;
			case 1:
				this.m_measurement_event.Set();
				break;
			case 2:
				this.m_measurement_event.Set();
				Console.WriteLine();
				break;
			case 3:
				this.m_exit_event.Set();
				break;
			case 5:
				this.m_measurement_event.Set();
				Console.WriteLine();
				break;
			}
		}

		// Token: 0x04000073 RID: 115
		public const float SEC_TO_MILLISEC = 1000f;

		// Token: 0x04000074 RID: 116
		public const float TIMEOUT_ACQUISITION_CONST = 100f;

		// Token: 0x04000075 RID: 117
		public const double WINDOWS_CONTEXT_COMMUTATION_TIME = 0.2;

		// Token: 0x04000076 RID: 118
		public const double ACQUISITION_DEPTH_TIME = 4.0;

		// Token: 0x04000077 RID: 119
		private static enFixedScanRates ScanRates = 4;

		// Token: 0x04000078 RID: 120
		private static int Average = 1;

		// Token: 0x04000079 RID: 121
		private static string ip_address = "**************";

		// Token: 0x0400007A RID: 122
		private static bool Serial = false;

		// Token: 0x0400007B RID: 123
		private static ushort ComPortId = 0;

		// Token: 0x0400007C RID: 124
		private static uint BaudRate = 0U;

		// Token: 0x0400007D RID: 125
		private dll_chr m_dll_chr = null;

		// Token: 0x0400007E RID: 126
		private sensor m_sensor = null;

		// Token: 0x0400007F RID: 127
		private cAcqParamMeasurement acqParamMeasurement = new cAcqParamMeasurement();

		// Token: 0x04000080 RID: 128
		private enSensorError sError = 1;

		// Token: 0x04000081 RID: 129
		private sensorManager m_sensor_manager = new sensorManager();

		// Token: 0x04000082 RID: 130
		private cNamedEvent m_measurement_event = new cNamedEvent();

		// Token: 0x04000083 RID: 131
		private cNamedEvent m_exit_event = new cNamedEvent();

		// Token: 0x04000084 RID: 132
		private cNamedEvent m_exit_event_do = new cNamedEvent(true);

		// Token: 0x04000085 RID: 133
		private bool flipFlop = false;
	}
}
