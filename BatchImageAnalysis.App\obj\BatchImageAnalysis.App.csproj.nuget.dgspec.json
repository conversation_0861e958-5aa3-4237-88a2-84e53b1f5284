{"format": 1, "restore": {"D:\\csharp\\support_analyze\\BatchImageAnalysis.App\\BatchImageAnalysis.App.csproj": {}}, "projects": {"D:\\csharp\\support_analyze\\BatchImageAnalysis.App\\BatchImageAnalysis.App.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\csharp\\support_analyze\\BatchImageAnalysis.App\\BatchImageAnalysis.App.csproj", "projectName": "BatchImageAnalysis.App", "projectPath": "D:\\csharp\\support_analyze\\BatchImageAnalysis.App\\BatchImageAnalysis.App.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\csharp\\support_analyze\\BatchImageAnalysis.App\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"D:\\csharp\\support_analyze\\BatchImageAnalysis.Core\\BatchImageAnalysis.Core.csproj": {"projectPath": "D:\\csharp\\support_analyze\\BatchImageAnalysis.Core\\BatchImageAnalysis.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Microsoft.Extensions.Hosting": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.Extensions.Logging.Console": {"target": "Package", "version": "[9.0.6, )"}, "System.CommandLine": {"target": "Package", "version": "[2.0.0-beta4.22272.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}}}, "D:\\csharp\\support_analyze\\BatchImageAnalysis.Core\\BatchImageAnalysis.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\csharp\\support_analyze\\BatchImageAnalysis.Core\\BatchImageAnalysis.Core.csproj", "projectName": "BatchImageAnalysis.Core", "projectPath": "D:\\csharp\\support_analyze\\BatchImageAnalysis.Core\\BatchImageAnalysis.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\csharp\\support_analyze\\BatchImageAnalysis.Core\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"D:\\csharp\\support_analyze\\BatchImageAnalysis.Interop\\BatchImageAnalysis.Interop.csproj": {"projectPath": "D:\\csharp\\support_analyze\\BatchImageAnalysis.Interop\\BatchImageAnalysis.Interop.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"ini-parser-netstandard": {"target": "Package", "version": "[2.5.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}}}, "D:\\csharp\\support_analyze\\BatchImageAnalysis.Interop\\BatchImageAnalysis.Interop.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\csharp\\support_analyze\\BatchImageAnalysis.Interop\\BatchImageAnalysis.Interop.csproj", "projectName": "BatchImageAnalysis.Interop", "projectPath": "D:\\csharp\\support_analyze\\BatchImageAnalysis.Interop\\BatchImageAnalysis.Interop.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\csharp\\support_analyze\\BatchImageAnalysis.Interop\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"SixLabors.ImageSharp": {"target": "Package", "version": "[3.1.10, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}}}}}