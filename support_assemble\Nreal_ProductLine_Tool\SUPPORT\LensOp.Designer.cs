﻿namespace Nreal_ProductLine_Tool.SUPPORT
{
	// Token: 0x02000018 RID: 24
	public partial class LensOp : global::Nreal_ProductLine_Tool.SUPPORT.FormDefault
	{
		// Token: 0x060000DB RID: 219 RVA: 0x00012A70 File Offset: 0x00010C70
		protected override void Dispose(bool disposing)
		{
			bool flag = disposing && this.components != null;
			if (flag)
			{
				this.components.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x060000DC RID: 220 RVA: 0x00012AA8 File Offset: 0x00010CA8
		private void InitializeComponent()
		{
			this.groupBox1 = new global::System.Windows.Forms.GroupBox();
			this.Z_press_btn = new global::System.Windows.Forms.Button();
			this.groupBox2 = new global::System.Windows.Forms.GroupBox();
			this.last_step_btn = new global::System.Windows.Forms.Button();
			this.point2_y_label = new global::System.Windows.Forms.Label();
			this.label2 = new global::System.Windows.Forms.Label();
			this.point2_x_label = new global::System.Windows.Forms.Label();
			this.label1 = new global::System.Windows.Forms.Label();
			this.groupBox3 = new global::System.Windows.Forms.GroupBox();
			this.move_x_btn = new global::System.Windows.Forms.Button();
			this.Link_finish_btn = new global::System.Windows.Forms.Button();
			this.groupBox1.SuspendLayout();
			this.groupBox2.SuspendLayout();
			this.groupBox3.SuspendLayout();
			base.SuspendLayout();
			this.groupBox1.Controls.Add(this.Z_press_btn);
			this.groupBox1.Location = new global::System.Drawing.Point(37, 24);
			this.groupBox1.Name = "groupBox1";
			this.groupBox1.Size = new global::System.Drawing.Size(969, 117);
			this.groupBox1.TabIndex = 0;
			this.groupBox1.TabStop = false;
			this.groupBox1.Text = "压下压杆";
			this.Z_press_btn.Location = new global::System.Drawing.Point(353, 33);
			this.Z_press_btn.Name = "Z_press_btn";
			this.Z_press_btn.Size = new global::System.Drawing.Size(235, 50);
			this.Z_press_btn.TabIndex = 0;
			this.Z_press_btn.Text = "Z轴下压";
			this.Z_press_btn.UseVisualStyleBackColor = true;
			this.Z_press_btn.Click += new global::System.EventHandler(this.Z_press_btn_Click);
			this.groupBox2.Controls.Add(this.last_step_btn);
			this.groupBox2.Controls.Add(this.point2_y_label);
			this.groupBox2.Controls.Add(this.label2);
			this.groupBox2.Controls.Add(this.point2_x_label);
			this.groupBox2.Controls.Add(this.label1);
			this.groupBox2.Location = new global::System.Drawing.Point(37, 186);
			this.groupBox2.Name = "groupBox2";
			this.groupBox2.Size = new global::System.Drawing.Size(969, 272);
			this.groupBox2.TabIndex = 1;
			this.groupBox2.TabStop = false;
			this.groupBox2.Text = "透镜准直";
			this.last_step_btn.Location = new global::System.Drawing.Point(362, 147);
			this.last_step_btn.Name = "last_step_btn";
			this.last_step_btn.Size = new global::System.Drawing.Size(217, 48);
			this.last_step_btn.TabIndex = 4;
			this.last_step_btn.Text = "跳转补偿镜准直页面";
			this.last_step_btn.UseVisualStyleBackColor = true;
			this.last_step_btn.Click += new global::System.EventHandler(this.last_step_btn_Click);
			this.point2_y_label.AutoSize = true;
			this.point2_y_label.Location = new global::System.Drawing.Point(133, 158);
			this.point2_y_label.Name = "point2_y_label";
			this.point2_y_label.Size = new global::System.Drawing.Size(73, 27);
			this.point2_y_label.TabIndex = 3;
			this.point2_y_label.Text = "label2";
			this.label2.AutoSize = true;
			this.label2.Location = new global::System.Drawing.Point(52, 158);
			this.label2.Name = "label2";
			this.label2.Size = new global::System.Drawing.Size(25, 27);
			this.label2.TabIndex = 2;
			this.label2.Text = "Y";
			this.point2_x_label.AutoSize = true;
			this.point2_x_label.Location = new global::System.Drawing.Point(133, 72);
			this.point2_x_label.Name = "point2_x_label";
			this.point2_x_label.Size = new global::System.Drawing.Size(73, 27);
			this.point2_x_label.TabIndex = 1;
			this.point2_x_label.Text = "label2";
			this.label1.AutoSize = true;
			this.label1.Location = new global::System.Drawing.Point(52, 72);
			this.label1.Name = "label1";
			this.label1.Size = new global::System.Drawing.Size(26, 27);
			this.label1.TabIndex = 0;
			this.label1.Text = "X";
			this.groupBox3.Controls.Add(this.move_x_btn);
			this.groupBox3.Location = new global::System.Drawing.Point(37, 507);
			this.groupBox3.Name = "groupBox3";
			this.groupBox3.Size = new global::System.Drawing.Size(969, 117);
			this.groupBox3.TabIndex = 1;
			this.groupBox3.TabStop = false;
			this.groupBox3.Text = "电机控制";
			this.move_x_btn.Location = new global::System.Drawing.Point(353, 33);
			this.move_x_btn.Name = "move_x_btn";
			this.move_x_btn.Size = new global::System.Drawing.Size(235, 50);
			this.move_x_btn.TabIndex = 0;
			this.move_x_btn.Text = "X轴移动";
			this.move_x_btn.UseVisualStyleBackColor = true;
			this.move_x_btn.Click += new global::System.EventHandler(this.move_x_btn_Click);
			this.Link_finish_btn.Location = new global::System.Drawing.Point(890, 678);
			this.Link_finish_btn.Name = "Link_finish_btn";
			this.Link_finish_btn.Size = new global::System.Drawing.Size(116, 51);
			this.Link_finish_btn.TabIndex = 1;
			this.Link_finish_btn.Text = "结束";
			this.Link_finish_btn.UseVisualStyleBackColor = true;
			this.Link_finish_btn.Click += new global::System.EventHandler(this.Link_finish_btn_Click);
			base.AutoScaleDimensions = new global::System.Drawing.SizeF(13f, 27f);
			base.AutoScaleMode = global::System.Windows.Forms.AutoScaleMode.Font;
			base.ClientSize = new global::System.Drawing.Size(1167, 777);
			base.Controls.Add(this.Link_finish_btn);
			base.Controls.Add(this.groupBox3);
			base.Controls.Add(this.groupBox2);
			base.Controls.Add(this.groupBox1);
			base.Name = "LensOp";
			this.Text = "Form1";
			this.groupBox1.ResumeLayout(false);
			this.groupBox2.ResumeLayout(false);
			this.groupBox2.PerformLayout();
			this.groupBox3.ResumeLayout(false);
			base.ResumeLayout(false);
		}

		// Token: 0x04000249 RID: 585
		private global::System.ComponentModel.IContainer components = null;

		// Token: 0x0400024A RID: 586
		private global::System.Windows.Forms.GroupBox groupBox1;

		// Token: 0x0400024B RID: 587
		private global::System.Windows.Forms.Button Z_press_btn;

		// Token: 0x0400024C RID: 588
		private global::System.Windows.Forms.GroupBox groupBox2;

		// Token: 0x0400024D RID: 589
		private global::System.Windows.Forms.Label label1;

		// Token: 0x0400024E RID: 590
		private global::System.Windows.Forms.Label point2_x_label;

		// Token: 0x0400024F RID: 591
		private global::System.Windows.Forms.Label label2;

		// Token: 0x04000250 RID: 592
		private global::System.Windows.Forms.Label point2_y_label;

		// Token: 0x04000251 RID: 593
		private global::System.Windows.Forms.Button last_step_btn;

		// Token: 0x04000252 RID: 594
		private global::System.Windows.Forms.GroupBox groupBox3;

		// Token: 0x04000253 RID: 595
		private global::System.Windows.Forms.Button move_x_btn;

		// Token: 0x04000254 RID: 596
		private global::System.Windows.Forms.Button Link_finish_btn;
	}
}
