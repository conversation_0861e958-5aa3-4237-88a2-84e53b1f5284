﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.Threading;
using System.Windows.Forms;

namespace Nreal_ProductLine_Tool.SUPPORT
{
	// Token: 0x02000017 RID: 23
	public partial class MotionZ : FormDefault
	{
		// Token: 0x060000C7 RID: 199 RVA: 0x000121C8 File Offset: 0x000103C8
		public MotionZ(Entrance entrance, int sequence, string title)
		{
			this.sequence = sequence;
			this.entrance = entrance;
			this.title = title;
			this.InitializeComponent();
			base.TopLevel = false;
			base.FormBorderStyle = FormBorderStyle.None;
			this.Dock = DockStyle.Fill;
			MotionZ.ax_operate_sem = new SemaphoreSlim(0, 1);
			Thread thread = new Thread(new ThreadStart(this.ax_operate));
			thread.Start();
		}

		// Token: 0x060000C8 RID: 200 RVA: 0x00012268 File Offset: 0x00010468
		public override string GetName()
		{
			return this.title;
		}

		// Token: 0x060000C9 RID: 201 RVA: 0x00012280 File Offset: 0x00010480
		public override void Start()
		{
			MotionZ.pulse = Configure.zDest1;
			this.motion_z_label.Text = "电机Z轴开始下压 " + MotionZ.pulse.ToString();
			base.Show();
			MotionZ.ax_operate_sem.Release();
		}

		// Token: 0x060000CA RID: 202 RVA: 0x000122BF File Offset: 0x000104BF
		public override void ExcepStop()
		{
			GSC_Controller.Stop("2");
			Thread.Sleep(200);
			MotionZ.pulse = 0;
			MotionZ.ax_operate_sem.Release();
		}

		// Token: 0x060000CB RID: 203 RVA: 0x000122E9 File Offset: 0x000104E9
		private void button1_Click(object sender, EventArgs e)
		{
			this.entrance.Finish(this.sequence, 0);
		}

		// Token: 0x060000CC RID: 204 RVA: 0x00012300 File Offset: 0x00010500
		private void ax_operate()
		{
			while (!this.isExited)
			{
				Logs.WriteInfo("ax_operate", true);
				MotionZ.ax_operate_sem.Wait();
				int i;
				for (i = 0; i < 10; i++)
				{
					bool flag = !MotionController.running;
					if (flag)
					{
						break;
					}
					Thread.Sleep(200);
				}
				bool flag2 = i == 10;
				if (flag2)
				{
					MessageBox.Show("当前电机繁忙，请重新开始");
				}
				else
				{
					Logs.WriteInfo("mottionz:" + MotionZ.pulse.ToString(), true);
					MotionController.running = true;
					bool flag3 = MotionZ.pulse != 0;
					if (flag3)
					{
						int num = 0;
						int speed = GSC_Controller.GetSpeed(ref num);
						bool flag4 = speed != 0;
						if (flag4)
						{
							speed = GSC_Controller.GetSpeed(ref num);
							bool flag5 = speed != 0;
							if (flag5)
							{
								MessageBox.Show("电机速度错误，请重启");
								continue;
							}
						}
						bool flag6 = num != Configure.zSpeedHigh;
						if (flag6)
						{
							MotionController.SetZSpeedHigh();
						}
						GSC_Controller.GetSpeed(ref num);
						GSC_Controller.MoveAbs(MotionZ.ax_type, MotionZ.pulse);
					}
					else
					{
						GSC_Controller.Home("w", "-+");
					}
					MotionController.running = false;
					Logs.WriteInfo("mottionz end:" + MotionZ.pulse.ToString(), true);
					bool flag7 = MotionZ.pulse != 0;
					if (flag7)
					{
						base.Invoke(new MethodInvoker(delegate()
						{
							this.entrance.Finish(this.sequence, 0);
						}));
					}
				}
			}
		}

		// Token: 0x0400022E RID: 558
		private Color fail = Color.OrangeRed;

		// Token: 0x0400022F RID: 559
		private Color pass = Color.Green;

		// Token: 0x04000230 RID: 560
		private Color gray = Color.Gray;

		// Token: 0x04000231 RID: 561
		private Entrance entrance;

		// Token: 0x04000232 RID: 562
		private int sequence;

		// Token: 0x04000233 RID: 563
		private int type;

		// Token: 0x04000234 RID: 564
		public string title;

		// Token: 0x04000235 RID: 565
		public static SemaphoreSlim ax_operate_sem;

		// Token: 0x04000236 RID: 566
		private bool isExited = false;

		// Token: 0x04000237 RID: 567
		public static string ax_type = "2";

		// Token: 0x04000238 RID: 568
		public static int pulse;
	}
}
