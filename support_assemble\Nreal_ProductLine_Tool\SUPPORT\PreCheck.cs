﻿using System;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Reflection;
using System.Runtime.InteropServices;
using System.Threading;
using System.Windows.Forms;

namespace Nreal_ProductLine_Tool.SUPPORT
{
	// Token: 0x02000016 RID: 22
	public partial class PreCheck : FormDefault
	{
		// Token: 0x060000BB RID: 187 RVA: 0x00010A38 File Offset: 0x0000EC38
		public PreCheck(Entrance entrance, int sequence, string title)
		{
			this.sequence = sequence;
			this.entrance = entrance;
			this.title = title;
			base.FormBorderStyle = FormBorderStyle.FixedDialog;
			base.FormClosed += this.FrmMain_FormClosed;
			Control.CheckForIllegalCrossThreadCalls = false;
			this.InitializeComponent();
			this.Text = string.Concat(new string[]
			{
				LOGINFO.rname,
				" ",
				LOGINFO.pname,
				" Version:",
				Assembly.GetExecutingAssembly().GetName().Version.ToString(),
				" 线号：",
				LOGINFO.lname
			});
			base.Load += this.MainForm_Load;
			this.Init_support_precheck();
			base.TopLevel = false;
			base.FormBorderStyle = FormBorderStyle.None;
			this.Dock = DockStyle.Fill;
		}

		// Token: 0x060000BC RID: 188 RVA: 0x00010B54 File Offset: 0x0000ED54
		private void Init_support_precheck()
		{
			CameraImageLib.Init_support_preCheck(new int[]
			{
				Configure.preCheck_LT_roi_x,
				Configure.preCheck_LT_roi_y,
				Configure.preCheck_LT_roi_width,
				Configure.preCheck_LT_roi_height,
				Configure.preCheck_RT_roi_x,
				Configure.preCheck_RT_roi_y,
				Configure.preCheck_RT_roi_width,
				Configure.preCheck_RT_roi_height,
				Configure.preCheck_LB_roi_x,
				Configure.preCheck_LB_roi_y,
				Configure.preCheck_LB_roi_width,
				Configure.preCheck_LB_roi_height,
				Configure.preCheck_RB_roi_x,
				Configure.preCheck_RB_roi_y,
				Configure.preCheck_RB_roi_width,
				Configure.preCheck_RB_roi_height
			}, 16);
		}

		// Token: 0x060000BD RID: 189 RVA: 0x00010BFC File Offset: 0x0000EDFC
		private void MainForm_Load(object sender, EventArgs e)
		{
			base.WindowState = FormWindowState.Maximized;
			this.groupBox1.Anchor = (AnchorStyles.Top | AnchorStyles.Right);
			this.groupBox1.Location = new Point(base.ClientSize.Width - this.groupBox1.Width - 20, 20);
			this.pictureBox1.Location = new Point(20, 20);
			this.pictureBox1.Size = new Size(base.ClientSize.Width - this.groupBox1.Width - 60, base.ClientSize.Height - 60);
		}

		// Token: 0x060000BE RID: 190 RVA: 0x00010CA4 File Offset: 0x0000EEA4
		private void FrmMain_FormClosed(object sender, FormClosedEventArgs e)
		{
			Process.GetCurrentProcess().Kill();
		}

		// Token: 0x060000BF RID: 191 RVA: 0x00010CB4 File Offset: 0x0000EEB4
		public void imgTransferCallback(IntPtr data, int count)
		{
			TimeSpan timeSpan = new TimeSpan(DateTime.Now.Ticks);
			byte[] array = new byte[count];
			Marshal.Copy(data, array, 0, count);
			MemoryStream stream = new MemoryStream(array);
			Logs.WriteInfo("imgTransferCallback", true);
			Image bitMap = Image.FromStream(stream);
			this.pictureBox1.BeginInvoke(new MethodInvoker(delegate()
			{
				this.pictureBox1.Image = bitMap;
			}));
		}

		// Token: 0x060000C0 RID: 192 RVA: 0x00010D2C File Offset: 0x0000EF2C
		private void button15_Click(object sender, EventArgs e)
		{
			TimeSpan t = new TimeSpan(DateTime.Now.Ticks);
			int ret = CameraImageLib.AnalysisSupportPreCheck(1, this.para);
			TimeSpan t2 = new TimeSpan(DateTime.Now.Ticks);
			Console.WriteLine(string.Format("ana程序执行时间：{0} 毫秒", (t2 - t).TotalMilliseconds));
			Logs.WriteInfo("分析结果:" + ret.ToString(), true);
			base.Invoke(new MethodInvoker(delegate()
			{
				bool flag = ret == 0;
				if (flag)
				{
					this.analysis_result_label.Text = "分析成功";
					this.analysis_result_label.BackColor = Color.Gray;
				}
				else
				{
					this.analysis_result_label.Text = "分析失败";
					this.analysis_result_label.BackColor = Color.Red;
				}
				this.center_x_label.Text = this.para[0].ToString("F3");
				this.center_y_label.Text = this.para[1].ToString("F3");
				this.center_x_diff.Text = this.para[2].ToString("F3");
				this.center_y_diff.Text = this.para[3].ToString("F3");
				bool flag2 = Math.Abs(this.para[2]) < (double)Configure.preCheck_x_offset_max;
				if (flag2)
				{
					this.center_x_diff.BackColor = Color.Green;
				}
				else
				{
					this.center_x_diff.BackColor = Color.Red;
				}
				bool flag3 = Math.Abs(this.para[3]) < (double)Configure.preCheck_y_offset_max;
				if (flag3)
				{
					this.center_y_diff.BackColor = Color.Green;
				}
				else
				{
					this.center_y_diff.BackColor = Color.Red;
				}
			}));
		}

		// Token: 0x060000C1 RID: 193 RVA: 0x00010DD8 File Offset: 0x0000EFD8
		private void button1_Click(object sender, EventArgs e)
		{
			this.button1.Enabled = false;
			MotionController.running = true;
			GSC_Controller.MoveAbs("1", Configure.yDest1);
			MotionController.running = false;
			Thread.Sleep(100);
			int ret = CameraImageLib.Capture();
			bool flag = ret == 0;
			if (flag)
			{
				ret = CameraImageLib.AnalysisSupportPreCheck(1, this.para);
				base.Invoke(new MethodInvoker(delegate()
				{
					bool flag2 = ret == 0;
					if (flag2)
					{
						this.analysis_result_label.Text = "分析成功";
						this.analysis_result_label.BackColor = Color.Gray;
					}
					else
					{
						this.analysis_result_label.Text = "分析失败";
						this.analysis_result_label.BackColor = Color.Red;
					}
					this.center_x_label.Text = this.para[0].ToString("F3");
					this.center_y_label.Text = this.para[1].ToString("F3");
					this.center_x_diff.Text = this.para[2].ToString("F3");
					this.center_y_diff.Text = this.para[3].ToString("F3");
					bool flag3 = Math.Abs(this.para[2]) < (double)Configure.preCheck_x_offset_max;
					if (flag3)
					{
						this.center_x_diff.BackColor = Color.Green;
					}
					else
					{
						this.center_x_diff.BackColor = Color.Red;
					}
					bool flag4 = Math.Abs(this.para[3]) < (double)Configure.preCheck_y_offset_max;
					if (flag4)
					{
						this.center_y_diff.BackColor = Color.Green;
					}
					else
					{
						this.center_y_diff.BackColor = Color.Red;
					}
				}));
			}
			this.button1.Enabled = true;
		}

		// Token: 0x060000C2 RID: 194 RVA: 0x00010E74 File Offset: 0x0000F074
		public override void Start()
		{
			this.imgTransfer = new PreCheck.ImgTransferCallback(this.imgTransferCallback);
			CameraImageLib.RegisterCallBackShowImage(this.imgTransfer);
			Colimator.serialPort = Colimator.serialPort1;
			Colimator.TurnOnColimator();
			Colimator.serialPort = Colimator.serialPort2;
			Colimator.TurnOnColimator();
			base.Show();
			this.c1_x_label.Text = "";
			this.c1_y_label.Text = "";
			this.c1_d_label.Text = "";
			this.c2_x_label.Text = "";
			this.c2_y_label.Text = "";
			this.c2_d_label.Text = "";
			this.c1_x_label.BackColor = this.gray;
			this.c1_y_label.BackColor = this.gray;
			this.c1_d_label.BackColor = this.gray;
			this.c2_x_label.BackColor = this.gray;
			this.c2_y_label.BackColor = this.gray;
			this.c2_d_label.BackColor = this.gray;
		}

		// Token: 0x060000C3 RID: 195 RVA: 0x00010F9C File Offset: 0x0000F19C
		private void button2_Click(object sender, EventArgs e)
		{
			this.c1_x_label.Text = GlobalData.colimatorOrigDataP13[0].ToString("F3");
			this.c1_y_label.Text = GlobalData.colimatorOrigDataP13[1].ToString("F3");
			this.c1_d_label.Text = GlobalData.p1_d.ToString("F3");
			this.c2_x_label.Text = GlobalData.colimatorOrigDataP2[0].ToString("F3");
			this.c2_y_label.Text = GlobalData.colimatorOrigDataP2[1].ToString("F3");
			this.c2_d_label.Text = GlobalData.p2_d.ToString("F3");
			bool flag = GlobalData.colimatorOrigDataP13[0] < Configure.preCheck_x_point1_min || GlobalData.colimatorOrigDataP13[0] > Configure.preCheck_x_point1_max;
			if (flag)
			{
				this.c1_x_label.BackColor = this.fail;
			}
			else
			{
				this.c1_x_label.BackColor = this.pass;
			}
			bool flag2 = GlobalData.colimatorOrigDataP13[1] < Configure.preCheck_y_point1_min || GlobalData.colimatorOrigDataP13[1] > Configure.preCheck_y_point1_max;
			if (flag2)
			{
				this.c1_y_label.BackColor = this.fail;
			}
			else
			{
				this.c1_y_label.BackColor = this.pass;
			}
			bool flag3 = GlobalData.p1_d < Configure.preCheck_d_point1_min || GlobalData.p1_d > Configure.preCheck_d_point1_max;
			if (flag3)
			{
				this.c1_d_label.BackColor = this.fail;
			}
			else
			{
				this.c1_d_label.BackColor = this.pass;
			}
			bool flag4 = GlobalData.colimatorOrigDataP2[0] < Configure.preCheck_x_point2_min || GlobalData.colimatorOrigDataP2[0] > Configure.preCheck_x_point2_max;
			if (flag4)
			{
				this.c2_x_label.BackColor = this.fail;
			}
			else
			{
				this.c2_x_label.BackColor = this.pass;
			}
			bool flag5 = GlobalData.colimatorOrigDataP2[1] < Configure.preCheck_y_point2_min || GlobalData.colimatorOrigDataP2[1] > Configure.preCheck_y_point2_max;
			if (flag5)
			{
				this.c2_y_label.BackColor = this.fail;
			}
			else
			{
				this.c2_y_label.BackColor = this.pass;
			}
			bool flag6 = GlobalData.p2_d < Configure.preCheck_d_point2_min || GlobalData.p2_d > Configure.preCheck_d_point2_max;
			if (flag6)
			{
				this.c2_d_label.BackColor = this.fail;
			}
			else
			{
				this.c2_d_label.BackColor = this.pass;
			}
		}

		// Token: 0x060000C4 RID: 196 RVA: 0x00011218 File Offset: 0x0000F418
		private void preCheck_finish_Click(object sender, EventArgs e)
		{
			MotionController.running = true;
			GSC_Controller.Home("1", "-");
			MotionController.running = false;
			Colimator.serialPort = Colimator.serialPort1;
			Colimator.TurnOffColimator();
			Colimator.serialPort = Colimator.serialPort2;
			Colimator.TurnOffColimator();
			Db.dataSaveIntoPreCheckDB(this.para[0], this.para[1], GlobalData.colimatorOrigDataP13[0], GlobalData.colimatorOrigDataP13[1], GlobalData.p1_d, GlobalData.colimatorOrigDataP2[0], GlobalData.colimatorOrigDataP2[1], GlobalData.p2_d);
			this.entrance.Finish(this.sequence, 5);
		}

		// Token: 0x0400020B RID: 523
		public const int WM_CLOSE = 16;

		// Token: 0x0400020C RID: 524
		private PreCheck.ImgTransferCallback imgTransfer = null;

		// Token: 0x0400020D RID: 525
		private double[] para = new double[11];

		// Token: 0x0400020E RID: 526
		private Entrance entrance;

		// Token: 0x0400020F RID: 527
		private int sequence;

		// Token: 0x04000210 RID: 528
		private int type;

		// Token: 0x04000211 RID: 529
		private string title;

		// Token: 0x04000212 RID: 530
		private Color fail = Color.OrangeRed;

		// Token: 0x04000213 RID: 531
		private Color pass = Color.Green;

		// Token: 0x04000214 RID: 532
		private Color gray = Color.Gray;

		// Token: 0x0200002D RID: 45
		// (Invoke) Token: 0x060002D8 RID: 728
		[UnmanagedFunctionPointer(CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public delegate void ImgTransferCallback(IntPtr data, int count);
	}
}
