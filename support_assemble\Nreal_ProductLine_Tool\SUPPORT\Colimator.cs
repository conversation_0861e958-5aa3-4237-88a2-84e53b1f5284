﻿using System;
using System.IO.Ports;
using System.Text;
using System.Threading;
using System.Windows.Forms;

namespace Nreal_ProductLine_Tool.SUPPORT
{
	// Token: 0x0200000F RID: 15
	internal class Colimator
	{
		// Token: 0x06000071 RID: 113 RVA: 0x00009610 File Offset: 0x00007810
		public static int InitColimator(ref SerialPort sport, string name, int type)
		{
			string[] portNames = SerialPort.GetPortNames();
			bool flag = false;
			for (int i = 0; i < portNames.Length; i++)
			{
				bool flag2 = portNames[i] == name;
				if (flag2)
				{
					flag = true;
				}
			}
			bool flag3 = !flag;
			int result;
			if (flag3)
			{
				MessageBox.Show("无效串口名！");
				result = -1;
			}
			else
			{
				sport.PortName = name;
				sport.BaudRate = 115200;
				sport.DataBits = 8;
				sport.Parity = 0;
				sport.StopBits = 1;
				bool flag4 = type == 1;
				if (flag4)
				{
					sport.DataReceived += new SerialDataReceivedEventHandler(Colimator.PortDataReceived1);
				}
				bool flag5 = type == 2;
				if (flag5)
				{
					sport.DataReceived += new SerialDataReceivedEventHandler(Colimator.PortDataReceived);
				}
				try
				{
					sport.Open();
					Thread.Sleep(50);
					Colimator.serialPort = sport;
					Colimator.TurnOffColimator();
					Colimator.TurnOnColimator();
					Colimator.serialPort.Write("S100\r\n");
					Thread.Sleep(50);
					Colimator.serialPort.Write("W122,0,0\r\n");
					Thread.Sleep(50);
					Colimator.serialPort.Write("W114,0\r\n");
					Thread.Sleep(50);
					Colimator.serialPort.Write("W115,200\r\n");
					Thread.Sleep(50);
					Colimator.serialPort.Write("S101\r\n");
					Thread.Sleep(50);
				}
				catch (Exception ex)
				{
					MessageBox.Show("串口" + name + "打开失败！");
					return -1;
				}
				result = 0;
			}
			return result;
		}

		// Token: 0x06000072 RID: 114 RVA: 0x000097AC File Offset: 0x000079AC
		public static int Init()
		{
			Colimator.serialPort1 = new SerialPort();
			int num = Colimator.InitColimator(ref Colimator.serialPort1, Configure.Colimator_SerialPort1, 1);
			bool flag = num != 0;
			int result;
			if (flag)
			{
				result = num;
			}
			else
			{
				Colimator.serialPort2 = new SerialPort();
				num = Colimator.InitColimator(ref Colimator.serialPort2, Configure.Colimator_SerialPort2, 2);
				result = num;
			}
			return result;
		}

		// Token: 0x06000073 RID: 115 RVA: 0x00009804 File Offset: 0x00007A04
		public static void TurnOnColimator()
		{
		}

		// Token: 0x06000074 RID: 116 RVA: 0x00009814 File Offset: 0x00007A14
		public static void TurnOffColimator()
		{
		}

		// Token: 0x06000075 RID: 117 RVA: 0x00009824 File Offset: 0x00007A24
		public static void PortDataReceived(object sender, SerialDataReceivedEventArgs e)
		{
			bool flag = Colimator.serialPort2 == null;
			if (!flag)
			{
				try
				{
					int bytesToRead = Colimator.serialPort2.BytesToRead;
					UTF8Encoding utf8Encoding = new UTF8Encoding();
					byte[] array = new byte[bytesToRead];
					int num = Colimator.serialPort2.Read(array, 0, bytesToRead);
					bool flag2 = num < 1;
					if (!flag2)
					{
						bool flag3 = Colimator.r2_readLens == 0;
						if (flag3)
						{
							Colimator.r2_command_res_tmp = utf8Encoding.GetString(array);
						}
						else
						{
							Colimator.r2_command_res_tmp += utf8Encoding.GetString(array);
						}
						Colimator.r2_readLens += num;
						bool flag4 = Colimator.r2_command_res_tmp.Length < 2 || Colimator.r2_command_res_tmp.LastIndexOf("\r\n") != Colimator.r2_command_res_tmp.Length - 2;
						if (!flag4)
						{
							Colimator.r2_readLens = 0;
							string[] array2 = Colimator.r2_command_res_tmp.Split(new char[]
							{
								','
							});
							bool flag5 = array2.Length < 5;
							if (!flag5)
							{
								GlobalData.colimatorOrigDataP2[0] = Convert.ToDouble(array2[2]);
								GlobalData.colimatorOrigDataP2[1] = Convert.ToDouble(array2[3]);
								GlobalData.p2_d = Convert.ToDouble(array2[4]);
							}
						}
					}
				}
				catch (Exception ex)
				{
					MessageBox.Show("准直仪数据读取异常！");
					Application.Exit();
				}
			}
		}

		// Token: 0x06000076 RID: 118 RVA: 0x00009988 File Offset: 0x00007B88
		public static void PortDataReceived1(object sender, SerialDataReceivedEventArgs e)
		{
			bool flag = Colimator.serialPort1 == null;
			if (!flag)
			{
				try
				{
					int bytesToRead = Colimator.serialPort1.BytesToRead;
					UTF8Encoding utf8Encoding = new UTF8Encoding();
					byte[] array = new byte[bytesToRead];
					int num = Colimator.serialPort1.Read(array, 0, bytesToRead);
					bool flag2 = num < 1;
					if (!flag2)
					{
						bool flag3 = Colimator.r13_readLens == 0;
						if (flag3)
						{
							Colimator.r13_command_res_tmp = utf8Encoding.GetString(array);
						}
						else
						{
							Colimator.r13_command_res_tmp += utf8Encoding.GetString(array);
						}
						Colimator.r13_readLens += num;
						bool flag4 = Colimator.r13_command_res_tmp.Length < 2 || Colimator.r13_command_res_tmp.LastIndexOf("\r\n") != Colimator.r13_command_res_tmp.Length - 2;
						if (!flag4)
						{
							Colimator.r13_readLens = 0;
							string[] array2 = Colimator.r13_command_res_tmp.Split(new char[]
							{
								','
							});
							bool flag5 = array2.Length < 8;
							if (!flag5)
							{
								GlobalData.colimatorOrigDataP13[0] = Convert.ToDouble(array2[2]);
								GlobalData.colimatorOrigDataP13[1] = Convert.ToDouble(array2[3]);
								GlobalData.colimatorOrigDataP13[2] = Convert.ToDouble(array2[5]);
								GlobalData.colimatorOrigDataP13[3] = Convert.ToDouble(array2[6]);
								GlobalData.colimatorOrigDataP13[4] = Convert.ToDouble(array2[8]);
								GlobalData.colimatorOrigDataP13[5] = Convert.ToDouble(array2[9]);
								GlobalData.p1_d = Convert.ToDouble(array2[4]);
							}
						}
					}
				}
				catch (Exception ex)
				{
					MessageBox.Show("准直仪数据读取异常！");
					Application.Exit();
				}
			}
		}

		// Token: 0x04000093 RID: 147
		public static bool setting;

		// Token: 0x04000094 RID: 148
		public static SerialPort serialPort1;

		// Token: 0x04000095 RID: 149
		public static SerialPort serialPort2;

		// Token: 0x04000096 RID: 150
		public static SerialPort serialPort;

		// Token: 0x04000097 RID: 151
		private static int flag1;

		// Token: 0x04000098 RID: 152
		private static int flag2;

		// Token: 0x04000099 RID: 153
		public static int r2_readLens;

		// Token: 0x0400009A RID: 154
		public static string r2_command_res_tmp;

		// Token: 0x0400009B RID: 155
		public static int r13_readLens;

		// Token: 0x0400009C RID: 156
		public static string r13_command_res_tmp;
	}
}
