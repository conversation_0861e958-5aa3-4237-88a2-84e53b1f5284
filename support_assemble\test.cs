﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;
using Nreal_ProductLine_Tool.SUPPORT;

namespace support_assemble
{
	// Token: 0x02000003 RID: 3
	public partial class test : FormDefault
	{
		// Token: 0x0600000F RID: 15 RVA: 0x00004004 File Offset: 0x00002204
		public test(Entrance entrance, int sequence, string title)
		{
			this.sequence = sequence;
			this.entrance = entrance;
			this.title = title;
			this.InitializeComponent();
			base.TopLevel = false;
			base.FormBorderStyle = FormBorderStyle.None;
			this.Dock = DockStyle.Fill;
		}

		// Token: 0x06000010 RID: 16 RVA: 0x00004054 File Offset: 0x00002254
		private void finish_btn_Click(object sender, EventArgs e)
		{
			base.Hide();
			this.entrance.Finish(this.sequence, 0);
		}

		// Token: 0x06000011 RID: 17 RVA: 0x00004074 File Offset: 0x00002274
		public override string GetName()
		{
			return this.title;
		}

		// Token: 0x06000012 RID: 18 RVA: 0x0000408C File Offset: 0x0000228C
		public override void Start()
		{
			GlobalData.sn_buchang_confirm = "";
			GlobalData.sn_toujing_confirm = "";
			base.Show();
		}

		// Token: 0x04000029 RID: 41
		private Entrance entrance;

		// Token: 0x0400002A RID: 42
		private int sequence;

		// Token: 0x0400002B RID: 43
		private int type;

		// Token: 0x0400002C RID: 44
		public string title;
	}
}
