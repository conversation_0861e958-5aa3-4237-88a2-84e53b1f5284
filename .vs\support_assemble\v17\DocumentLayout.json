{"Version": 1, "WorkspaceRootPath": "D:\\csharp\\support_analyze\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{7D471186-1164-4851-B7B6-93D43EDE63D6}|support_assemble\\support_assemble.csproj|d:\\csharp\\support_analyze\\support_assemble\\nreal_productline_tool\\support\\cameraimagelib.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{7D471186-1164-4851-B7B6-93D43EDE63D6}|support_assemble\\support_assemble.csproj|solutionrelative:support_assemble\\nreal_productline_tool\\support\\cameraimagelib.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{7D471186-1164-4851-B7B6-93D43EDE63D6}|support_assemble\\support_assemble.csproj|d:\\csharp\\support_analyze\\support_assemble\\nreal_productline_tool\\support\\lensop.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{7D471186-1164-4851-B7B6-93D43EDE63D6}|support_assemble\\support_assemble.csproj|solutionrelative:support_assemble\\nreal_productline_tool\\support\\lensop.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{7D471186-1164-4851-B7B6-93D43EDE63D6}|support_assemble\\support_assemble.csproj|d:\\csharp\\support_analyze\\support_assemble\\nreal_productline_tool\\support\\lensop.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form", "RelativeMoniker": "D:0:0:{7D471186-1164-4851-B7B6-93D43EDE63D6}|support_assemble\\support_assemble.csproj|solutionrelative:support_assemble\\nreal_productline_tool\\support\\lensop.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form"}, {"AbsoluteMoniker": "D:0:0:{7D471186-1164-4851-B7B6-93D43EDE63D6}|support_assemble\\support_assemble.csproj|d:\\csharp\\support_analyze\\support_assemble\\controller\\imagehandle.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{7D471186-1164-4851-B7B6-93D43EDE63D6}|support_assemble\\support_assemble.csproj|solutionrelative:support_assemble\\controller\\imagehandle.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 2, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{34e76e81-ee4a-11d0-ae2e-00a0c90fffc3}"}, {"$type": "Bookmark", "Name": "ST:0:0:{b1e99781-ab81-11d0-b683-00aa00a3ee26}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "CameraImageLib.cs", "DocumentMoniker": "D:\\csharp\\support_analyze\\support_assemble\\Nreal_ProductLine_Tool\\SUPPORT\\CameraImageLib.cs", "RelativeDocumentMoniker": "support_assemble\\Nreal_ProductLine_Tool\\SUPPORT\\CameraImageLib.cs", "ToolTip": "D:\\csharp\\support_analyze\\support_assemble\\Nreal_ProductLine_Tool\\SUPPORT\\CameraImageLib.cs", "RelativeToolTip": "support_assemble\\Nreal_ProductLine_Tool\\SUPPORT\\CameraImageLib.cs", "ViewState": "AgIAABYAAAAAAAAAAAA1wBsAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-08T04:13:28.553Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "LensOp.cs", "DocumentMoniker": "D:\\csharp\\support_analyze\\support_assemble\\Nreal_ProductLine_Tool\\SUPPORT\\LensOp.cs", "RelativeDocumentMoniker": "support_assemble\\Nreal_ProductLine_Tool\\SUPPORT\\LensOp.cs", "ToolTip": "D:\\csharp\\support_analyze\\support_assemble\\Nreal_ProductLine_Tool\\SUPPORT\\LensOp.cs", "RelativeToolTip": "support_assemble\\Nreal_ProductLine_Tool\\SUPPORT\\LensOp.cs", "ViewState": "AgIAAK8AAAAAAAAAAAAowAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-08T04:13:07.558Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "LensOp.cs [设计]", "DocumentMoniker": "D:\\csharp\\support_analyze\\support_assemble\\Nreal_ProductLine_Tool\\SUPPORT\\LensOp.cs", "RelativeDocumentMoniker": "support_assemble\\Nreal_ProductLine_Tool\\SUPPORT\\LensOp.cs", "ToolTip": "D:\\csharp\\support_analyze\\support_assemble\\Nreal_ProductLine_Tool\\SUPPORT\\LensOp.cs [设计]", "RelativeToolTip": "support_assemble\\Nreal_ProductLine_Tool\\SUPPORT\\LensOp.cs [设计]", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-08T04:13:03.509Z", "EditorCaption": " [设计]"}, {"$type": "Document", "DocumentIndex": 3, "Title": "ImageHandle.cs", "DocumentMoniker": "D:\\csharp\\support_analyze\\support_assemble\\controller\\ImageHandle.cs", "RelativeDocumentMoniker": "support_assemble\\controller\\ImageHandle.cs", "ToolTip": "D:\\csharp\\support_analyze\\support_assemble\\controller\\ImageHandle.cs", "RelativeToolTip": "support_assemble\\controller\\ImageHandle.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAuwAUAAAAQAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-08T04:12:48.29Z", "EditorCaption": ""}]}]}]}