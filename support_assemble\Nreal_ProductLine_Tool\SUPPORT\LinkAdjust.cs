﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using System.Runtime.InteropServices;
using System.Threading;
using System.Windows.Forms;

namespace Nreal_ProductLine_Tool.SUPPORT
{
	// Token: 0x02000014 RID: 20
	public partial class LinkAdjust : FormDefault
	{
		// Token: 0x06000092 RID: 146 RVA: 0x0000BEF4 File Offset: 0x0000A0F4
		public LinkAdjust(Entrance entrance, int sequence, string title)
		{
			this.sequence = sequence;
			this.entrance = entrance;
			this.title = title;
			this.InitializeComponent();
			base.Load += this.MainForm_Load;
			this.imgTransfer = new LinkAdjust.ImgTransferCallback(this.imgTransferCallback);
			CameraImageLib.RegisterCallBackShowImage(this.imgTransfer);
			string text = Path.Combine(Environment.CurrentDirectory, "pic");
			bool flag = !Directory.Exists(text);
			if (flag)
			{
				Directory.CreateDirectory(text);
			}
			this.message_sem = new SemaphoreSlim(0, 1);
			Thread thread = new Thread(new ThreadStart(this.capture_analyze));
			thread.Start();
			this.axis_operate_sem = new SemaphoreSlim(0, 1);
			Thread thread2 = new Thread(new ThreadStart(this.axis_operate));
			thread2.Start();
			base.TopLevel = false;
			base.FormBorderStyle = FormBorderStyle.None;
			this.Dock = DockStyle.Fill;
		}

		// Token: 0x06000093 RID: 147 RVA: 0x0000C02C File Offset: 0x0000A22C
		private void MainForm_Load(object sender, EventArgs e)
		{
			base.WindowState = FormWindowState.Maximized;
			this.groupBox1.Anchor = (AnchorStyles.Top | AnchorStyles.Right);
			this.groupBox1.Location = new Point(base.ClientSize.Width - this.groupBox1.Width - 20, 20);
			this.pictureBox1.Location = new Point(20, 20);
			this.pictureBox1.Size = new Size(base.ClientSize.Width - this.groupBox1.Width - 60, base.ClientSize.Height - 60);
		}

		// Token: 0x06000094 RID: 148 RVA: 0x0000C0D4 File Offset: 0x0000A2D4
		private void FrmMain_FormClosed(object sender, FormClosedEventArgs e)
		{
			Process.GetCurrentProcess().Kill();
		}

		// Token: 0x06000095 RID: 149 RVA: 0x0000C0E4 File Offset: 0x0000A2E4
		public void imgTransferCallback(IntPtr data, int count)
		{
			TimeSpan timeSpan = new TimeSpan(DateTime.Now.Ticks);
			byte[] array = new byte[count];
			Marshal.Copy(data, array, 0, count);
			MemoryStream stream = new MemoryStream(array);
			Image bitMap = Image.FromStream(stream);
			this.pictureBox1.BeginInvoke(new MethodInvoker(delegate()
			{
				this.pictureBox1.Image = bitMap;
			}));
		}

		// Token: 0x06000096 RID: 150 RVA: 0x0000C150 File Offset: 0x0000A350
		public override void Start()
		{
			CameraImageLib.RegisterCallBackShowImage(this.imgTransfer);
			this.link_next_button.Enabled = false;
			this.link_adjust_angle_label.BackColor = Color.Gray;
			this.link_adjust_line_label.BackColor = Color.Gray;
			this.link_adjust_center_label.BackColor = Color.Gray;
			this.pulse = Configure.yDest1;
			this.axis_operate_sem.Release();
			base.Show();
			this.link_adjust_status_label.BeginInvoke(new MethodInvoker(delegate()
			{
				this.link_adjust_status_label.Text = "移动相机...";
			}));
		}

		// Token: 0x06000097 RID: 151 RVA: 0x0000C1E0 File Offset: 0x0000A3E0
		public override void ExcepStop()
		{
			this.isStopCapture = true;
			GSC_Controller.Stop("1");
			Thread.Sleep(200);
		}

		// Token: 0x06000098 RID: 152 RVA: 0x0000C200 File Offset: 0x0000A400
		private void capture_analyze()
		{
			while (!this.isExited)
			{
				this.message_sem.Wait();
				while (!this.isStopCapture)
				{
					int ret = CameraImageLib.Capture();
					bool flag = ret == 0;
					if (flag)
					{
						ret = CameraImageLib.AnalysisSupport(this.show_flag, this.para);
						base.Invoke(new MethodInvoker(delegate()
						{
							bool flag2 = ret == 0;
							if (flag2)
							{
								this.link_adjust_status_label.Text = "分析成功";
								this.link_adjust_status_label.BackColor = Color.Gray;
							}
							else
							{
								this.link_adjust_status_label.Text = "分析失败";
								this.link_adjust_status_label.BackColor = Color.Red;
							}
							GlobalData.CompensationMirrorCenterX = this.para[3];
							GlobalData.CompensationMirrorCenterY = this.para[4];
							GlobalData.LensCenterX = this.para[5];
							GlobalData.LensCenterY = this.para[6];
							GlobalData.EdgeDistance = this.para[0];
							GlobalData.CenterDistance = this.para[2];
							GlobalData.Angle = this.para[1];
							GlobalData.CompensationMirrorMaxResidual = this.para[7];
							GlobalData.CompensationMirrorAverageResidual = this.para[8];
							GlobalData.LensMaxResidual = this.para[9];
							GlobalData.LensAverageResidual = this.para[10];
							this.link_adjust_line_label.Text = this.para[0].ToString("F3");
							this.link_adjust_angle_label.Text = this.para[1].ToString("F3");
							bool flag3 = GlobalData.Angle < Configure.angle_max;
							if (flag3)
							{
								this.link_adjust_angle_label.BackColor = Color.Green;
							}
							else
							{
								this.link_adjust_angle_label.BackColor = Color.Red;
							}
							bool flag4 = GlobalData.EdgeDistance < Configure.line_diff_max;
							if (flag4)
							{
								this.link_adjust_line_label.BackColor = Color.Green;
							}
							else
							{
								this.link_adjust_line_label.BackColor = Color.Red;
							}
							double num = GlobalData.CenterDistance - Configure.center_offset_compensatoin;
							this.link_adjust_center_label.Text = num.ToString("F3");
							bool flag5 = num < Configure.circle_center_diff_max;
							if (flag5)
							{
								this.link_adjust_center_label.BackColor = Color.Green;
							}
							else
							{
								this.link_adjust_center_label.BackColor = Color.Red;
							}
							this.adjust_prism_centerx_label.Text = this.para[3].ToString("F3");
							this.adjust_prism_lenx_label.Text = this.para[5].ToString("F3");
							this.adjust_prism_centery_label.Text = this.para[4].ToString("F3");
							this.adjust_prism_leny_label.Text = this.para[6].ToString("F3");
							double value = this.para[3] - this.para[5] - Configure.x_offset_compensatoin;
							double value2 = this.para[4] - this.para[6] - Configure.y_offset_compensatoin;
							bool flag6 = Math.Abs(value) < Configure.x_offset_max;
							if (flag6)
							{
								this.adjust_prism_movex_label.BackColor = Color.Green;
							}
							else
							{
								this.adjust_prism_movex_label.BackColor = Color.Red;
							}
							bool flag7 = Math.Abs(value2) < Configure.y_offset_max;
							if (flag7)
							{
								this.adjust_prism_movey_label.BackColor = Color.Green;
							}
							else
							{
								this.adjust_prism_movey_label.BackColor = Color.Red;
							}
							this.adjust_prism_movex_label.Text = value.ToString("F3");
							this.adjust_prism_movey_label.Text = value2.ToString("F3");
							this.adjust_prism_max_residual_label.Text = this.para[7].ToString("F3");
							this.adjust_prism_avg_residual_label.Text = this.para[8].ToString("F3");
							this.adjust_len_max_residual_label.Text = this.para[9].ToString("F3");
							this.adjust_len_avg_residual_label.Text = this.para[10].ToString("F3");
							bool flag8 = GlobalData.Angle < Configure.angle_max && GlobalData.EdgeDistance < Configure.line_diff_max && GlobalData.CenterDistance < Configure.circle_center_diff_max;
							if (flag8)
							{
								this.link_next_button.Enabled = true;
							}
						}));
					}
				}
				this.pictureBox1.BeginInvoke(new MethodInvoker(delegate()
				{
					string text = Path.Combine(Application.StartupPath, "Images");
					bool flag2 = !Directory.Exists(text);
					if (flag2)
					{
						Directory.CreateDirectory(text);
					}
					string path = DateTime.Now.ToString("yyyyMMdd_HH_mm_ss") + GlobalData.saved_sn_buchang_confirm + ".png";
					string filename = Path.Combine(text, path);
					bool flag3 = this.pictureBox1.Image != null;
					if (flag3)
					{
						this.pictureBox1.Image.Save(filename, ImageFormat.Png);
					}
				}));
			}
		}

		// Token: 0x06000099 RID: 153 RVA: 0x0000C2AF File Offset: 0x0000A4AF
		private void link_next_button_Click(object sender, EventArgs e)
		{
			this.isStopCapture = true;
			this.entrance.Finish(this.sequence, 0);
		}

		// Token: 0x0600009A RID: 154 RVA: 0x0000C2CC File Offset: 0x0000A4CC
		private void show_pic_help_button_Click(object sender, EventArgs e)
		{
			bool flag = this.show_pic_help_button.Text == "显示辅助线";
			if (flag)
			{
				this.show_flag = 1;
				this.show_pic_help_button.Text = "取消辅助线";
			}
			else
			{
				bool flag2 = this.show_pic_help_button.Text == "取消辅助线";
				if (flag2)
				{
					this.show_flag = 0;
					this.show_pic_help_button.Text = "显示辅助线";
				}
			}
		}

		// Token: 0x0600009B RID: 155 RVA: 0x0000C344 File Offset: 0x0000A544
		private void adjust_pre_button_Click(object sender, EventArgs e)
		{
			this.isStopCapture = true;
			GSC_Controller.Stop("1");
			Thread.Sleep(200);
			this.pulse = 0;
			this.axis_operate_sem.Release();
			this.entrance.Finish(this.sequence, 4);
		}

		// Token: 0x0600009C RID: 156 RVA: 0x0000C398 File Offset: 0x0000A598
		private void axis_operate()
		{
			while (!this.isExited)
			{
				this.axis_operate_sem.Wait();
				int i;
				for (i = 0; i < 10; i++)
				{
					bool flag = !MotionController.running;
					if (flag)
					{
						break;
					}
					Thread.Sleep(200);
				}
				bool flag2 = i == 10;
				if (flag2)
				{
					MessageBox.Show("当前电机繁忙，请重新开始");
				}
				else
				{
					MotionController.running = true;
					try
					{
						bool flag3 = this.pulse != 0;
						if (flag3)
						{
							GSC_Controller.MoveAbs("1", this.pulse);
							this.message_sem.Release();
							this.isStopCapture = false;
						}
						else
						{
							GSC_Controller.Home("1", "-");
						}
					}
					catch (Exception ex)
					{
						MessageBox.Show("轴状态异常，重启程序");
						Process.GetCurrentProcess().Kill();
					}
					MotionController.running = false;
				}
			}
		}

		// Token: 0x0400019D RID: 413
		private int mesEnable = 0;

		// Token: 0x0400019E RID: 414
		public const int WM_CLOSE = 16;

		// Token: 0x0400019F RID: 415
		private bool isExited = false;

		// Token: 0x040001A0 RID: 416
		private bool isStopCapture = false;

		// Token: 0x040001A1 RID: 417
		protected SemaphoreSlim message_sem;

		// Token: 0x040001A2 RID: 418
		protected SemaphoreSlim axis_operate_sem;

		// Token: 0x040001A3 RID: 419
		private LinkAdjust.ImgTransferCallback imgTransfer = null;

		// Token: 0x040001A4 RID: 420
		private double[] para = new double[11];

		// Token: 0x040001A5 RID: 421
		private int show_flag = 0;

		// Token: 0x040001A6 RID: 422
		private Entrance entrance;

		// Token: 0x040001A7 RID: 423
		private int sequence;

		// Token: 0x040001A8 RID: 424
		private int type;

		// Token: 0x040001A9 RID: 425
		private string title;

		// Token: 0x040001AA RID: 426
		private int pulse = 0;

		// Token: 0x040001AB RID: 427
		private Dictionary<string, string> mes_resource = new Dictionary<string, string>();

		// Token: 0x02000029 RID: 41
		// (Invoke) Token: 0x060002CD RID: 717
		[UnmanagedFunctionPointer(CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public delegate void ImgTransferCallback(IntPtr data, int count);
	}
}
