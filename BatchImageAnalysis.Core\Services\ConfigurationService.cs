using IniParser;
using Nreal.BatchImageAnalysis.Core.Models;
using System.IO;

namespace Nreal.BatchImageAnalysis.Core.Services
{
    public class ConfigurationService
    {
        public AnalysisConfig LoadConfig(string iniFilePath)
        {
            if (!File.Exists(iniFilePath))
            {
                throw new FileNotFoundException("Configuration file not found.", iniFilePath);
            }

            var parser = new FileIniDataParser();
            var iniData = parser.ReadFile(iniFilePath);
            return AnalysisConfig.FromIniData(iniData["0"]);
        }
    }
}