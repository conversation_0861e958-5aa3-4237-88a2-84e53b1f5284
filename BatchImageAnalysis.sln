﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "BatchImageAnalysis.App", "BatchImageAnalysis.App\BatchImageAnalysis.App.csproj", "{5A5A5EA6-9AA1-4306-A197-47E982AB44F3}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "BatchImageAnalysis.Core", "BatchImageAnalysis.Core\BatchImageAnalysis.Core.csproj", "{96F71266-789E-4FDD-A676-9C1F88DACA85}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "BatchImageAnalysis.Interop", "BatchImageAnalysis.Interop\BatchImageAnalysis.Interop.csproj", "{548BA1F6-8EFA-4E6B-B56E-CF5472D0B61B}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{5A5A5EA6-9AA1-4306-A197-47E982AB44F3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5A5A5EA6-9AA1-4306-A197-47E982AB44F3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5A5A5EA6-9AA1-4306-A197-47E982AB44F3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5A5A5EA6-9AA1-4306-A197-47E982AB44F3}.Release|Any CPU.Build.0 = Release|Any CPU
		{96F71266-789E-4FDD-A676-9C1F88DACA85}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{96F71266-789E-4FDD-A676-9C1F88DACA85}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{96F71266-789E-4FDD-A676-9C1F88DACA85}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{96F71266-789E-4FDD-A676-9C1F88DACA85}.Release|Any CPU.Build.0 = Release|Any CPU
		{548BA1F6-8EFA-4E6B-B56E-CF5472D0B61B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{548BA1F6-8EFA-4E6B-B56E-CF5472D0B61B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{548BA1F6-8EFA-4E6B-B56E-CF5472D0B61B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{548BA1F6-8EFA-4E6B-B56E-CF5472D0B61B}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
EndGlobal
