﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.Timers;
using System.Windows.Forms;

namespace Nreal_ProductLine_Tool.SUPPORT
{
	// Token: 0x02000008 RID: 8
	public partial class ColimatorUVHandle : FormDefault
	{
		// Token: 0x06000028 RID: 40 RVA: 0x00004C04 File Offset: 0x00002E04
		public ColimatorUVHandle(Entrance entrance, int sequence, string title)
		{
			this.sequence = sequence;
			this.entrance = entrance;
			this.title = title;
			this.InitializeComponent();
			this.show_timer.Elapsed += this.Show_Timer_Elapsed;
			this.show_timer.Interval = 40.0;
			this.show_timer.Enabled = false;
			this.uv_timer.Elapsed += this.UV_Timer_Elapsed;
			this.uv_timer.Enabled = false;
			base.TopLevel = false;
			base.FormBorderStyle = FormBorderStyle.None;
			this.Dock = DockStyle.Fill;
		}

		// Token: 0x06000029 RID: 41 RVA: 0x00004D10 File Offset: 0x00002F10
		private void p1_activate_btn_Click(object sender, EventArgs e)
		{
			this.check_flag = 1;
			this.colimator_p2_groupBox.Enabled = false;
			this.colimator_p1_groupBox.Enabled = true;
			this.colimator_1_btn.Enabled = true;
			this.show_timer.Enabled = true;
			this.p1_checked = true;
		}

		// Token: 0x0600002A RID: 42 RVA: 0x00004D60 File Offset: 0x00002F60
		private void InitStatus()
		{
			this.p1_x_label.Text = "";
			this.p1_y_label.Text = "";
			this.p3_x_label.Text = "";
			this.p3_y_label.Text = "";
			this.p13_avgx_label.Text = "";
			this.p13_avgy_label.Text = "";
			this.p2_x_label.Text = "";
			this.p2_y_label.Text = "";
			this.p1_x_label.BackColor = this.gray;
			this.p1_y_label.BackColor = this.gray;
			this.p3_x_label.BackColor = this.gray;
			this.p3_y_label.BackColor = this.gray;
			this.p13_avgx_label.BackColor = this.gray;
			this.p13_avgy_label.BackColor = this.gray;
			this.p2_x_label.BackColor = this.gray;
			this.p2_y_label.BackColor = this.gray;
			this.coli_diff_label.BackColor = this.gray;
			this.coli_diff_label.Text = "";
		}

		// Token: 0x0600002B RID: 43 RVA: 0x00004EAC File Offset: 0x000030AC
		public override void Start()
		{
			this.uv_btn_click = false;
			this.uv_phrase = 1;
			ColimatorUVHandle.timer_stop = false;
			GlobalData.distance[0] = -1.0;
			this.colimator_1_btn.Enabled = false;
			this.InitStatus();
			this.show_timer.Enabled = true;
			Logs.WriteInfo("uv begin", true);
			base.Show();
		}

		// Token: 0x0600002C RID: 44 RVA: 0x00004F12 File Offset: 0x00003112
		public override void ExcepStop()
		{
			ColimatorUVHandle.timer_stop = true;
			this.show_timer.Enabled = false;
			Colimator.serialPort = Colimator.serialPort1;
			Colimator.TurnOffColimator();
			Colimator.serialPort = Colimator.serialPort2;
			Colimator.TurnOffColimator();
			ZMotionController.VacuummeterGasOutClose();
		}

		// Token: 0x0600002D RID: 45 RVA: 0x00004F50 File Offset: 0x00003150
		private int FindEffectPoints()
		{
			int num = 0;
			bool flag = GlobalData.colimatorOrigDataP13[0] > Configure.x_except_min && GlobalData.colimatorOrigDataP13[0] < Configure.x_except_max && GlobalData.colimatorOrigDataP13[1] > Configure.y_except_min && GlobalData.colimatorOrigDataP13[1] < Configure.y_except_max;
			if (flag)
			{
				num++;
				bool flag2 = this.uv_phrase == 1;
				if (flag2)
				{
					GlobalData.phrase2_colimatorPoint1[0] = GlobalData.colimatorOrigDataP13[0];
					GlobalData.phrase2_colimatorPoint1[1] = GlobalData.colimatorOrigDataP13[1];
				}
				else
				{
					GlobalData.phrase3_colimatorPoint1[0] = GlobalData.colimatorOrigDataP13[0];
					GlobalData.phrase3_colimatorPoint1[1] = GlobalData.colimatorOrigDataP13[1];
				}
			}
			else
			{
				bool flag3 = this.uv_phrase == 1;
				if (flag3)
				{
					GlobalData.phrase2_colimatorPoint1[0] = Configure.x_except_max;
					GlobalData.phrase2_colimatorPoint1[1] = Configure.y_except_max;
				}
				else
				{
					GlobalData.phrase3_colimatorPoint1[0] = Configure.x_except_max;
					GlobalData.phrase3_colimatorPoint1[1] = Configure.y_except_max;
				}
			}
			bool flag4 = GlobalData.colimatorOrigDataP13[2] > Configure.x_except_min && GlobalData.colimatorOrigDataP13[2] < Configure.x_except_max && GlobalData.colimatorOrigDataP13[3] > Configure.y_except_min && GlobalData.colimatorOrigDataP13[3] < Configure.y_except_max;
			if (flag4)
			{
				num++;
				bool flag5 = this.uv_phrase == 1;
				if (flag5)
				{
					GlobalData.phrase2_colimatorPoint3[0] = GlobalData.colimatorOrigDataP13[2];
					GlobalData.phrase2_colimatorPoint3[1] = GlobalData.colimatorOrigDataP13[3];
				}
				else
				{
					GlobalData.phrase3_colimatorPoint3[0] = GlobalData.colimatorOrigDataP13[2];
					GlobalData.phrase3_colimatorPoint3[1] = GlobalData.colimatorOrigDataP13[3];
				}
			}
			else
			{
				bool flag6 = this.uv_phrase == 1;
				if (flag6)
				{
					GlobalData.phrase2_colimatorPoint3[0] = Configure.x_except_max;
					GlobalData.phrase2_colimatorPoint3[1] = Configure.y_except_max;
				}
				else
				{
					GlobalData.phrase3_colimatorPoint3[0] = Configure.x_except_max;
					GlobalData.phrase3_colimatorPoint3[1] = Configure.y_except_max;
				}
			}
			bool flag7 = GlobalData.colimatorOrigDataP13[4] > Configure.x_except_min && GlobalData.colimatorOrigDataP13[4] < Configure.x_except_max && GlobalData.colimatorOrigDataP13[5] > Configure.y_except_min && GlobalData.colimatorOrigDataP13[5] < Configure.y_except_max;
			if (flag7)
			{
				bool flag8 = this.uv_phrase == 1;
				if (flag8)
				{
					bool flag9 = num < 2;
					if (flag9)
					{
						bool flag10 = GlobalData.phrase2_colimatorPoint1[0] == Configure.x_except_max;
						if (flag10)
						{
							GlobalData.phrase2_colimatorPoint1[0] = GlobalData.colimatorOrigDataP13[4];
							GlobalData.phrase2_colimatorPoint1[1] = GlobalData.colimatorOrigDataP13[5];
						}
						bool flag11 = GlobalData.phrase2_colimatorPoint3[0] == Configure.x_except_max;
						if (flag11)
						{
							GlobalData.phrase2_colimatorPoint3[0] = GlobalData.colimatorOrigDataP13[4];
							GlobalData.phrase2_colimatorPoint3[1] = GlobalData.colimatorOrigDataP13[5];
						}
					}
					else
					{
						bool flag12 = GlobalData.phrase2_colimatorPoint1[0] > GlobalData.phrase2_colimatorPoint3[0];
						if (flag12)
						{
							bool flag13 = GlobalData.phrase2_colimatorPoint1[0] > GlobalData.colimatorOrigDataP13[4];
							if (flag13)
							{
								GlobalData.phrase2_colimatorPoint1[0] = GlobalData.colimatorOrigDataP13[4];
								GlobalData.phrase2_colimatorPoint1[1] = GlobalData.colimatorOrigDataP13[5];
							}
						}
						else
						{
							bool flag14 = GlobalData.phrase2_colimatorPoint2[0] > GlobalData.colimatorOrigDataP13[4];
							if (flag14)
							{
								GlobalData.phrase2_colimatorPoint2[0] = GlobalData.colimatorOrigDataP13[4];
								GlobalData.phrase2_colimatorPoint2[1] = GlobalData.colimatorOrigDataP13[5];
							}
						}
					}
				}
				else
				{
					bool flag15 = num < 2;
					if (flag15)
					{
						bool flag16 = GlobalData.phrase3_colimatorPoint1[0] == Configure.x_except_max;
						if (flag16)
						{
							GlobalData.phrase3_colimatorPoint1[0] = GlobalData.colimatorOrigDataP13[4];
							GlobalData.phrase3_colimatorPoint1[1] = GlobalData.colimatorOrigDataP13[5];
						}
						bool flag17 = GlobalData.phrase3_colimatorPoint3[0] == Configure.x_except_max;
						if (flag17)
						{
							GlobalData.phrase3_colimatorPoint3[0] = GlobalData.colimatorOrigDataP13[4];
							GlobalData.phrase3_colimatorPoint3[1] = GlobalData.colimatorOrigDataP13[5];
						}
					}
					else
					{
						bool flag18 = GlobalData.phrase3_colimatorPoint1[0] > GlobalData.phrase3_colimatorPoint3[0];
						if (flag18)
						{
							bool flag19 = GlobalData.phrase3_colimatorPoint1[0] > GlobalData.colimatorOrigDataP13[4];
							if (flag19)
							{
								GlobalData.phrase3_colimatorPoint1[0] = GlobalData.colimatorOrigDataP13[4];
								GlobalData.phrase3_colimatorPoint1[1] = GlobalData.colimatorOrigDataP13[5];
							}
						}
						else
						{
							bool flag20 = GlobalData.phrase3_colimatorPoint3[0] > GlobalData.colimatorOrigDataP13[4];
							if (flag20)
							{
								GlobalData.phrase3_colimatorPoint3[0] = GlobalData.colimatorOrigDataP13[4];
								GlobalData.phrase3_colimatorPoint3[1] = GlobalData.colimatorOrigDataP13[5];
							}
						}
					}
				}
				num++;
			}
			return num;
		}

		// Token: 0x0600002E RID: 46 RVA: 0x0000537C File Offset: 0x0000357C
		private void p2_operate(int phrase)
		{
			this.p2_x_label.Text = GlobalData.colimatorOrigDataP2[0].ToString("F4");
			this.p2_y_label.Text = GlobalData.colimatorOrigDataP2[1].ToString("F4");
			bool flag = phrase == 1;
			double num;
			double num2;
			double num3;
			double num4;
			if (flag)
			{
				num = Configure.preUV_x_point2_min;
				num2 = Configure.preUV_x_point2_max;
				num3 = Configure.preUV_y_point2_min;
				num4 = Configure.preUV_y_point2_max;
				GlobalData.phrase2_colimatorPoint1[0] = GlobalData.colimatorOrigDataP2[0];
				GlobalData.phrase2_colimatorPoint1[1] = GlobalData.colimatorOrigDataP2[1];
			}
			else
			{
				num = Configure.afterUV_x_point2_min;
				num2 = Configure.afterUV_x_point2_max;
				num3 = Configure.afterUV_y_point2_min;
				num4 = Configure.afterUV_y_point2_max;
				GlobalData.phrase3_colimatorPoint1[0] = GlobalData.colimatorOrigDataP2[0];
				GlobalData.phrase3_colimatorPoint1[1] = GlobalData.colimatorOrigDataP2[1];
			}
			bool flag2 = GlobalData.colimatorOrigDataP2[0] < num || GlobalData.colimatorOrigDataP2[0] > num2;
			if (flag2)
			{
				this.p2_x_label.BackColor = this.fail;
			}
			else
			{
				this.p2_x_label.BackColor = this.pass;
			}
			bool flag3 = GlobalData.colimatorOrigDataP2[1] < num3 || GlobalData.colimatorOrigDataP2[1] > num4;
			if (flag3)
			{
				this.p2_y_label.BackColor = this.fail;
			}
			else
			{
				this.p2_y_label.BackColor = this.pass;
			}
		}

		// Token: 0x0600002F RID: 47 RVA: 0x000054CC File Offset: 0x000036CC
		private void Show_Timer_Elapsed(object sender, ElapsedEventArgs e)
		{
			bool flag = !ColimatorUVHandle.timer_stop;
			if (flag)
			{
				Logs.WriteInfo("check uv condition", true);
				base.Invoke(new MethodInvoker(delegate()
				{
					this.dist_val_label.Text = (GlobalData.distance[0].ToString() ?? "");
					this.InitStatus();
					int num = this.FindEffectPoints();
					bool flag2 = num == 0;
					if (flag2)
					{
						this.colimator_p13_status_label.Text = "当前没有有效点";
						this.colimator_p13_status_label.ForeColor = Color.Red;
						this.p2_operate(1);
					}
					else
					{
						this.colimator_p13_status_label.Text = "";
						this.colimator_p13_status_label.ForeColor = Color.Gray;
						bool flag3 = num == 1;
						if (flag3)
						{
							bool flag4 = GlobalData.phrase2_colimatorPoint1[0] != Configure.x_except_max;
							if (flag4)
							{
								GlobalData.phrase2_colimatorPoint3[0] = GlobalData.phrase2_colimatorPoint1[0];
								GlobalData.phrase2_colimatorPoint3[1] = GlobalData.phrase2_colimatorPoint1[1];
							}
							else
							{
								GlobalData.phrase2_colimatorPoint1[0] = GlobalData.phrase2_colimatorPoint3[0];
								GlobalData.phrase2_colimatorPoint1[1] = GlobalData.phrase2_colimatorPoint3[1];
							}
						}
						this.p1_x_label.Text = GlobalData.phrase2_colimatorPoint1[0].ToString("F4");
						this.p1_y_label.Text = GlobalData.phrase2_colimatorPoint1[1].ToString("F4");
						this.p3_x_label.Text = GlobalData.phrase2_colimatorPoint3[0].ToString("F4");
						this.p3_y_label.Text = GlobalData.phrase2_colimatorPoint3[1].ToString("F4");
						bool flag5 = GlobalData.phrase2_colimatorPoint1[0] < Configure.preUV_x_point1_min || GlobalData.phrase2_colimatorPoint1[0] > Configure.preUV_x_point1_max;
						if (flag5)
						{
							this.p1_x_label.BackColor = this.fail;
						}
						else
						{
							this.p1_x_label.BackColor = this.pass;
						}
						bool flag6 = GlobalData.phrase2_colimatorPoint1[1] < Configure.preUV_y_point1_min || GlobalData.phrase2_colimatorPoint1[1] > Configure.preUV_y_point1_max;
						if (flag6)
						{
							this.p1_y_label.BackColor = this.fail;
						}
						else
						{
							this.p1_y_label.BackColor = this.pass;
						}
						bool flag7 = GlobalData.phrase2_colimatorPoint3[0] < Configure.preUV_x_point3_min || GlobalData.phrase2_colimatorPoint3[0] > Configure.preUV_x_point3_max;
						if (flag7)
						{
							this.p3_x_label.BackColor = this.fail;
						}
						else
						{
							this.p3_x_label.BackColor = this.pass;
						}
						bool flag8 = GlobalData.phrase2_colimatorPoint3[1] < Configure.preUV_y_point3_min || GlobalData.phrase2_colimatorPoint3[1] > Configure.preUV_y_point3_max;
						if (flag8)
						{
							this.p3_y_label.BackColor = this.fail;
						}
						else
						{
							this.p3_y_label.BackColor = this.pass;
						}
						GlobalData.phrase2_colimatorPoint13Avg[0] = (GlobalData.phrase2_colimatorPoint1[0] + GlobalData.phrase2_colimatorPoint3[0]) / 2.0;
						GlobalData.phrase2_colimatorPoint13Avg[1] = (GlobalData.phrase2_colimatorPoint1[1] + GlobalData.phrase2_colimatorPoint3[1]) / 2.0;
						this.p13_avgx_label.Text = GlobalData.phrase2_colimatorPoint13Avg[0].ToString("F4");
						this.p13_avgy_label.Text = GlobalData.phrase2_colimatorPoint13Avg[1].ToString("F4");
						bool flag9 = GlobalData.phrase2_colimatorPoint13Avg[0] < Configure.preUV_x_point13_min || GlobalData.phrase2_colimatorPoint13Avg[0] > Configure.preUV_x_point13_max;
						if (flag9)
						{
							this.p13_avgx_label.BackColor = this.fail;
						}
						else
						{
							this.p13_avgx_label.BackColor = this.pass;
						}
						bool flag10 = GlobalData.phrase2_colimatorPoint13Avg[1] < Configure.preUV_y_point13_min || GlobalData.phrase2_colimatorPoint13Avg[1] > Configure.preUV_y_point13_max;
						if (flag10)
						{
							this.p13_avgy_label.BackColor = this.fail;
						}
						else
						{
							this.p13_avgy_label.BackColor = this.pass;
						}
						this.p2_operate(1);
						double x = GlobalData.phrase2_colimatorPoint13Avg[0] - GlobalData.colimatorOrigDataP2[0];
						double x2 = GlobalData.phrase2_colimatorPoint13Avg[1] - GlobalData.colimatorOrigDataP2[1];
						double num2 = Math.Pow(x, 2.0) + Math.Pow(x2, 2.0);
						num2 = Math.Sqrt(num2);
						GlobalData.phrase2_colimatorPoint2[0] = GlobalData.colimatorOrigDataP2[0];
						GlobalData.phrase2_colimatorPoint2[1] = GlobalData.colimatorOrigDataP2[1];
						GlobalData.phrase2_colimatorDiff = num2;
						this.coli_diff_label.Text = Math.Round(num2, 3).ToString();
						bool flag11 = num2 > Configure.preUV_diff_min && num2 < Configure.preUV_diff_max;
						if (flag11)
						{
							this.coli_diff_label.BackColor = this.pass;
						}
						else
						{
							this.coli_diff_label.BackColor = this.fail;
						}
						bool flag12 = this.p1_x_label.BackColor == this.pass && this.p1_y_label.BackColor == this.pass && this.p3_x_label.BackColor == this.pass && this.p3_y_label.BackColor == this.pass && this.p13_avgx_label.BackColor == this.pass && this.p13_avgy_label.BackColor == this.pass && this.p2_x_label.BackColor == this.pass && this.p2_y_label.BackColor == this.pass && this.coli_diff_label.BackColor == this.pass && GlobalData.distance[0] >= Configure.uv_thickness_val_min && GlobalData.distance[0] <= Configure.uv_thickness_val_max;
						if (flag12)
						{
							bool flag13 = true;
							Logs.WriteInfo("开始自动UV", true);
							int num3;
							int num4;
							GSC_Controller.GetAxisStatus(out num3, out num4, out flag13);
							Logs.WriteInfo("z轴位置：" + num4.ToString(), true);
							ColimatorUVHandle.timer_stop = true;
							this.uv_phrase = 2;
							ZMotionController.StartUV();
							this.show_timer.Enabled = false;
							this.uv_timer.Interval = (double)Configure.uv_delay_time;
							this.uv_timer.Enabled = true;
						}
					}
				}));
			}
			else
			{
				this.show_timer.Enabled = false;
			}
		}

		// Token: 0x06000030 RID: 48 RVA: 0x00005518 File Offset: 0x00003718
		private void UV_Timer_Elapsed(object sender, ElapsedEventArgs e)
		{
			ZMotionController.StopUV();
			this.uv_timer.Enabled = false;
			bool flag = !ColimatorUVHandle.uv_timer_stop;
			if (flag)
			{
				base.Invoke(new MethodInvoker(delegate()
				{
					this.InitStatus();
					int num = this.FindEffectPoints();
					bool flag2 = num == 0;
					if (flag2)
					{
						this.colimator_p13_status_label.Text = "当前没有有效点";
						this.colimator_p13_status_label.ForeColor = Color.Red;
						this.p2_operate(2);
						GlobalData.result = "False";
						this.Finish();
					}
					else
					{
						this.colimator_p13_status_label.Text = "";
						this.colimator_p13_status_label.ForeColor = Color.Gray;
						bool flag3 = num == 1;
						if (flag3)
						{
							bool flag4 = GlobalData.phrase3_colimatorPoint1[0] != Configure.x_except_max;
							if (flag4)
							{
								GlobalData.phrase3_colimatorPoint3[0] = GlobalData.phrase3_colimatorPoint1[0];
								GlobalData.phrase3_colimatorPoint3[1] = GlobalData.phrase3_colimatorPoint1[1];
							}
							else
							{
								GlobalData.phrase3_colimatorPoint1[0] = GlobalData.phrase3_colimatorPoint3[0];
								GlobalData.phrase3_colimatorPoint1[1] = GlobalData.phrase3_colimatorPoint3[1];
							}
						}
						this.p1_x_label.Text = GlobalData.phrase3_colimatorPoint1[0].ToString("F4");
						this.p1_y_label.Text = GlobalData.phrase3_colimatorPoint1[1].ToString("F4");
						this.p3_x_label.Text = GlobalData.phrase3_colimatorPoint3[0].ToString("F4");
						this.p3_y_label.Text = GlobalData.phrase3_colimatorPoint3[1].ToString("F4");
						bool flag5 = GlobalData.phrase3_colimatorPoint1[0] < Configure.afterUV_x_point1_min || GlobalData.phrase3_colimatorPoint1[0] > Configure.afterUV_x_point1_max;
						if (flag5)
						{
							this.p1_x_label.BackColor = this.fail;
						}
						else
						{
							this.p1_x_label.BackColor = this.pass;
						}
						bool flag6 = GlobalData.phrase3_colimatorPoint1[1] < Configure.afterUV_y_point1_min || GlobalData.phrase3_colimatorPoint1[1] > Configure.afterUV_y_point1_max;
						if (flag6)
						{
							this.p1_y_label.BackColor = this.fail;
						}
						else
						{
							this.p1_y_label.BackColor = this.pass;
						}
						bool flag7 = GlobalData.phrase3_colimatorPoint3[0] < Configure.afterUV_x_point3_min || GlobalData.phrase3_colimatorPoint3[0] > Configure.afterUV_x_point3_max;
						if (flag7)
						{
							this.p3_x_label.BackColor = this.fail;
						}
						else
						{
							this.p3_x_label.BackColor = this.pass;
						}
						bool flag8 = GlobalData.phrase3_colimatorPoint3[1] < Configure.afterUV_y_point3_min || GlobalData.phrase3_colimatorPoint3[1] > Configure.afterUV_y_point3_max;
						if (flag8)
						{
							this.p3_y_label.BackColor = this.fail;
						}
						else
						{
							this.p3_y_label.BackColor = this.pass;
						}
						GlobalData.phrase3_colimatorPoint13Avg[0] = (GlobalData.phrase3_colimatorPoint1[0] + GlobalData.phrase3_colimatorPoint3[0]) / 2.0;
						GlobalData.phrase3_colimatorPoint13Avg[1] = (GlobalData.phrase3_colimatorPoint1[1] + GlobalData.phrase3_colimatorPoint3[1]) / 2.0;
						this.p13_avgx_label.Text = GlobalData.phrase3_colimatorPoint13Avg[0].ToString("F4");
						this.p13_avgy_label.Text = GlobalData.phrase3_colimatorPoint13Avg[1].ToString("F4");
						bool flag9 = GlobalData.phrase3_colimatorPoint13Avg[0] < Configure.afterUV_x_point13_min || GlobalData.phrase3_colimatorPoint13Avg[0] > Configure.afterUV_x_point13_max;
						if (flag9)
						{
							this.p13_avgx_label.BackColor = this.fail;
						}
						else
						{
							this.p13_avgx_label.BackColor = this.pass;
						}
						bool flag10 = GlobalData.phrase3_colimatorPoint13Avg[1] < Configure.afterUV_y_point13_min || GlobalData.phrase3_colimatorPoint13Avg[1] > Configure.afterUV_y_point13_max;
						if (flag10)
						{
							this.p13_avgy_label.BackColor = this.fail;
						}
						else
						{
							this.p13_avgy_label.BackColor = this.pass;
						}
						this.p2_operate(2);
						GlobalData.phrase3_colimatorPoint2[0] = GlobalData.colimatorOrigDataP2[0];
						GlobalData.phrase3_colimatorPoint2[1] = GlobalData.colimatorOrigDataP2[1];
						double x = GlobalData.phrase3_colimatorPoint13Avg[0] - GlobalData.colimatorOrigDataP2[0];
						double x2 = GlobalData.phrase3_colimatorPoint13Avg[1] - GlobalData.colimatorOrigDataP2[1];
						double num2 = Math.Pow(x, 2.0) + Math.Pow(x2, 2.0);
						num2 = Math.Sqrt(num2);
						this.coli_diff_label.Text = Math.Round(num2, 3).ToString();
						GlobalData.phrase3_colimatorDiff = num2;
						bool flag11 = num2 > Configure.afterUV_diff_min && num2 < Configure.afterUV_diff_max;
						if (flag11)
						{
							this.coli_diff_label.BackColor = this.pass;
						}
						else
						{
							this.coli_diff_label.BackColor = this.fail;
						}
						bool flag12 = this.p1_x_label.BackColor == this.pass && this.p1_y_label.BackColor == this.pass && this.p3_x_label.BackColor == this.pass && this.p3_y_label.BackColor == this.pass && this.p13_avgx_label.BackColor == this.pass && this.p13_avgy_label.BackColor == this.pass && this.p2_x_label.BackColor == this.pass && this.p2_y_label.BackColor == this.pass && this.coli_diff_label.BackColor == this.pass;
						if (flag12)
						{
							GlobalData.result = "True";
						}
						else
						{
							GlobalData.result = "False";
						}
						bool flag13 = this.uv_btn_click;
						if (flag13)
						{
							GlobalData.result = "False";
						}
						this.Finish();
					}
				}));
			}
			else
			{
				this.uv_timer.Enabled = false;
			}
		}

		// Token: 0x06000031 RID: 49 RVA: 0x0000556B File Offset: 0x0000376B
		private void p3_activate_btn_Click(object sender, EventArgs e)
		{
			this.check_flag = 2;
			this.colimator_p2_groupBox.Enabled = true;
			this.colimator_p1_groupBox.Enabled = false;
			this.show_timer.Enabled = true;
			this.p2_checked = true;
		}

		// Token: 0x06000032 RID: 50 RVA: 0x000055A4 File Offset: 0x000037A4
		private void uv_operate_button_Click(object sender, EventArgs e)
		{
			bool flag = this.uv_phrase == 2;
			if (!flag)
			{
				this.show_timer.Enabled = false;
				ColimatorUVHandle.timer_stop = true;
				bool flag2 = true;
				int num;
				int num2;
				GSC_Controller.GetAxisStatus(out num, out num2, out flag2);
				Logs.WriteInfo("z轴位置：" + num2.ToString(), true);
				Logs.WriteInfo("开始UV", true);
				ZMotionController.StartUV();
				this.uv_timer.Interval = (double)Configure.uv_delay_time;
				this.uv_timer.Enabled = true;
				this.uv_btn_click = true;
			}
		}

		// Token: 0x06000033 RID: 51 RVA: 0x00005634 File Offset: 0x00003834
		private void Finish()
		{
			bool flag = true;
			int num;
			int num2;
			GSC_Controller.GetAxisStatus(out num, out num2, out flag);
			Logs.WriteInfo("z轴位置：" + num2.ToString(), true);
			MotionZ.pulse = 0;
			MotionZ.ax_operate_sem.Release();
			ZMotionController.VacuummeterGasOutClose();
			Db.dataSaveIntoDB();
			base.Hide();
			this.entrance.Finish(this.sequence, 0);
		}

		// Token: 0x06000034 RID: 52 RVA: 0x000056A0 File Offset: 0x000038A0
		private void colimator_1_btn_Click(object sender, EventArgs e)
		{
			this.show_timer.Enabled = false;
			Colimator.serialPort = Colimator.serialPort1;
			Colimator.TurnOffColimator();
			Colimator.serialPort = Colimator.serialPort2;
			Colimator.TurnOffColimator();
			this.entrance.Finish(this.sequence, 0);
		}

		// Token: 0x04000043 RID: 67
		private Timer show_timer = new Timer();

		// Token: 0x04000044 RID: 68
		public static bool timer_stop;

		// Token: 0x04000045 RID: 69
		public static bool uv_timer_stop;

		// Token: 0x04000046 RID: 70
		private int check_flag = 0;

		// Token: 0x04000047 RID: 71
		private Color fail = Color.OrangeRed;

		// Token: 0x04000048 RID: 72
		private Color pass = Color.Green;

		// Token: 0x04000049 RID: 73
		private Color gray = Color.Gray;

		// Token: 0x0400004A RID: 74
		private bool p1_checked = false;

		// Token: 0x0400004B RID: 75
		private bool p2_checked = false;

		// Token: 0x0400004C RID: 76
		private Entrance entrance;

		// Token: 0x0400004D RID: 77
		private int sequence;

		// Token: 0x0400004E RID: 78
		private int type;

		// Token: 0x0400004F RID: 79
		private string title;

		// Token: 0x04000050 RID: 80
		private int uv_phrase = 0;

		// Token: 0x04000051 RID: 81
		private Timer uv_timer = new Timer();

		// Token: 0x04000052 RID: 82
		private bool uv_btn_click = false;
	}
}
