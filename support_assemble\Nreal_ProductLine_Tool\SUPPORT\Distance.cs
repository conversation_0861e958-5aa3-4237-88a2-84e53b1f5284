﻿using System;
using System.Threading;

namespace Nreal_ProductLine_Tool.SUPPORT
{
	// Token: 0x0200000A RID: 10
	public class Distance
	{
		// Token: 0x0600003D RID: 61 RVA: 0x00007F28 File Offset: 0x00006128
		public static int Init()
		{
			Distance.m_Dist = new cDistance();
			bool flag = Distance.m_Dist == null;
			int result;
			if (flag)
			{
				result = -1;
			}
			else
			{
				bool flag2 = !Distance.m_Dist.Init();
				if (flag2)
				{
					result = -2;
				}
				else
				{
					bool flag3 = !Distance.m_Dist.Open(19);
					if (flag3)
					{
						result = -3;
					}
					else
					{
						Distance.m_Dist.SetParameter();
						Distance.dist_operate_sem = new SemaphoreSlim(0, 1);
						Logs.WriteInfo("StartAcquisition", true);
						bool flag4 = Distance.m_Dist.StartAcquisition();
						if (flag4)
						{
							Thread thread = new Thread(new ThreadStart(Distance.m_Dist.Execute));
							thread.Start();
							Thread thread2 = new Thread(new ThreadStart(Distance.m_Dist.Monitor));
							thread2.Start();
						}
						else
						{
							Logs.WriteInfo("StartAcquisition : Error : New", true);
						}
						result = 0;
					}
				}
			}
			return result;
		}

		// Token: 0x0600003E RID: 62 RVA: 0x00008011 File Offset: 0x00006211
		public static void dist_operate()
		{
		}

		// Token: 0x0600003F RID: 63 RVA: 0x00008014 File Offset: 0x00006214
		public static void dist_measure_start()
		{
			Distance.dist_operate_sem.Release();
		}

		// Token: 0x06000040 RID: 64 RVA: 0x00008022 File Offset: 0x00006222
		public static void dist_measure_stop()
		{
			Logs.WriteInfo("dist_measure_stop", true);
			Distance.m_Dist.StopAcquisition();
			Logs.WriteInfo("dist_measure_stop end", true);
		}

		// Token: 0x04000071 RID: 113
		public static cDistance m_Dist;

		// Token: 0x04000072 RID: 114
		public static SemaphoreSlim dist_operate_sem;
	}
}
