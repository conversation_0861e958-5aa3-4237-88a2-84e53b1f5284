﻿using System;
using System.Runtime.InteropServices;
using System.Text;

namespace Nreal_ProductLine_Tool.SUPPORT
{
	// Token: 0x02000023 RID: 35
	public class zmcaux
	{
		// Token: 0x06000160 RID: 352
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Execute(IntPtr handle, string pszCommand, StringBuilder psResponse, uint uiResponseLength);

		// Token: 0x06000161 RID: 353
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_DirectCommand(IntPtr handle, string pszCommand, StringBuilder psResponse, uint uiResponseLength);

		// Token: 0x06000162 RID: 354
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_SetTraceFile(int bifTofile, string pFilePathName);

		// Token: 0x06000163 RID: 355
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_OpenCom(uint comid, out IntPtr phandle);

		// Token: 0x06000164 RID: 356
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_SearchAndOpenCom(uint uimincomidfind, uint uimaxcomidfind, ref uint pcomid, uint uims, out IntPtr phandle);

		// Token: 0x06000165 RID: 357
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_SetComDefaultBaud(uint dwbaudRate, uint dwByteSize, uint dwParity, uint dwStopBits);

		// Token: 0x06000166 RID: 358
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_SetIp(IntPtr handle, string ipaddress);

		// Token: 0x06000167 RID: 359
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_OpenEth(string ipaddr, out IntPtr phandle);

		// Token: 0x06000168 RID: 360
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_SearchEthlist(StringBuilder ipaddrlist, uint addrbufflength, uint uims);

		// Token: 0x06000169 RID: 361
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_SearchEth(string ipaddress, uint uims);

		// Token: 0x0600016A RID: 362
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Close(IntPtr handle);

		// Token: 0x0600016B RID: 363
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Resume(IntPtr handle);

		// Token: 0x0600016C RID: 364
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Pause(IntPtr handle);

		// Token: 0x0600016D RID: 365
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_BasDown(IntPtr handle, string Filename, uint run_mode);

		// Token: 0x0600016E RID: 366
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetIn(IntPtr handle, int ionum, ref uint piValue);

		// Token: 0x0600016F RID: 367
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_SetOp(IntPtr handle, int ionum, uint iValue);

		// Token: 0x06000170 RID: 368
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetOp(IntPtr handle, int ionum, ref uint piValue);

		// Token: 0x06000171 RID: 369
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetAD(IntPtr handle, int ionum, ref double pfValue);

		// Token: 0x06000172 RID: 370
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_SetDA(IntPtr handle, int ionum, double fValue);

		// Token: 0x06000173 RID: 371
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetDA(IntPtr handle, int ionum, ref double pfValue);

		// Token: 0x06000174 RID: 372
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_SetInvertIn(IntPtr handle, int ionum, int bifInvert);

		// Token: 0x06000175 RID: 373
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetInvertIn(IntPtr handle, int ionum, ref int piValue);

		// Token: 0x06000176 RID: 374
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_SetPwmFreq(IntPtr handle, int ionum, double fValue);

		// Token: 0x06000177 RID: 375
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetPwmFreq(IntPtr handle, int ionum, ref double pfValue);

		// Token: 0x06000178 RID: 376
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_SetPwmDuty(IntPtr handle, int ionum, double fValue);

		// Token: 0x06000179 RID: 377
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetPwmDuty(IntPtr handle, int ionum, ref double pfValue);

		// Token: 0x0600017A RID: 378
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_GetModbusIn(IntPtr handle, int ionumfirst, int ionumend, out byte[] pValueList);

		// Token: 0x0600017B RID: 379
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_GetModbusOut(IntPtr handle, int ionumfirst, int ionumend, out byte[] pValueList);

		// Token: 0x0600017C RID: 380
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_GetModbusDpos(IntPtr handle, int imaxaxises, double[] pValueList);

		// Token: 0x0600017D RID: 381
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_GetModbusMpos(IntPtr handle, int imaxaxises, double[] pValueList);

		// Token: 0x0600017E RID: 382
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_GetModbusCurSpeed(IntPtr handle, int imaxaxises, double[] pValueList);

		// Token: 0x0600017F RID: 383
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_SetParam(IntPtr handle, string sParam, int iaxis, double fset);

		// Token: 0x06000180 RID: 384
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetParam(IntPtr handle, string sParam, int iaxis, ref double pfValue);

		// Token: 0x06000181 RID: 385
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_SetAccel(IntPtr handle, int iaxis, double fValue);

		// Token: 0x06000182 RID: 386
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetAccel(IntPtr handle, int iaxis, ref double pfValue);

		// Token: 0x06000183 RID: 387
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetAddax(IntPtr handle, int iaxis, ref int piValue);

		// Token: 0x06000184 RID: 388
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_SetAlmIn(IntPtr handle, int iaxis, int iValue);

		// Token: 0x06000185 RID: 389
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetAlmIn(IntPtr handle, int iaxis, ref int piValue);

		// Token: 0x06000186 RID: 390
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_SetAtype(IntPtr handle, int iaxis, int iValue);

		// Token: 0x06000187 RID: 391
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetAtype(IntPtr handle, int iaxis, ref int piValue);

		// Token: 0x06000188 RID: 392
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetAxisStatus(IntPtr handle, int iaxis, ref int piValue);

		// Token: 0x06000189 RID: 393
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_SetAxisAddress(IntPtr handle, int iaxis, int iValue);

		// Token: 0x0600018A RID: 394
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetAxisAddress(IntPtr handle, int iaxis, ref int piValue);

		// Token: 0x0600018B RID: 395
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_SetAxisEnable(IntPtr handle, int iaxis, int iValue);

		// Token: 0x0600018C RID: 396
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetAxisEnable(IntPtr handle, int iaxis, ref int piValue);

		// Token: 0x0600018D RID: 397
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_SetClutchRate(IntPtr handle, int iaxis, double fValue);

		// Token: 0x0600018E RID: 398
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetClutchRate(IntPtr handle, int iaxis, ref double pfValue);

		// Token: 0x0600018F RID: 399
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_SetCloseWin(IntPtr handle, int iaxis, double fValue);

		// Token: 0x06000190 RID: 400
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetCloseWin(IntPtr handle, int iaxis, ref double pfValue);

		// Token: 0x06000191 RID: 401
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_SetCornerMode(IntPtr handle, int iaxis, int pfValue);

		// Token: 0x06000192 RID: 402
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetCornerMode(IntPtr handle, int iaxis, ref int piValue);

		// Token: 0x06000193 RID: 403
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_SetCreep(IntPtr handle, int iaxis, double fValue);

		// Token: 0x06000194 RID: 404
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetCreep(IntPtr handle, int iaxis, ref double pfValue);

		// Token: 0x06000195 RID: 405
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_SetDatumIn(IntPtr handle, int iaxis, int iValue);

		// Token: 0x06000196 RID: 406
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetDatumIn(IntPtr handle, int iaxis, ref int piValue);

		// Token: 0x06000197 RID: 407
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_SetDecel(IntPtr handle, int iaxis, double fValue);

		// Token: 0x06000198 RID: 408
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetDecel(IntPtr handle, int iaxis, ref double pfValue);

		// Token: 0x06000199 RID: 409
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_SetDecelAngle(IntPtr handle, int iaxis, double fValue);

		// Token: 0x0600019A RID: 410
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetDecelAngle(IntPtr handle, int iaxis, ref double pfValue);

		// Token: 0x0600019B RID: 411
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_SetDpos(IntPtr handle, int iaxis, double fValue);

		// Token: 0x0600019C RID: 412
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetDpos(IntPtr handle, int iaxis, ref double pfValue);

		// Token: 0x0600019D RID: 413
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetEncoder(IntPtr handle, int iaxis, ref double pfValue);

		// Token: 0x0600019E RID: 414
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetEndMove(IntPtr handle, int iaxis, ref double pfValue);

		// Token: 0x0600019F RID: 415
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetEndMoveBuffer(IntPtr handle, int iaxis, ref double pfValue);

		// Token: 0x060001A0 RID: 416
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_SetEndMoveSpeed(IntPtr handle, int iaxis, double fValue);

		// Token: 0x060001A1 RID: 417
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetEndMoveSpeed(IntPtr handle, int iaxis, ref double pfValue);

		// Token: 0x060001A2 RID: 418
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_SetErrormask(IntPtr handle, int iaxis, int iValue);

		// Token: 0x060001A3 RID: 419
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetErrormask(IntPtr handle, int iaxis, ref int piValue);

		// Token: 0x060001A4 RID: 420
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_SetFastJog(IntPtr handle, int iaxis, int iValue);

		// Token: 0x060001A5 RID: 421
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetFastJog(IntPtr handle, int iaxis, ref int piValue);

		// Token: 0x060001A6 RID: 422
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_SetFastDec(IntPtr handle, int iaxis, double iValue);

		// Token: 0x060001A7 RID: 423
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetFastDec(IntPtr handle, int iaxis, ref double pfValue);

		// Token: 0x060001A8 RID: 424
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetFe(IntPtr handle, int iaxis, ref double pfValue);

		// Token: 0x060001A9 RID: 425
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_SetFeLimit(IntPtr handle, int iaxis, double fValue);

		// Token: 0x060001AA RID: 426
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetFeLimit(IntPtr handle, int iaxis, ref double pfValue);

		// Token: 0x060001AB RID: 427
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_SetFRange(IntPtr handle, int iaxis, double fValue);

		// Token: 0x060001AC RID: 428
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetFeRange(IntPtr handle, int iaxis, ref double fValue);

		// Token: 0x060001AD RID: 429
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_SetFholdIn(IntPtr handle, int iaxis, int iValue);

		// Token: 0x060001AE RID: 430
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetFholdIn(IntPtr handle, int iaxis, ref int piValue);

		// Token: 0x060001AF RID: 431
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_SetFhspeed(IntPtr handle, int iaxis, double fValue);

		// Token: 0x060001B0 RID: 432
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetFhspeed(IntPtr handle, int iaxis, ref double pfValue);

		// Token: 0x060001B1 RID: 433
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_SetForceSpeed(IntPtr handle, int iaxis, double fValue);

		// Token: 0x060001B2 RID: 434
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetForceSpeed(IntPtr handle, int iaxis, ref double pfValue);

		// Token: 0x060001B3 RID: 435
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_SetFsLimit(IntPtr handle, int iaxis, double fValue);

		// Token: 0x060001B4 RID: 436
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetFsLimit(IntPtr handle, int iaxis, ref double pfValue);

		// Token: 0x060001B5 RID: 437
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_SetFullSpRadius(IntPtr handle, int iaxis, double fValue);

		// Token: 0x060001B6 RID: 438
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetFullSpRadius(IntPtr handle, int iaxis, ref double pfValue);

		// Token: 0x060001B7 RID: 439
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_SetFwdIn(IntPtr handle, int iaxis, int iValue);

		// Token: 0x060001B8 RID: 440
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetFwdIn(IntPtr handle, int iaxis, ref int piValue);

		// Token: 0x060001B9 RID: 441
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_SetFwdJog(IntPtr handle, int iaxis, int iValue);

		// Token: 0x060001BA RID: 442
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetFwdJog(IntPtr handle, int iaxis, ref int piValue);

		// Token: 0x060001BB RID: 443
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetIfIdle(IntPtr handle, int iaxis, ref int piValue);

		// Token: 0x060001BC RID: 444
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_SetInvertStep(IntPtr handle, int iaxis, int iValue);

		// Token: 0x060001BD RID: 445
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetInvertStep(IntPtr handle, int iaxis, ref int piValue);

		// Token: 0x060001BE RID: 446
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_SetInterpFactor(IntPtr handle, int iaxis, int iValue);

		// Token: 0x060001BF RID: 447
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetInterpFactor(IntPtr handle, int iaxis, ref int piValue);

		// Token: 0x060001C0 RID: 448
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_SetJogSpeed(IntPtr handle, int iaxis, double fValue);

		// Token: 0x060001C1 RID: 449
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetJogSpeed(IntPtr handle, int iaxis, ref double pfValue);

		// Token: 0x060001C2 RID: 450
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetLinkax(IntPtr handle, int iaxis, ref int piValue);

		// Token: 0x060001C3 RID: 451
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetLoaded(IntPtr handle, int iaxis, ref int piValue);

		// Token: 0x060001C4 RID: 452
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_SetLspeed(IntPtr handle, int iaxis, double fValue);

		// Token: 0x060001C5 RID: 453
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetLspeed(IntPtr handle, int iaxis, ref double pfValue);

		// Token: 0x060001C6 RID: 454
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_SetHomeWait(IntPtr handle, int iaxis, int fValue);

		// Token: 0x060001C7 RID: 455
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetHomeWait(IntPtr handle, int iaxis, ref int pfValue);

		// Token: 0x060001C8 RID: 456
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetMark(IntPtr handle, int iaxis, ref int piValue);

		// Token: 0x060001C9 RID: 457
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetMarkB(IntPtr handle, int iaxis, ref int piValue);

		// Token: 0x060001CA RID: 458
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_SetMaxSpeed(IntPtr handle, int iaxis, int iValue);

		// Token: 0x060001CB RID: 459
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetMaxSpeed(IntPtr handle, int iaxis, ref int piValue);

		// Token: 0x060001CC RID: 460
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_SetMerge(IntPtr handle, int iaxis, int iValue);

		// Token: 0x060001CD RID: 461
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetMerge(IntPtr handle, int iaxis, ref int piValue);

		// Token: 0x060001CE RID: 462
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetMovesBuffered(IntPtr handle, int iaxis, ref int piValue);

		// Token: 0x060001CF RID: 463
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetMoveCurmark(IntPtr handle, int iaxis, ref int piValue);

		// Token: 0x060001D0 RID: 464
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_SetMovemark(IntPtr handle, int iaxis, int iValue);

		// Token: 0x060001D1 RID: 465
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_SetMpos(IntPtr handle, int iaxis, double fValue);

		// Token: 0x060001D2 RID: 466
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetMpos(IntPtr handle, int iaxis, ref double pfValue);

		// Token: 0x060001D3 RID: 467
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetMspeed(IntPtr handle, int iaxis, ref double pfValue);

		// Token: 0x060001D4 RID: 468
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetMtype(IntPtr handle, int iaxis, ref int piValue);

		// Token: 0x060001D5 RID: 469
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetNtype(IntPtr handle, int iaxis, ref int piValue);

		// Token: 0x060001D6 RID: 470
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_SetOffpos(IntPtr handle, int iaxis, double fValue);

		// Token: 0x060001D7 RID: 471
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetOffpos(IntPtr handle, int iaxis, ref double pfValue);

		// Token: 0x060001D8 RID: 472
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_SetOpenWin(IntPtr handle, int iaxis, double fValue);

		// Token: 0x060001D9 RID: 473
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetOpenWin(IntPtr handle, int iaxis, ref double pfValue);

		// Token: 0x060001DA RID: 474
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetRegPos(IntPtr handle, int iaxis, ref double pfValue);

		// Token: 0x060001DB RID: 475
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetRegPosB(IntPtr handle, int iaxis, ref double pfValue);

		// Token: 0x060001DC RID: 476
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetRemain(IntPtr handle, int iaxis, ref double pfValue);

		// Token: 0x060001DD RID: 477
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetRemain_LineBuffer(IntPtr handle, int iaxis, ref int piValue);

		// Token: 0x060001DE RID: 478
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetRemain_Buffer(IntPtr handle, int iaxis, ref int piValue);

		// Token: 0x060001DF RID: 479
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_SetRepDist(IntPtr handle, int iaxis, double fValue);

		// Token: 0x060001E0 RID: 480
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetRepDist(IntPtr handle, int iaxis, ref double pfValue);

		// Token: 0x060001E1 RID: 481
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_SetRepOption(IntPtr handle, int iaxis, int iValue);

		// Token: 0x060001E2 RID: 482
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetRepOption(IntPtr handle, int iaxis, ref int piValue);

		// Token: 0x060001E3 RID: 483
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_SetRevIn(IntPtr handle, int iaxis, int iValue);

		// Token: 0x060001E4 RID: 484
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetRevIn(IntPtr handle, int iaxis, ref int piValue);

		// Token: 0x060001E5 RID: 485
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_SetRevJog(IntPtr handle, int iaxis, int iValue);

		// Token: 0x060001E6 RID: 486
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetRevJog(IntPtr handle, int iaxis, ref int piValue);

		// Token: 0x060001E7 RID: 487
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_SetRsLimit(IntPtr handle, int iaxis, double fValue);

		// Token: 0x060001E8 RID: 488
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetRsLimit(IntPtr handle, int iaxis, ref double pfValue);

		// Token: 0x060001E9 RID: 489
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_SetSpeed(IntPtr handle, int iaxis, double fValue);

		// Token: 0x060001EA RID: 490
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetSpeed(IntPtr handle, int iaxis, ref double pfValue);

		// Token: 0x060001EB RID: 491
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_SetSramp(IntPtr handle, int iaxis, double fValue);

		// Token: 0x060001EC RID: 492
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetSramp(IntPtr handle, int iaxis, ref double pfValue);

		// Token: 0x060001ED RID: 493
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_SetStartMoveSpeed(IntPtr handle, int iaxis, double fValue);

		// Token: 0x060001EE RID: 494
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetStartMoveSpeed(IntPtr handle, int iaxis, ref double pfValue);

		// Token: 0x060001EF RID: 495
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_SetStopAngle(IntPtr handle, int iaxis, double fValue);

		// Token: 0x060001F0 RID: 496
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetStopAngle(IntPtr handle, int iaxis, ref double pfValue);

		// Token: 0x060001F1 RID: 497
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_SetZsmooth(IntPtr handle, int iaxis, double fValue);

		// Token: 0x060001F2 RID: 498
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetZsmooth(IntPtr handle, int iaxis, ref double pfValue);

		// Token: 0x060001F3 RID: 499
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_SetUnits(IntPtr handle, int iaxis, double fValue);

		// Token: 0x060001F4 RID: 500
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetUnits(IntPtr handle, int iaxis, ref double pfValue);

		// Token: 0x060001F5 RID: 501
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetVectorBuffered(IntPtr handle, int iaxis, ref double pfValue);

		// Token: 0x060001F6 RID: 502
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetVpSpeed(IntPtr handle, int iaxis, ref double pfValue);

		// Token: 0x060001F7 RID: 503
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetVariablef(IntPtr handle, string pname, ref double pfValue);

		// Token: 0x060001F8 RID: 504
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetVariableInt(IntPtr handle, string pname, ref int piValue);

		// Token: 0x060001F9 RID: 505
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_Base(IntPtr handle, int imaxaxises, int[] piAxislist);

		// Token: 0x060001FA RID: 506
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_Defpos(IntPtr handle, int iaxis, double pfDpos);

		// Token: 0x060001FB RID: 507
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_Move(IntPtr handle, int imaxaxises, int[] piAxislist, double[] pfDposlist);

		// Token: 0x060001FC RID: 508
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_MoveSp(IntPtr handle, int imaxaxises, int[] piAxislist, double[] pfDposlist);

		// Token: 0x060001FD RID: 509
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_MoveAbs(IntPtr handle, int imaxaxises, int[] piAxislist, double[] pfDposlist);

		// Token: 0x060001FE RID: 510
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_MoveAbsSp(IntPtr handle, int imaxaxises, int[] piAxislist, double[] pfDposlist);

		// Token: 0x060001FF RID: 511
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_MoveModify(IntPtr handle, int iaxis, double pfDisance);

		// Token: 0x06000200 RID: 512
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_MoveCirc(IntPtr handle, int imaxaxises, int[] piAxislist, double fend1, double fend2, double fcenter1, double fcenter2, int idirection);

		// Token: 0x06000201 RID: 513
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_MoveCircSp(IntPtr handle, int imaxaxises, int[] piAxislist, double fend1, double fend2, double fcenter1, double fcenter2, int idirection);

		// Token: 0x06000202 RID: 514
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_MoveCircAbs(IntPtr handle, int imaxaxises, int[] piAxislist, double fend1, double fend2, double fcenter1, double fcenter2, int idirection);

		// Token: 0x06000203 RID: 515
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_MoveCircAbsSp(IntPtr handle, int imaxaxises, int[] piAxislist, double fend1, double fend2, double fcenter1, double fcenter2, int idirection);

		// Token: 0x06000204 RID: 516
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_MoveCirc2(IntPtr handle, int imaxaxises, int[] piAxislist, double fmid1, double fmid2, double fend1, double fend2);

		// Token: 0x06000205 RID: 517
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_MoveCirc2Abs(IntPtr handle, int imaxaxises, int[] piAxislist, double fmid1, double fmid2, double fend1, double fend2);

		// Token: 0x06000206 RID: 518
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_MoveCirc2Sp(IntPtr handle, int imaxaxises, int[] piAxislist, double fmid1, double fmid2, double fend1, double fend2);

		// Token: 0x06000207 RID: 519
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_MoveCirc2AbsSp(IntPtr handle, int imaxaxises, int[] piAxislist, double fmid1, double fmid2, double fend1, double fend2);

		// Token: 0x06000208 RID: 520
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_MHelical(IntPtr handle, int imaxaxises, int[] piAxislist, double fend1, double fend2, double fcenter1, double fcenter2, int idirection, double fDistance3, int imode);

		// Token: 0x06000209 RID: 521
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_MHelicalAbs(IntPtr handle, int imaxaxises, int[] piAxislist, double fend1, double fend2, double fcenter1, double fcenter2, int idirection, double fDistance3, int imode);

		// Token: 0x0600020A RID: 522
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_MHelicalSp(IntPtr handle, int imaxaxises, int[] piAxislist, double fend1, double fend2, double fcenter1, double fcenter2, int idirection, double fDistance3, int imode);

		// Token: 0x0600020B RID: 523
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_MHelicalAbsSp(IntPtr handle, int imaxaxises, int[] piAxislist, double fend1, double fend2, double fcenter1, double fcenter2, int idirection, double fDistance3, int imode);

		// Token: 0x0600020C RID: 524
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_MHelical2(IntPtr handle, int imaxaxises, int[] piAxislist, double fmid1, double fmid2, double fend1, double fend2, double fDistance3, int imode);

		// Token: 0x0600020D RID: 525
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_MHelical2Abs(IntPtr handle, int imaxaxises, int[] piAxislist, double fmid1, double fmid2, double fend1, double fend2, double fDistance3, int imode);

		// Token: 0x0600020E RID: 526
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_MHelical2Sp(IntPtr handle, int imaxaxises, int[] piAxislist, double fmid1, double fmid2, double fend1, double fend2, double fDistance3, int imode);

		// Token: 0x0600020F RID: 527
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_MHelical2AbsSp(IntPtr handle, int imaxaxises, int[] piAxislist, double fmid1, double fmid2, double fend1, double fend2, double fDistance3, int imode);

		// Token: 0x06000210 RID: 528
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_MEclipse(IntPtr handle, int imaxaxises, int[] piAxislist, double fend1, double fend2, double fcenter1, double fcenter2, int idirection, double fADis, double fBDis);

		// Token: 0x06000211 RID: 529
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_MEclipseAbs(IntPtr handle, int imaxaxises, int[] piAxislist, double fend1, double fend2, double fcenter1, double fcenter2, int idirection, double fADis, double fBDis);

		// Token: 0x06000212 RID: 530
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_MEclipseSp(IntPtr handle, int imaxaxises, int[] piAxislist, double fend1, double fend2, double fcenter1, double fcenter2, int idirection, double fADis, double fBDis);

		// Token: 0x06000213 RID: 531
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_MEclipseAbsSp(IntPtr handle, int imaxaxises, int[] piAxislist, double fend1, double fend2, double fcenter1, double fcenter2, int idirection, double fADis, double fBDis);

		// Token: 0x06000214 RID: 532
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_MEclipseHelical(IntPtr handle, int imaxaxises, int[] piAxislist, double fend1, double fend2, double fcenter1, double fcenter2, int idirection, double fADis, double fBDis, double fDistance3);

		// Token: 0x06000215 RID: 533
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_MEclipseHelicalAbs(IntPtr handle, int imaxaxises, int[] piAxislist, double fend1, double fend2, double fcenter1, double fcenter2, int idirection, double fADis, double fBDis, double fDistance3);

		// Token: 0x06000216 RID: 534
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_MEclipseHelicalSp(IntPtr handle, int imaxaxises, int[] piAxislist, double fend1, double fend2, double fcenter1, double fcenter2, int idirection, double fADis, double fBDis, double fDistance3);

		// Token: 0x06000217 RID: 535
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_MEclipseHelicalAbsSp(IntPtr handle, int imaxaxises, int[] piAxislist, double fend1, double fend2, double fcenter1, double fcenter2, int idirection, double fADis, double fBDis, double fDistance3);

		// Token: 0x06000218 RID: 536
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_MSpherical(IntPtr handle, int imaxaxises, int[] piAxislist, double fend1, double fend2, double fend3, double fcenter1, double fcenter2, double fcenter3, int imode, double fcenter4, double fcenter5);

		// Token: 0x06000219 RID: 537
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_MSphericalSp(IntPtr handle, int imaxaxises, int[] piAxislist, double fend1, double fend2, double fend3, double fcenter1, double fcenter2, double fcenter3, int imode, double fcenter4, double fcenter5);

		// Token: 0x0600021A RID: 538
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_MoveSpiral(IntPtr handle, int imaxaxises, int[] piAxislist, double centre1, double centre2, double circles, double pitch, double distance3, double distance4);

		// Token: 0x0600021B RID: 539
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_MoveSpiralSp(IntPtr handle, int imaxaxises, int[] piAxislist, double centre1, double centre2, double circles, double pitch, double distance3, double distance4);

		// Token: 0x0600021C RID: 540
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_MoveSmooth(IntPtr handle, int imaxaxises, int[] piAxislist, double end1, double end2, double end3, double next1, double next2, double next3, double radius);

		// Token: 0x0600021D RID: 541
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_MoveSmoothSp(IntPtr handle, int imaxaxises, int[] piAxislist, double end1, double end2, double end3, double next1, double next2, double next3, double radius);

		// Token: 0x0600021E RID: 542
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_MovePause(IntPtr handle, int iaxis, int imode);

		// Token: 0x0600021F RID: 543
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_MoveResume(IntPtr handle, int iaxis);

		// Token: 0x06000220 RID: 544
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_MoveLimit(IntPtr handle, int iaxis, double limitspeed);

		// Token: 0x06000221 RID: 545
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_MoveOp(IntPtr handle, int iaxis, int ioutnum, int ivalue);

		// Token: 0x06000222 RID: 546
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_MoveOpMulti(IntPtr handle, int iaxis, int ioutnumfirst, int ioutnumend, int ivalue);

		// Token: 0x06000223 RID: 547
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_MoveOp2(IntPtr handle, int iaxis, int ioutnum, int ivalue, int iofftimems);

		// Token: 0x06000224 RID: 548
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_MoveAout(IntPtr handle, int iaxis, int ioutnum, double fvalue);

		// Token: 0x06000225 RID: 549
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_MoveDelay(IntPtr handle, int iaxis, int itimems);

		// Token: 0x06000226 RID: 550
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_MoveTurnabs(IntPtr handle, int tablenum, int imaxaxises, int[] piAxislist, double[] pfDisancelist);

		// Token: 0x06000227 RID: 551
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_McircTurnabs(IntPtr handle, int tablenum, double refpos1, double refpos2, int mode, double end1, double end2, int imaxaxises, int[] piAxislist, uint pfDisancelistNumber, double[] pfDisancelist);

		// Token: 0x06000228 RID: 552
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_Cam(IntPtr handle, int iaxis, int istartpoint, int iendpoint, double ftablemulti, double fDistance);

		// Token: 0x06000229 RID: 553
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_Cambox(IntPtr handle, int iaxis, int istartpoint, int iendpoint, double ftablemulti, double fDistance, int ilinkaxis, int ioption, double flinkstartpos);

		// Token: 0x0600022A RID: 554
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_Movelink(IntPtr handle, int iaxis, double fDistance, double fLinkDis, double fLinkAcc, double fLinkDec, int iLinkaxis, int ioption, double flinkstartpos);

		// Token: 0x0600022B RID: 555
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_Moveslink(IntPtr handle, int iaxis, double fDistance, double fLinkDis, double startsp, double endsp, int iLinkaxis, int ioption, double flinkstartpos);

		// Token: 0x0600022C RID: 556
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_Connect(IntPtr handle, double ratio, int link_axis, int move_axis);

		// Token: 0x0600022D RID: 557
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_Connpath(IntPtr handle, double ratio, int link_axis, int move_axis);

		// Token: 0x0600022E RID: 558
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_Regist(IntPtr handle, int iaxis, int imode);

		// Token: 0x0600022F RID: 559
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_EncoderRatio(IntPtr handle, int iaxis, int mpos_count, int input_count);

		// Token: 0x06000230 RID: 560
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_StepRatio(IntPtr handle, int iaxis, int mpos_count, int input_count);

		// Token: 0x06000231 RID: 561
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_Rapidstop(IntPtr handle, int imode);

		// Token: 0x06000232 RID: 562
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_CancelAxisList(IntPtr handle, int imaxaxises, int[] piAxislist, int imode);

		// Token: 0x06000233 RID: 563
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_Connframe(IntPtr handle, int Jogmaxaxises, int[] JogAxislist, int frame, int tablenum, int Virmaxaxises, int[] VirAxislist);

		// Token: 0x06000234 RID: 564
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_Connreframe(IntPtr handle, int Virmaxaxises, int[] VirAxislist, int frame, int tablenum, int Jogmaxaxises, int[] JogAxislist);

		// Token: 0x06000235 RID: 565
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_Single_Addax(IntPtr handle, int iaxis, int iaddaxis);

		// Token: 0x06000236 RID: 566
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_Single_Cancel(IntPtr handle, int iaxis, int imode);

		// Token: 0x06000237 RID: 567
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_Single_Vmove(IntPtr handle, int iaxis, int idir);

		// Token: 0x06000238 RID: 568
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_Single_Datum(IntPtr handle, int iaxis, int imode);

		// Token: 0x06000239 RID: 569
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetHomeStatus(IntPtr handle, int iaxis, ref uint homestatus);

		// Token: 0x0600023A RID: 570
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_Single_Move(IntPtr handle, int iaxis, double fdistance);

		// Token: 0x0600023B RID: 571
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_Single_MoveAbs(IntPtr handle, int iaxis, double fdistance);

		// Token: 0x0600023C RID: 572
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_SetVrf(IntPtr handle, int vrstartnum, int numes, double[] pfValue);

		// Token: 0x0600023D RID: 573
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetVrf(IntPtr handle, int vrstartnum, int numes, double[] pfValue);

		// Token: 0x0600023E RID: 574
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetVrInt(IntPtr handle, int vrstartnum, int numes, int[] piValue);

		// Token: 0x0600023F RID: 575
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_SetTable(IntPtr handle, int vrstartnum, int numes, double[] pfValue);

		// Token: 0x06000240 RID: 576
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetTable(IntPtr handle, int tabstart, int numes, double[] pfValue);

		// Token: 0x06000241 RID: 577
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_TransStringtoDouble(string pstringin, int inumes, double[] pfValue);

		// Token: 0x06000242 RID: 578
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_TransStringtoInt(string pstringin, int inumes, int[] pfValue);

		// Token: 0x06000243 RID: 579
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_WriteUFile(string sFilename, double[] pVarlist, int inum);

		// Token: 0x06000244 RID: 580
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_ReadUFile(string sFilename, double[] pVarlist, ref int inum);

		// Token: 0x06000245 RID: 581
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Modbus_Set0x(IntPtr handle, ushort start, ushort inum, byte[] pdata);

		// Token: 0x06000246 RID: 582
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Modbus_Get0x(IntPtr handle, ushort start, ushort inum, byte[] pdata);

		// Token: 0x06000247 RID: 583
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Modbus_Set4x(IntPtr handle, ushort start, ushort inum, ushort[] pfdata);

		// Token: 0x06000248 RID: 584
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Modbus_Get4x(IntPtr handle, ushort start, ushort inum, ushort[] pfdata);

		// Token: 0x06000249 RID: 585
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Modbus_Get4x_Double(IntPtr handle, ushort start, ushort inum, double[] pfdata);

		// Token: 0x0600024A RID: 586
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Modbus_Set4x_Double(IntPtr handle, ushort start, ushort inum, double[] pfdata);

		// Token: 0x0600024B RID: 587
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Modbus_Get4x_Long(IntPtr handle, ushort start, ushort inum, int[] pfdata);

		// Token: 0x0600024C RID: 588
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Modbus_Set4x_Long(IntPtr handle, ushort start, ushort inum, int[] pfdata);

		// Token: 0x0600024D RID: 589
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Modbus_Get4x_String(IntPtr handle, ushort start, ushort inum, StringBuilder pfdata);

		// Token: 0x0600024E RID: 590
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Modbus_Set4x_String(IntPtr handle, ushort start, ushort inum, string pfdata);

		// Token: 0x0600024F RID: 591
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_FlashWritef(IntPtr handle, ushort uiflashid, uint uinumes, float[] pfvlue);

		// Token: 0x06000250 RID: 592
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_FlashReadf(IntPtr handle, ushort uiflashid, uint uibuffnum, float[] pfvlue, ref uint puinumesread);

		// Token: 0x06000251 RID: 593
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Trigger(IntPtr handle);

		// Token: 0x06000252 RID: 594
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_MovePara(IntPtr handle, uint base_axis, string paraname, uint iaxis, double fvalue);

		// Token: 0x06000253 RID: 595
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_MovePwm(IntPtr handle, uint base_axis, uint pwm_num, double pwm_duty, double pwm_freq);

		// Token: 0x06000254 RID: 596
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_MoveSynmove(IntPtr handle, uint base_axis, uint iaxis, double fdist, uint ifsp);

		// Token: 0x06000255 RID: 597
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_MoveASynmove(IntPtr handle, uint base_axis, uint iaxis, double fdist, uint ifsp);

		// Token: 0x06000256 RID: 598
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_MoveTable(IntPtr handle, uint base_axis, uint table_num, double fvalue);

		// Token: 0x06000257 RID: 599
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_MoveWait(IntPtr handle, uint base_axis, string paraname, int inum, int Cmp_mode, double fvalue);

		// Token: 0x06000258 RID: 600
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_MoveTask(IntPtr handle, uint base_axis, uint tasknum, string labelname);

		// Token: 0x06000259 RID: 601
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_Pswitch(IntPtr handle, int num, int enable, int axisnum, int outnum, int outstate, double setpos, double resetpos);

		// Token: 0x0600025A RID: 602
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_HwPswitch(IntPtr handle, int Axisnum, int Mode, int Direction, int Reserve, int Tablestart, int Tableend);

		// Token: 0x0600025B RID: 603
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetHwPswitchBuff(IntPtr handle, int axisnum, ref int buff);

		// Token: 0x0600025C RID: 604
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_HwTimer(IntPtr handle, int mode, int cyclonetime, int optime, int reptimes, int opstate, int opnum);

		// Token: 0x0600025D RID: 605
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetAxisStopReason(IntPtr handle, int iaxis, ref int piValue);

		// Token: 0x0600025E RID: 606
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetAllAxisPara(IntPtr handle, string sParam, int imaxaxis, double[] pfValue);

		// Token: 0x0600025F RID: 607
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetAllAxisInfo(IntPtr handle, int imaxaxis, int[] IdleStatus, double[] DposStatus, double[] MposStatus, int[] AxisStatus);

		// Token: 0x06000260 RID: 608
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_SetUserArray(IntPtr handle, string arrayname, int arraystart, int numes, double[] pfValue);

		// Token: 0x06000261 RID: 609
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetUserArray(IntPtr handle, string arrayname, int arraystart, int numes, double[] pfValue);

		// Token: 0x06000262 RID: 610
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_SetUserVar(IntPtr handle, string varname, double pfValue);

		// Token: 0x06000263 RID: 611
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetUserVar(IntPtr handle, string varname, ref double pfValue);

		// Token: 0x06000264 RID: 612
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_GetMaxPciCards();

		// Token: 0x06000265 RID: 613
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_OpenPci(uint cardnum, out IntPtr phandle);

		// Token: 0x06000266 RID: 614
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_GetControllerInfo(IntPtr handle, StringBuilder SoftType, StringBuilder SoftVersion, StringBuilder ControllerId);

		// Token: 0x06000267 RID: 615
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_BusCmd_GetNodeNum(IntPtr handle, int slot, ref int piValue);

		// Token: 0x06000268 RID: 616
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_BusCmd_GetNodeInfo(IntPtr handle, uint slot, uint node, uint sel, ref int piValue);

		// Token: 0x06000269 RID: 617
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_BusCmd_GetNodeStatus(IntPtr handle, uint slot, uint node, ref uint nodestatus);

		// Token: 0x0600026A RID: 618
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_BusCmd_SDORead(IntPtr handle, uint slot, uint node, uint index, uint subindex, uint type, ref int value, uint tableNumber = 0U);

		// Token: 0x0600026B RID: 619
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_BusCmd_SDOWrite(IntPtr handle, uint slot, uint node, uint index, uint subindex, uint type, int value);

		// Token: 0x0600026C RID: 620
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_BusCmd_RtexRead(IntPtr handle, uint iaxis, uint ipara, ref double value);

		// Token: 0x0600026D RID: 621
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_BusCmd_RtexWrite(IntPtr handle, uint iaxis, uint ipara, double value);

		// Token: 0x0600026E RID: 622
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_BusCmd_SetDatumOffpos(IntPtr handle, uint iaxis, double fValue);

		// Token: 0x0600026F RID: 623
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_BusCmd_GetDatumOffpos(IntPtr handle, uint iaxis, ref double fValue);

		// Token: 0x06000270 RID: 624
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_BusCmd_Datum(IntPtr handle, uint iaxis, uint homemode);

		// Token: 0x06000271 RID: 625
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_BusCmd_GetHomeStatus(IntPtr handle, uint iaxis, ref uint homestatus);

		// Token: 0x06000272 RID: 626
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_BusCmd_DriveClear(IntPtr handle, uint iaxis, uint mode);

		// Token: 0x06000273 RID: 627
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_BusCmd_GetDriveTorque(IntPtr handle, uint iaxis, ref int piValue);

		// Token: 0x06000274 RID: 628
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_BusCmd_SetMaxDriveTorque(IntPtr handle, uint iaxis, int piValue);

		// Token: 0x06000275 RID: 629
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_BusCmd_SDOWriteAxis(IntPtr handle, uint iaxis, uint index, uint subindex, uint type, int value);

		// Token: 0x06000276 RID: 630
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_BusCmd_SDOReadAxis(IntPtr handle, uint iaxis, uint index, uint subindex, uint type, ref int value, uint tableNumber = 0U);

		// Token: 0x06000277 RID: 631
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_BusCmd_GetMaxDriveTorque(IntPtr handle, uint iaxis, ref int piValue);

		// Token: 0x06000278 RID: 632
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_SetDAC(IntPtr handle, uint iaxis, double fValue);

		// Token: 0x06000279 RID: 633
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetDAC(IntPtr handle, uint iaxis, ref double fValue);

		// Token: 0x0600027A RID: 634
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_BusCmd_InitBus(IntPtr handle);

		// Token: 0x0600027B RID: 635
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_BusCmd_GetInitStatus(IntPtr handle, ref int piValue);

		// Token: 0x0600027C RID: 636
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetInMulti(IntPtr handle, int startio, int endio, out int piValue);

		// Token: 0x0600027D RID: 637
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_SetTimeOut(IntPtr handle, uint timems);

		// Token: 0x0600027E RID: 638
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_HwPswitch2(IntPtr handle, int Axisnum, int Mode, int Opnum, int Opstate, double ModePara1, double ModePara2, double ModePara3, double ModePara4, double ModePara5, double ModePara6);

		// Token: 0x0600027F RID: 639
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_GetSysSpecification(IntPtr handle, ref ushort Max_VirtuAxises, byte[] Max_motor, byte[] Max_io);

		// Token: 0x06000280 RID: 640
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_SetAutoUpCallBack(IntPtr handle, zmcaux.ZAuxCallBack pcallback);

		// Token: 0x06000281 RID: 641
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_SetOutMulti(IntPtr handle, ushort iofirst, ushort ioend, uint[] istate);

		// Token: 0x06000282 RID: 642
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetOutMulti(IntPtr handle, ushort iofirst, ushort ioend, out uint istate);

		// Token: 0x06000283 RID: 643
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_MultiMove(IntPtr handle, int iMoveLen, int imaxaxises, int[] piAxislist, double[] pfDposlist);

		// Token: 0x06000284 RID: 644
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_MultiMoveAbs(IntPtr handle, int iMoveLen, int imaxaxises, int[] piAxislist, double[] pfDposlist);

		// Token: 0x06000285 RID: 645
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_FrameRotate(IntPtr handle, int iaxis, double[] pfRotatePara);

		// Token: 0x06000286 RID: 646
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_GetCanInfo(IntPtr handle, ref byte CanNum, ushort[] CanId_List, byte[] CanIn_List, byte[] CanOut_List, byte[] CanAin_List, byte[] CanAOut_List, byte[] CanAxis_List);

		// Token: 0x06000287 RID: 647
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_MultiMovePt(IntPtr handle, int iMoveLen, int imaxaxises, int[] piAxislist, uint[] pTickslist, double[] pfDisancelist);

		// Token: 0x06000288 RID: 648
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_MultiMovePtAbs(IntPtr handle, int iMoveLen, int imaxaxises, int[] piAxislist, uint[] pTickslist, double[] pfDisancelist);

		// Token: 0x06000289 RID: 649
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_ZarDown(IntPtr handle, string Filename, uint run_mode);

		// Token: 0x0600028A RID: 650
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_GetRtcTime(IntPtr handle, StringBuilder RtcDate, StringBuilder RtcTime);

		// Token: 0x0600028B RID: 651
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_SetRtcTime(IntPtr handle, string RtcDate, string RtcTime);

		// Token: 0x0600028C RID: 652
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_FastOpen(int type, string pconnectstring, uint uims, out IntPtr phandle);

		// Token: 0x0600028D RID: 653
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_UserDatum(IntPtr handle, int iaxis, int imode, double HighSpeed, double LowSpeed, double DatumOffset);

		// Token: 0x0600028E RID: 654
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_Pitchset(IntPtr handle, int iaxis, int iEnable, double StartPos, uint maxpoint, double DisOne, uint TablNum, double[] pfDisancelist);

		// Token: 0x0600028F RID: 655
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_Pitchset2(IntPtr handle, int iaxis, int iEnable, double StartPos, uint maxpoint, double DisOne, uint TablNum, double[] pfDisancelist, uint RevTablNum, double[] RevpfDisancelist);

		// Token: 0x06000290 RID: 656
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetPitchStatus(IntPtr handle, int iaxis, ref int IfEnable, ref double PitchDist);

		// Token: 0x06000291 RID: 657
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_MultiLineN(IntPtr handle, int imode, int iMoveLen, int imaxaxises, int[] piAxislist, float[] pfDisancelist, ref int iReBuffLen);

		// Token: 0x06000292 RID: 658
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_MoveSync(IntPtr handle, double imode, int synctime, double syncposition, int syncaxis, int imaxaxises, int[] piAxislist, double[] pfDposlist);

		// Token: 0x06000293 RID: 659
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_CycleRegist(IntPtr handle, int iaxis, int imode, int iTabStart, int iTabNum);

		// Token: 0x06000294 RID: 660
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_MoveCancel(IntPtr handle, int base_axis, int Cancel_Axis, int iMode);

		// Token: 0x06000295 RID: 661
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_BusCmd_NodePdoWrite(IntPtr handle, uint inode, uint index, uint subindex, uint type, int value);

		// Token: 0x06000296 RID: 662
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_BusCmd_NodePdoRead(IntPtr handle, uint inode, uint index, uint subindex, uint type, ref int ivalue);

		// Token: 0x06000297 RID: 663
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_ZpjDown(IntPtr handle, string ZpjName, string ZarName, string pPass, uint uid, uint run_mode);

		// Token: 0x06000298 RID: 664
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_SetVpMode(IntPtr handle, uint axis, uint mode);

		// Token: 0x06000299 RID: 665
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_SetAxisZset(IntPtr handle, uint axis, uint mode);

		// Token: 0x0600029A RID: 666
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_BusCmd_SlotScan(IntPtr handle, uint slotNumber);

		// Token: 0x0600029B RID: 667
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_BusCmd_SlotStart(IntPtr handle, uint slotNumber);

		// Token: 0x0600029C RID: 668
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_BusCmd_SlotStop(IntPtr handle, uint slotNumber);

		// Token: 0x0600029D RID: 669
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_GetSysZfeature(IntPtr handle, uint code, ref uint value);

		// Token: 0x0600029E RID: 670
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_SetServoPeriod(IntPtr handle, uint value);

		// Token: 0x0600029F RID: 671
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_GetServoPeriod(IntPtr handle, ref uint value);

		// Token: 0x060002A0 RID: 672
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_BusCmd_GetNodeAxisCount(IntPtr handle, uint slot, uint node, ref uint axisNumber);

		// Token: 0x060002A1 RID: 673
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_BusCmd_SetDriveProfile(IntPtr handle, uint axis, int mode);

		// Token: 0x060002A2 RID: 674
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_BusCmd_GetDriveProfile(IntPtr handle, uint axis, ref int mode);

		// Token: 0x060002A3 RID: 675
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_BusCmd_SetDriveIO(IntPtr handle, uint axis, uint IoStart);

		// Token: 0x060002A4 RID: 676
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_BusCmd_GetDriveIO(IntPtr handle, uint axis, ref uint IoStart);

		// Token: 0x060002A5 RID: 677
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_BusCmd_SetNodeIo(IntPtr handle, uint slot, uint node, uint IoStart);

		// Token: 0x060002A6 RID: 678
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_BusCmd_GetNodeIo(IntPtr handle, uint slot, uint node, ref uint IoStart);

		// Token: 0x060002A7 RID: 679
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_DisableGroup(IntPtr handle, uint axisNumber, uint[] axisList);

		// Token: 0x060002A8 RID: 680
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_SetWdog(IntPtr handle, bool b);

		// Token: 0x060002A9 RID: 681
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_GetWdog(IntPtr handle, ref bool b);

		// Token: 0x060002AA RID: 682
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_MoveAddax(IntPtr handle, int baseAxis, uint iaxisNumber, int[] iaddaxisList);

		// Token: 0x060002AB RID: 683
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_MSphericalAbs(IntPtr handle, int imaxaxises, int[] piAxislist, double fend1, double fend2, double fend3, double fcenter1, double fcenter2, double fcenter3, int imode, double fcenter4, double fcenter5);

		// Token: 0x060002AC RID: 684
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_MSphericalAbsSp(IntPtr handle, int imaxaxises, int[] piAxislist, double fend1, double fend2, double fend3, double fcenter1, double fcenter2, double fcenter3, int imode, double fcenter4, double fcenter5);

		// Token: 0x060002AD RID: 685
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_Direct_Backlash(IntPtr handle, int iaxis, bool enable, double dist, double speed, double accel);

		// Token: 0x060002AE RID: 686
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_CycleUpEnable(IntPtr handle, uint cycleindex, float fintervalms, string psetesname);

		// Token: 0x060002AF RID: 687
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_CycleUpReadBuffInt(IntPtr handle, uint cycleindex, string setname, uint isetindex, string value);

		// Token: 0x060002B0 RID: 688
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_CycleUpGetRecvTimes(IntPtr handle, uint cycleindex);

		// Token: 0x060002B1 RID: 689
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_CycleUpDisable(IntPtr handle, uint cycleindex);

		// Token: 0x060002B2 RID: 690
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_CycleUpForceOnce(IntPtr handle, uint cycleindex);

		// Token: 0x060002B3 RID: 691
		[DllImport("zauxdll.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public static extern int ZAux_CycleUpReadBuff(IntPtr handle, uint cycleindex, string setname, uint isetindex, ref string value);

		// Token: 0x02000039 RID: 57
		// (Invoke) Token: 0x060002F5 RID: 757
		public delegate void ZAuxCallBack(IntPtr handle, int itypecode, int idatalength, [MarshalAs(UnmanagedType.LPArray, SizeConst = 2048)] byte[] pdata);
	}
}
