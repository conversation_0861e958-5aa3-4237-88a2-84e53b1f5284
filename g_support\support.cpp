#include "pch.h"
#include "MyPylon.h"
#include "pylon/ParameterIncludes.h"
#include <pylon/PylonIncludes.h>
#include <pylon/BaslerUniversalInstantCamera.h>
#include "opencv2/opencv.hpp"

#include <algorithm>
#include <vector>
#include "CirclePatternDetection.h"
#include <ctime>
#include <sstream>
#include <fstream>
#include <iostream>
#include <cmath>
#include <chrono>  
#include <random>

using namespace Pylon;
using namespace cv;
using namespace std;

// 计算给定的一组点在图像中的灰度均值
double checkMeanGrayValue(const std::vector<cv::Point>& points, const cv::Mat& img) {
    std::vector<double> grayValues;
    for (const auto& point : points) {
        grayValues.push_back(img.at<uchar>(point.y, point.x));
    }
    double sum = 0;
    for (double val : grayValues) {
        sum += val;
    }
    return sum / grayValues.size();
}

// 向左或向右平移并检查灰度值变化，确定最终的x坐标位置
std::pair<int, int> moveAndCheck(const cv::Mat& img, int left_x, int right_x) {
    int height = img.rows;
    int width = img.cols;
    int center_x = width / 2;
    int center_y = height / 2;

    int leftFinalX = 0;
    int rightFinalX = width;

    bool startLeft = false;
    bool stopLeft = false;
    while (left_x >= 0 &&!stopLeft) {
        std::vector<cv::Point> pointsToCheck;
        for (int i = 0; i < 3; ++i) {
            pointsToCheck.push_back(cv::Point(left_x + i, center_y));
        }
        double meanGray = checkMeanGrayValue(pointsToCheck, img);
        if (meanGray < 200 &&!startLeft) {
            startLeft = true;
        }
        else if (meanGray > 230 && startLeft) {
            stopLeft = true;
            leftFinalX = left_x;
        }
        left_x--;
    }

    bool startRight = false;
    bool stopRight = false;
    while (right_x < width &&!stopRight) {
        std::vector<cv::Point> pointsToCheck;
        for (int i = 0; i < 3; ++i) {
            pointsToCheck.push_back(cv::Point(right_x - i, center_y));
        }
        double meanGray = checkMeanGrayValue(pointsToCheck, img);
        if (meanGray < 200 &&!startRight) {
            startRight = true;
        }
        else if (meanGray > 230 && startRight) {
            stopRight = true;
            rightFinalX = right_x;
        }
        right_x++;
    }

    return std::make_pair(leftFinalX, rightFinalX);
}

int moveAndCheck4(const cv::Mat& img, int x, int y) {
    int height = img.rows;
    int width = img.cols;

    while (x < width) {
        if (img.at<uchar>(y, x) < 200)
            x++;
        else
            break;
    }
    //int ddd = img.at<uchar>(y, x);
    //cout << "img:" << x << " " << ddd << endl;
    while (x < width) {
        std::vector<cv::Point> pointsToCheck;
        for (int i = 0; i < 3; ++i) {
            //int ddd = img.at<uchar>(y, x+i);
            //cout << "img:" << x << " " << ddd << endl;
            pointsToCheck.push_back(cv::Point(x + i, y));
        }
        double meanGray = checkMeanGrayValue(pointsToCheck, img);
        if (meanGray < 200) {
            return x;
        }
        x++;
    }

    return 0;
}

// 另一种向左或向右平移并检查灰度值变化确定最终x坐标位置的函数（简化版）
std::pair<int, int> moveAndCheck1(const cv::Mat& img, int left_x, int right_x) {
    int height = img.rows;
    int width = img.cols;
    int center_x = width / 2;
    int center_y = height / 2;

    int leftFinalX = 0;
    int rightFinalX = width;

    while (left_x >= 0) {
        std::vector<cv::Point> pointsToCheck;
        for (int i = 0; i < 3; ++i) {
            pointsToCheck.push_back(cv::Point(left_x + i, center_y));
        }
        double meanGray = checkMeanGrayValue(pointsToCheck, img);
        if (meanGray < 200) {
            leftFinalX = left_x;
            break;
        }
        left_x--;
    }

    while (right_x < width) {
        std::vector<cv::Point> pointsToCheck;
        for (int i = 0; i < 3; ++i) {
            pointsToCheck.push_back(cv::Point(right_x - i, center_y));
        }
        double meanGray = checkMeanGrayValue(pointsToCheck, img);
        if (meanGray < 200) {
            rightFinalX = right_x;
            break;
        }
        right_x++;
    }

    return std::make_pair(leftFinalX, rightFinalX);
}

// 向上或向下平移并检查灰度值变化确定最终y坐标位置的函数
std::pair<int, int> moveAndCheck2(const cv::Mat& img, int left_x, int right_x) {
    int height = img.rows;
    int width = img.cols;
    int center_x = width / 2;
    int choose_y = height / 2;
    int center_y = height / 2;

    int leftFinalY = 0;
    int rightFinalY = width;

    while (choose_y >= 0) {
        std::vector<cv::Point> pointsToCheck;
        for (int i = 0; i < 3; ++i) {
            pointsToCheck.push_back(cv::Point(left_x, choose_y + i));
        }
        double meanGray = checkMeanGrayValue(pointsToCheck, img);
        if (meanGray < 200) {
            leftFinalY = choose_y;
            break;
        }
        choose_y--;
    }

    while (center_y >= 0) {
        std::vector<cv::Point> pointsToCheck;
        for (int i = 0; i < 3; ++i) {
            pointsToCheck.push_back(cv::Point(right_x, center_y + i));
        }
        double meanGray = checkMeanGrayValue(pointsToCheck, img);
        if (meanGray < 200) {
            rightFinalY = center_y;
            break;
        }
        center_y--;
    }

    return std::make_pair(leftFinalY, rightFinalY);
}

// 向上或向下平移并检查灰度值变化确定最终y坐标位置的函数
int moveAndCheck3(const cv::Mat& img, int x_pos, int begin, int max_y) {    
    int choose_y = begin;

    int leftFinalY = begin;

    while (choose_y < max_y) {
        std::vector<cv::Point> pointsToCheck;
        for (int i = 0; i < 5; ++i) {
            pointsToCheck.push_back(cv::Point(x_pos, choose_y + i));
        }
        double meanGray = checkMeanGrayValue(pointsToCheck, img);
        if (meanGray > 200) {
            leftFinalY = choose_y;
            break;
        }
        choose_y++;
    }

    return leftFinalY;
}

// 图像相关处理函数，涉及阈值处理、轮廓查找、筛选轮廓等操作
int imageHandle(cv::Mat& img, std::vector<cv::Point>& group1, std::vector<cv::Point>& group2, int num, int offset, int yoffset) {
    cv::Mat thresh;
    cv::threshold(img, thresh, 160, 255, cv::THRESH_BINARY);

    std::vector<cv::Point>* group;

    std::vector<std::vector<cv::Point>> contours;
    std::vector<cv::Vec4i> hierarchy;
    cv::findContours(thresh, contours, hierarchy, cv::RETR_TREE, cv::CHAIN_APPROX_SIMPLE);

    cv::Mat imgColor;
    cv::cvtColor(img, imgColor, cv::COLOR_GRAY2BGR);

    int xMinRange = 10;
    int xMaxRange = 500;
    int yMinRange = 100;
    int yMaxRange = 1100;

    std::vector<std::vector<cv::Point>> filteredContours;

    std::map<int, std::vector<cv::Point>> contourLengthMap;

    for (size_t i = 0; i < contours.size(); ++i) {
        if (contours[i].size() < num) {
            continue;
        }
        contourLengthMap[contours[i].size()] = contours[i];
    }

    std::vector<int> sortedContourLengths;
    for (const auto& it : contourLengthMap) {
        sortedContourLengths.push_back(it.first);
    }
    std::sort(sortedContourLengths.begin(), sortedContourLengths.end(), std::greater<int>());

    if (sortedContourLengths.size() < 1) {

        return -1;
    }

    if (sortedContourLengths.size() < 2) {
        int length = sortedContourLengths[0];
        std::vector<cv::Point> groupContour = contourLengthMap[length];

        for (const auto& point : groupContour) {
            int x = point.x;
            int y = point.y;
            int px = point.x + offset;
            int py = point.y + yoffset;
            if (xMinRange <= x && x <= xMaxRange && yMinRange <= y && y <= yMaxRange) {
                //if(img.at<uchar>(point.y, point.x)<100)
                group1.push_back(cv::Point(px, py));
            }
        }
        return 0;
    }

    std::map<int, std::vector<cv::Point>> contourGroup;

    int minMaxX = 10000;
    int maxMaxX = 0;

    for (size_t i = 0; i < 2; ++i) {
        int length = sortedContourLengths[i];
        std::vector<cv::Point> groupContour = contourLengthMap[length];
/*
        cv::namedWindow("Contours Image", cv::WINDOW_NORMAL);
        cv::drawContours(imgColor, std::vector<std::vector<cv::Point>>{groupContour}, -1, cv::Scalar(0, 0, 255), 2);
        cv::imshow("Contours Image", imgColor);
        cv::waitKey(0);
        cv::destroyAllWindows();
*/
        int numPoints = groupContour.size();
        //std::cout << "轮廓 " << (i + 1) << " 筛选后的点集个数: " << numPoints << std::endl;

        std::vector<cv::Point> newContour;
        int maxX = 0;
        int minX = 10000;
        for (const auto& point : groupContour) {
            int x = point.x;
            int y = point.y;
            if (xMinRange <= x && x <= xMaxRange && yMinRange <= y && y <= yMaxRange) {
                newContour.push_back(point);
            }
            if (offset < 1500) {
                if (x < minX) {
                    minX = x;
                }
            }
            else {
                if (x > maxX) {
                    maxX = x;
                }
            }

        }

        std::cout << " maxX " << maxX << std::endl;
        if (offset < 1500) {
            contourGroup[minX] = newContour;
            if (minX < minMaxX) {
                minMaxX = minX;
            }
        }
        else {
            contourGroup[maxX] = newContour;
            if (maxX > maxMaxX) {
                maxMaxX = maxX;
            }
        }
    }

    for (const auto& it : contourGroup) {
        int key = it.first;
        const std::vector<cv::Point>& newContour = it.second;
        std::cout << "key " << key << std::endl;
        std::cout << "newContour.size " << newContour.size() << std::endl;
        if (offset < 1500) {
            if (key == minMaxX) {
                group = &group2;
            }
            else {
                group = &group1;
            }
        }
        else {
            if (key == maxMaxX) {
                group = &group2;
            }
            else {
                group = &group1;
            }
        }
        for (const auto& point : newContour) {
            int px = point.x + offset;
            int py = point.y + yoffset;
            group->push_back(cv::Point(px, py));
            //cv2.circle(img_color, (int(px), int(py)), 2, (0, 0, 255), -1);
        }

        //std::cout << "group size:" << group->size() << std::endl;
        //std::cout << "group1 size:" << group1.size() << std::endl;
    }

    return 0;
}

int imageHandle2(cv::Mat& img, std::vector<cv::Point>& group1, std::vector<cv::Point>& group2, int num, int offset, int yoffset) {
    cv::Mat thresh;
    cv::threshold(img, thresh, 170, 255, cv::THRESH_BINARY);
    cv::imwrite("Contours.png", thresh);
    std::vector<cv::Point>* group;

    std::vector<std::vector<cv::Point>> contours;
    std::vector<cv::Vec4i> hierarchy;
    cv::findContours(thresh, contours, hierarchy, cv::RETR_TREE, cv::CHAIN_APPROX_SIMPLE);

    cv::Mat imgColor;
    cv::cvtColor(img, imgColor, cv::COLOR_GRAY2BGR);

    int xMinRange = 5;
    int xMaxRange = 500;
    int yMinRange = 100;
    int yMaxRange = 1100;

    std::vector<std::vector<cv::Point>> filteredContours;

    std::map<int, std::vector<cv::Point>> contourLengthMap;

    for (size_t i = 0; i < contours.size(); ++i) {
        std::cout << "contours[i].size() " << contours[i].size() << std::endl;
        if (contours[i].size() < num) {
            continue;
        }
        contourLengthMap[contours[i].size()] = contours[i];
    }

    std::vector<int> sortedContourLengths;
    for (const auto& it : contourLengthMap) {
        sortedContourLengths.push_back(it.first);
    }
    std::sort(sortedContourLengths.begin(), sortedContourLengths.end(), std::greater<int>());

    if (sortedContourLengths.size() < 1) {

        return -1;
    }

    if (sortedContourLengths.size() < 2) {
        int length = sortedContourLengths[0];
        std::vector<cv::Point> groupContour = contourLengthMap[length];

        for (const auto& point : groupContour) {
            int x = point.x;
            int y = point.y;
            int px = point.x + offset;
            int py = point.y + yoffset;
            if (xMinRange <= x && x <= xMaxRange) {
                //if(img.at<uchar>(point.y, point.x)<100)
                group1.push_back(cv::Point(px, py));
            }
        }
        return 0;
    }

    std::map<int, std::vector<cv::Point>> contourGroup;

    int minMaxX = 10000;
    int maxMaxX = 0;

    for (size_t i = 0; i < 2; ++i) {
        int length = sortedContourLengths[i];
        std::vector<cv::Point> groupContour = contourLengthMap[length];

        int numPoints = groupContour.size();
        std::cout << "轮廓 " << (i + 1) << " 筛选后的点集个数: " << numPoints << std::endl;

        std::vector<cv::Point> newContour;
        int maxX = 0;
        int minX = 10000;
        for (const auto& point : groupContour) {
            int x = point.x;
            int y = point.y;
            if (xMinRange <= x && x <= xMaxRange) {
                //std::cout << "val " << int(img.at<uchar>(point.y, point.x)) << " ";
                //if(img.at<uchar>(point.y, point.x)<100)
                newContour.push_back(point);
            }
            if (offset < 1500) {
                if (x < minX) {
                    minX = x;
                }
            }
            else {
                if (x > maxX) {
                    maxX = x;
                }
            }

        }

        std::cout << " maxX " << maxX << std::endl;
        if (offset < 1500) {
            contourGroup[minX] = newContour;
            if (minX < minMaxX) {
                minMaxX = minX;
            }
        }
        else {
            contourGroup[maxX] = newContour;
            if (maxX > maxMaxX) {
                maxMaxX = maxX;
            }
        }
    }

    for (const auto& it : contourGroup) {
        int key = it.first;
        const std::vector<cv::Point>& newContour = it.second;
        std::cout << "key " << key << std::endl;
        std::cout << "newContour.size " << newContour.size() << std::endl;
        if (offset < 1500) {
            if (key == minMaxX) {
                group = &group2;
            }
            else {
                group = &group1;
            }
        }
        else {
            if (key == maxMaxX) {
                group = &group2;
            }
            else {
                group = &group1;
            }
        }
        for (const auto& point : newContour) {
            int px = point.x + offset;
            int py = point.y + yoffset;
            group->push_back(cv::Point(px, py));
            //cv2.circle(img_color, (int(px), int(py)), 2, (0, 0, 255), -1);
        }

        std::cout << "group size:" << group->size() << std::endl;
        std::cout << "group1 size:" << group1.size() << std::endl;
    }

    return 0;
}

int imageLineFind(cv::Mat& img, std::vector<cv::Vec4i> &lines, int value, int maxLen){
    cv::HoughLinesP(img, lines, 1, CV_PI / 180, value, maxLen, 80);

    if (lines.empty()) 
        return -1;

    std::cout << "OK" << std::endl;

    return 0;
}

cv::Vec4f fitLineRANSAC(const std::vector<cv::Point2f>& points, 
                                   float distanceThreshold = 1.0, 
                                   int maxIterations = 100) {
        if (points.size() < 2) {
            throw std::runtime_error("Not enough points to fit a line");
        }

        cv::Vec4f bestLine;
        int maxInliers = 0;

        // 随机数生成器
        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_int_distribution<> dis(0, points.size() - 1);

        for (int iter = 0; iter < maxIterations; ++iter) {
            // 随机选择两个点
            int idx1 = dis(gen);
            int idx2 = dis(gen);
            
            // 确保选择不同的点
            while (idx1 == idx2) {
                idx2 = dis(gen);
            }

            cv::Point2f pt1 = points[idx1];
            cv::Point2f pt2 = points[idx2];

            // 计算直线参数 (使用两点式)
            float dx = pt2.x - pt1.x;
            float dy = pt2.y - pt1.y;
            float slope = dy / (dx + 1e-10);  // 防止除零
            float intercept = pt1.y - slope * pt1.x;

            // 计算内点
            int inlierCount = 0;
            std::vector<cv::Point2f> currentInliers;

            for (const auto& pt : points) {
                // 计算点到直线的距离
                float distance = std::abs(pt.y - (slope * pt.x + intercept)) / 
                                 std::sqrt(1 + slope * slope);

                if (distance < distanceThreshold) {
                    inlierCount++;
                    currentInliers.push_back(pt);
                }
            }

            // 更新最佳拟合直线
            if (inlierCount > maxInliers) {
                maxInliers = inlierCount;
                
                // 使用最小二乘法重新拟合内点
                cv::Vec4f line;
                cv::fitLine(currentInliers, line, cv::DIST_L2, 0, 0.01, 0.01);
                bestLine = line;
            }
        }

        return bestLine;
    }

    // 可视化直线拟合结果
double visualizeLineFitting(cv::Mat& image, const std::vector<cv::Point2f>& points, 
                                    const cv::Vec4f& line, double slope, double &angle, cv::Scalar color) {

    // 绘制所有点
    for (const auto& pt : points) {
        //cv::circle(image, cv::Point(pt.x, pt.y), 5, cv::Scalar(0, 0, 255), -1);
    }

    // 获取points中x坐标的最小值和最大值
    //auto [min_x_it, max_x_it] = std::minmax_element(points.begin(), points.end(),
                                                   //[](const cv::Point& p1, const cv::Point& p2) { return p1.x < p2.x; });
    //int min_x = min_x_it->x;
    //int max_x = max_x_it->x;

    // 计算直线端点
    double cos_theta = line[0];
    double sin_theta = line[1];
    double x0 = line[2];
    double y0 = line[3];
    double k = sin_theta / cos_theta;

    std::cout << "cos_theta:" << cos_theta << std::endl;
    std::cout << "sin_theta:" << sin_theta << std::endl;

    std::cout << "x0:" << x0 << std::endl;
    std::cout << "y0:" << y0 << std::endl;

    std::cout << "k:" << k << std::endl;
    std::cout << "slope:" << slope << std::endl;

    //std::cout << "m1500:" << y0+k*(1500-x0) << std::endl;

    // 计算直线的两个端点
    cv::Point pt1(600, static_cast<int>(y0 + k*(600-x0)));
    cv::Point pt2(1200, static_cast<int>(y0 + k*(1200-x0)));

    // 绘制拟合直线
    cv::line(image, pt1, pt2, color, 1, cv::LINE_AA);

    
    double tan_theta = std::abs((k - slope) / (1 + slope * k));
    //double theta_radian = std::atan(tan_theta);
    angle = tan_theta;
    
    return y0+k*(1500-x0);
}

double calculateMeanV(cv::Mat& image, int x0, int y0, int x1) {
    cv::Rect roi(x0, y0 + 5, x1 - x0, 5);

    // 限制随机数生成范围在ROI内，获取宽度和高度
    int roi_width = roi.width;
    int roi_height = roi.height;

    // 设置随机数种子，确保每次运行生成的随机序列不同（基于当前时间）
    std::srand(static_cast<unsigned int>(std::time(nullptr)));

    // 用于存储挑选的点的像素灰度值
    std::vector<int> pixel_values;

    // 在ROI区域内随机挑选20个点并获取其像素灰度值
    for (int i = 0; i < 20; ++i) {
        int random_x = roi.x + std::rand() % roi_width;
        int random_y = roi.y + std::rand() % roi_height;
        int pixel_value = image.at<uchar>(random_y, random_x);
        pixel_values.push_back(pixel_value);
    }

    // 计算像素灰度均值
    int sum = 0;
    for (int value : pixel_values) {
        sum += value;
    }
    double mean = static_cast<double>(sum) / pixel_values.size();

    std::cout << "calculateMeanV: " << mean << std::endl;

    return mean;
}

int lineCapture(cv::Mat& roi, cv::Vec4i &line) {
    std::map<int, std::vector<cv::Vec4i>> horizontalLines;
    int times = 0;
    int flag = 0;
    std::vector<cv::Vec4i> lines;

    cv::Mat blurredImage;
    cv::GaussianBlur(roi, blurredImage, cv::Size(9, 9), 1);

    //cv::Mat thresh;
    //cv::threshold(blurredImage, thresh, 150, 255, cv::THRESH_BINARY);

    cv::Mat edges;
    cv::Canny(blurredImage, edges, 50, 150);

    std::cout << "lineCapture" << std::endl;
    while (times < 4) {
        int ret = imageLineFind(edges, lines, 100 - times * 20, 80 - times * 15);
        if (!lines.empty()) {
            for (const auto& l : lines) {
                int x1 = l[0];
                int y1 = l[1];
                int x2 = l[2];
                int y2 = l[3];
                double slope;

                std::cout << "x1 " << x1 << " x2 " << x2 << std::endl;

                if (x2 - x1 == 0) {
                    slope = std::numeric_limits<double>::infinity();
                }
                else {
                    slope = static_cast<double>(y2 - y1) / (x2 - x1);
                }
                if (-0.1 <= slope && slope <= 0.1) {
                    int y_max = std::max(y1, y2);
                    double mean_v = calculateMeanV(roi, x1, y_max, x2);
                    std::cout << "mean_v " << mean_v << std::endl;
                    if (mean_v < 150)
                        continue;
                    flag = 1;
                    horizontalLines[1000- y_max].push_back(l);
                }
            }
        }
        if (ret == 0 && flag == 1)
            break;
        horizontalLines.clear();
        times++;
    }

    if (horizontalLines.empty()) {
        std::cout << "horizontalLines map为空" << std::endl;
        return -1;
    }

    int maxKey = horizontalLines.rbegin()->first;
    line = horizontalLines[maxKey][0];

    return 0;
}

int imageLine(cv::Mat& img, cv::Mat& imgColor, cv::Point2f(&points)[4]) {
    cv::Mat blurredImage;
    cv::GaussianBlur(img, blurredImage, cv::Size(9, 9), 1);

    //cv::Mat thresh;
    //cv::threshold(blurredImage, thresh, 150, 255, cv::THRESH_BINARY);

    cv::Mat edges;
    cv::Canny(blurredImage, edges, 50, 150);

    std::vector<cv::Vec4i> lines;
    //cv::HoughLinesP(edges, lines, 1, CV_PI / 180, 100, 500, 80);
    int times = 0;

    int height = edges.rows;
    int width = edges.cols;
    cv::Mat eLeft = edges(cv::Rect(300, 0, 200, height));
    std::vector<cv::Vec4i> linesLeft;
    //cv::HoughLinesP(eLeft, linesLeft, 1, CV_PI / 180, 80, 80, 20);
    //cv::imwrite("eleft.png", eLeft);
    std::map<int, std::vector<cv::Vec4i>> horizontalLines;
    times = 0;
    int flag = 0;
    /*
    while (times < 3) {
        int ret = imageLineFind(eLeft, linesLeft, 100 - times * 20, 80 - times * 15);
        if (!linesLeft.empty()) {
            for (const auto& l : linesLeft) {
                int x1 = l[0];
                int y1 = l[1];
                int x2 = l[2];
                int y2 = l[3];
                double slope;

                std::cout << "mmmmm left" << std::endl;
                std::cout << "x1 " << x1 << " x2 " << x2 << std::endl;

                if (x2 - x1 == 0) {
                    slope = std::numeric_limits<double>::infinity();
                }
                else {
                    slope = static_cast<double>(y2 - y1) / (x2 - x1);
                }
                if (-0.1 <= slope && slope <= 0.1) {
                    int y_max = std::max(y1, y2);
                    double mean_v = calculateMeanV(img, x1 + 300, y_max, x2 + 300);
                    std::cout << "left mean_v " << mean_v << std::endl;
                    if (mean_v < 150)
                        continue;
                    flag = 1;
                    horizontalLines[std::abs(x1-x2)].push_back(l);                    
                }
            }
        }
        if (ret == 0 && flag == 1)
            break;
        horizontalLines.clear();
        times++;
    }

    if (horizontalLines.empty()) {
        std::cout << "horizontalLines map为空" << std::endl;
        return -1;
    }
    
    int maxKey = horizontalLines.rbegin()->first;
    cv::Vec4i line1 = horizontalLines[maxKey][0];    
    */
    cv::Vec4i line1;
    //lineCapture(eLeft, imgColor, line1);
    eLeft = img(cv::Rect(300, 0, 200, height));
    lineCapture(eLeft, line1);
    horizontalLines.clear();
    cv::Mat eRight = edges(cv::Rect(width - 500, 0, 200, height));
    int rightDiff = width - 500;
    cv::Vec4i line2;
    std::vector<cv::Vec4i> linesRight;
    //cv::HoughLinesP(eRight, linesRight, 1, CV_PI / 180, 80, 80, 20);
    times = 0;
    flag = 0;
    while (times < 4) {
        int ret = imageLineFind(eRight, linesRight, 100 - times * 15, 80 - times * 10);
        if (!linesRight.empty()) {
            for (const auto& l : linesRight) {
                int x1 = l[0];
                int y1 = l[1];
                int x2 = l[2];
                int y2 = l[3];
                double slope;

                std::cout << "mmmmm right" << std::endl;
                std::cout << "x1 " << x1 << " x2 " << x2 << std::endl;

                if (x2 - x1 == 0) {
                    slope = std::numeric_limits<double>::infinity();
                }
                else {
                    slope = static_cast<double>(y2 - y1) / (x2 - x1);
                }
                if (-0.1 <= slope && slope <= 0.1) {
                    int y_max = std::max(y1, y2);
                    double mean_v = calculateMeanV(img, x1+ rightDiff, y_max, x2+ rightDiff);
                    std::cout << "right mean_v " << mean_v << std::endl;
                    if (mean_v < 150)
                        continue;
                    horizontalLines[std::abs(x1 - x2)].push_back(l);
                    flag = 1;
                    cv::line(imgColor, cv::Point(x1 + rightDiff, y1), cv::Point(x2 + rightDiff, y2), cv::Scalar(0, 0, 255), 2);
                }
            }
        }
        if (ret == 0 && flag==1)
            break;
        times++;
    }
    
    int maxKey = horizontalLines.rbegin()->first;
    line2 = horizontalLines[maxKey][0];

    double x1 = line1[0];
    double y1 = line1[1];
    double x2 = line1[2];
    double y2 = line1[3];

    cv::Point2f point0 = cv::Point2f(x1+300, y1);
    cv::Point2f point1 = cv::Point2f(x2+300, y2);

    cv::line(imgColor, point0, point1, cv::Scalar(0, 255, 0), 2);

    //std::cout << "point0 " << x1 << " " << y1 << std::endl;
    //std::cout << "point1 " << x2 << " " << y2 << std::endl;

    x1 = line2[0];
    y1 = line2[1];
    x2 = line2[2];
    y2 = line2[3];

    cv::Point2f point2 = cv::Point2f(x1 + rightDiff, y1);
    cv::Point2f point3 = cv::Point2f(x2 + rightDiff, y2);
    cv::line(imgColor, point2, point3, cv::Scalar(0, 255, 0), 2);

    points[0] = point0;
    points[1] = point1;
    points[2] = point2;
    points[3] = point3;


    //double angle;
    // 可视化结果
    //visualizeLineFitting(imgColor, pointSet, line, slope, angle);


    //cv::imwrite("img_color.png", imgColor);

    return 0;
}

double calculateLineDiff(cv::Point2f& p1, cv::Point2f& p2, int x)
{
    double m = (double)(p2.y - p1.y) / (p2.x - p1.x);
    double b = p1.y - m * p1.x;

    return m * x + b;
}

// 处理图像中的直线相关操作，包括直线检测、筛选水平直线、计算夹角等
cv::Mat imageLineHandle(cv::Mat& img, double &diff, double &angle) {
    cv::Mat blurredImage;
    cv::GaussianBlur(img, blurredImage, cv::Size(9, 9), 1);

    //cv::Mat thresh;
    //cv::threshold(blurredImage, thresh, 150, 255, cv::THRESH_BINARY);

    cv::Mat edges;
    cv::Canny(blurredImage, edges, 50, 150);

    std::vector<cv::Vec4i> lines;
    //cv::HoughLinesP(edges, lines, 1, CV_PI / 180, 100, 500, 80);
    int times = 0;
    cv::Mat imgColor;
    cv::cvtColor(img, imgColor, cv::COLOR_GRAY2BGR);    
    cv::Point2f pointss[4];
    int width = edges.cols;
    int height = img.rows;
    cv::Vec4i line1;
    //lineCapture(eLeft, imgColor, line1);
    cv::Mat roi = img(cv::Rect(260, 0, 200, height));
    int ret = lineCapture(roi, line1);
    if (ret < 0)
        return cv::Mat();

    double x1 = line1[0];
    double y1 = line1[1];
    double x2 = line1[2];
    double y2 = line1[3];

    pointss[0] = cv::Point2f(x1 + 260, y1);
    pointss[1] = cv::Point2f(x2 + 260, y2);
    cv::line(imgColor, pointss[0], pointss[1], cv::Scalar(0, 255, 0), 1, cv::LINE_AA);

    roi = img(cv::Rect(width - 460, 0, 200, height));
    int rightDiff = width - 460;
    cv::Vec4i line2;
    ret = lineCapture(roi, line2);
    if (ret < 0)
        return cv::Mat();

    x1 = line2[0];
    y1 = line2[1];
    x2 = line2[2];
    y2 = line2[3];

    pointss[2] = cv::Point2f(x1 + rightDiff, y1);
    pointss[3] = cv::Point2f(x2 + rightDiff, y2);

    cv::line(imgColor, pointss[2], pointss[3], cv::Scalar(0, 255, 0), 1, cv::LINE_AA);
    std::cout << "pointss0 " << pointss[0].x << " " << pointss[0].y << std::endl;
    std::cout << "pointss1 " << pointss[1].x << " " << pointss[1].y << std::endl;
    std::cout << "pointss2 " << pointss[2].x << " " << pointss[2].y << std::endl;
    std::cout << "pointss3 " << pointss[3].x << " " << pointss[3].y << std::endl;

    cv::Point2f points[4];
    roi = img(cv::Rect(0, 0, 180, height));
    ret = lineCapture(roi, line1);
    if (ret < 0)
        return cv::Mat();

    x1 = line1[0];
    y1 = line1[1];
    x2 = line1[2];
    y2 = line1[3];

    points[0] = cv::Point2f(x1, y1);
    points[1] = cv::Point2f(x2, y2);
    cv::line(imgColor, points[0], points[1], cv::Scalar(0, 0, 255), 1, cv::LINE_AA);

    roi = img(cv::Rect(width - 190, 0, 180, height));
    rightDiff = width - 240;
    ret = lineCapture(roi, line1);
    if (ret < 0)
        return cv::Mat();

    x1 = line1[0];
    y1 = line1[1];
    x2 = line1[2];
    y2 = line1[3];

    points[2] = cv::Point2f(x1+ rightDiff, y1);
    points[3] = cv::Point2f(x2+ rightDiff, y2);
    cv::line(imgColor, points[2], points[3], cv::Scalar(0, 0, 255), 1, cv::LINE_AA);

    std::cout << "points " << points[0].x << " " << points[0].y << std::endl;
    std::cout << "points " << points[1].x << " " << points[1].y << std::endl;
    std::cout << "points " << points[2].x << " " << points[2].y << std::endl;
    std::cout << "points " << points[3].x << " " << points[3].y << std::endl;

    double o_l_height = calculateLineDiff(points[0], points[1], 300);
    double i_l_height = calculateLineDiff(pointss[0], pointss[1], 300);

    double o_r_height = calculateLineDiff(points[2], points[3], 2000);
    double i_r_height = calculateLineDiff(pointss[2], pointss[3], 2000);

    double cangle = std::abs((i_l_height - o_l_height) - (i_r_height - o_r_height))/200;
    std::cout << "o_l_height " << o_l_height << " " << i_l_height << std::endl;
    std::cout << "o_r_height " << o_r_height << " " << i_r_height << std::endl;
    std::cout << "cangle " << cangle << std::endl;

    std::vector<cv::Point2f> pointSetss(pointss, pointss + 4);
    cv::Vec4i line0 = fitLineRANSAC(pointSetss, 100, 20);

    x1 = line0[0];
    y1 = line0[1];
    x2 = line0[2];
    y2 = line0[3];

    // 可视化结果
    double middle0 = visualizeLineFitting(imgColor, pointSetss, line0, 0, angle, cv::Scalar(0, 255, 0));
    
    double slope = y1/x1;
    double intercept = y1 - slope * x1;

    std::cout << "middle0:" << middle0 << std::endl;

    std::vector<cv::Point2f> pointSet(points, points + 4);
    cv::Vec4f line = fitLineRANSAC(pointSet, 100, 20);

        // 可视化结果
    double m1500 = visualizeLineFitting(imgColor, pointSet, line, slope, angle, cv::Scalar(0, 0, 255));
    diff = m1500 - middle0;    
    cangle = std::max(cangle, angle);
    std::cout << "cangle " << cangle << std::endl;
    double theta_radian = std::atan(cangle);
    angle = theta_radian * 180 / CV_PI;

    return imgColor;
}

double calculateDistance(const cv::Point2f& p1, const cv::Point2f& p2) {
    double dx = p2.x - p1.x;
    double dy = p2.y - p1.y;
    return std::sqrt(dx * dx + dy * dy);
}

double visualizeLineFitting(cv::Mat& image, const std::vector<cv::Point2f>& points,
    const cv::Vec4f& line, double& angle, cv::Scalar color) {

    // 绘制所有点
    for (const auto& pt : points) {
        cv::circle(image, cv::Point(pt.x, pt.y), 13, cv::Scalar(0, 0, 255), -1);
    }

    // 获取points中x坐标的最小值和最大值
    //auto [min_x_it, max_x_it] = std::minmax_element(points.begin(), points.end(),
                                                   //[](const cv::Point& p1, const cv::Point& p2) { return p1.x < p2.x; });
    //int min_x = min_x_it->x;
    //int max_x = max_x_it->x;

    auto minmax_it = std::minmax_element(points.begin(), points.end(),
        [](const cv::Point& p1, const cv::Point& p2) { return p1.x < p2.x; });
    // 提取最小元素对应的迭代器
    auto min_x_it = minmax_it.first;
    // 提取最大元素对应的迭代器
    auto max_x_it = minmax_it.second;

    int min_x = min_x_it->x;
    int max_x = max_x_it->x;

    // 计算直线端点
    double cos_theta = line[0];
    double sin_theta = line[1];
    double x0 = line[2];
    double y0 = line[3];
    double k = sin_theta / cos_theta;

    //std::cout << "m1500:" << y0+k*(1500-x0) << std::endl;

    // 计算直线的两个端点
    cv::Point pt1(min_x, static_cast<int>(y0 + k * (min_x - x0)));
    cv::Point pt2(max_x, static_cast<int>(y0 + k * (max_x - x0)));

    // 绘制拟合直线
    cv::line(image, pt1, pt2, color, 2);

    double theta_radian = std::atan(k);
    angle = theta_radian * 180 / CV_PI;

    return 0;
}

// 处理图像中的直线相关操作，包括直线检测、筛选水平直线、计算夹角等
cv::Mat imageLineHandle(cv::Mat& img, double& angle) {
    cv::Mat blurredImage;
    cv::GaussianBlur(img, blurredImage, cv::Size(9, 9), 1);

    //cv::Mat thresh;
    //cv::threshold(blurredImage, thresh, 150, 255, cv::THRESH_BINARY);

    cv::Mat edges;
    cv::Canny(blurredImage, edges, 50, 150);

    cv::Mat imgColor;
    cv::cvtColor(img, imgColor, cv::COLOR_GRAY2BGR);

    int height = edges.rows;
    int width = edges.cols;
    cv::Mat eLeft = edges(cv::Rect(0, 0, 300, height));
    std::vector<cv::Vec4i> linesLeft;
    //cv::HoughLinesP(eLeft, linesLeft, 1, CV_PI / 180, 80, 80, 20);
    //cv::imwrite("eleft.png", eLeft);
    int times = 0;
    while (times < 3) {
        int ret = imageLineFind(eLeft, linesLeft, 100 - times * 20, 80 - times * 15);
        if (ret == 0)
            break;
        //std::cout << "left " << ret << std::endl;
        times++;
    }
    cv::Vec4i line1;
    if (!linesLeft.empty()) {
        for (const auto& l : linesLeft) {
            int x1 = l[0];
            int y1 = l[1];
            int x2 = l[2];
            int y2 = l[3];
            double slope;

            if (x2 - x1 == 0) {
                slope = std::numeric_limits<double>::infinity();
            }
            else {
                slope = static_cast<double>(y2 - y1) / (x2 - x1);
            }
            if (-0.1 <= slope && slope <= 0.1) {
                int y_max = std::max(y1, y2);
                double mean_v = calculateMeanV(img, x1, y_max, x2);
                if (mean_v < 200)
                    continue;
                line1 = l;
                cv::line(imgColor, cv::Point(x1, y1), cv::Point(x2, y2), cv::Scalar(0, 0, 255), 2);
            }
        }
    }

    cv::Mat eRight = edges(cv::Rect(width - 300, 0, 300, height));
    int rightDiff = width - 300;
    cv::Vec4i line2;
    std::vector<cv::Vec4i> linesRight;
    //cv::HoughLinesP(eRight, linesRight, 1, CV_PI / 180, 80, 80, 20);
    times = 0;
    while (times < 3) {
        int ret = imageLineFind(eRight, linesRight, 100 - times * 15, 80 - times * 10);
        if (ret == 0)
            break;
        times++;
    }
    if (!linesRight.empty()) {
        for (const auto& l : linesRight) {
            int x1 = l[0];
            int y1 = l[1];
            int x2 = l[2];
            int y2 = l[3];
            double slope;

            if (x2 - x1 == 0) {
                slope = std::numeric_limits<double>::infinity();
            }
            else {
                slope = static_cast<double>(y2 - y1) / (x2 - x1);
            }
            if (-0.1 <= slope && slope <= 0.1) {
                int y_max = std::max(y1, y2);
                double mean_v = calculateMeanV(img, x1 + rightDiff, y_max, x2 + rightDiff);
                if (mean_v < 200)
                    continue;
                line2 = l;
                cv::line(imgColor, cv::Point(x1 + rightDiff, y1), cv::Point(x2 + rightDiff, y2), cv::Scalar(0, 0, 255), 2);
            }
        }
    }

    double x1 = line1[0];
    double y1 = line1[1];
    double x2 = line1[2];
    double y2 = line1[3];
    double slope1 = (y2 - y1) / (x2 - x1);

    cv::Point2f point0 = cv::Point2f(x1, y1);
    cv::Point2f point1 = cv::Point2f(x2, y2);
    //std::cout << "point0 " << x1 << " " << y1 << std::endl;
    //std::cout << "point1 " << x2 << " " << y2 << std::endl;

    x1 = line2[0];
    y1 = line2[1];
    x2 = line2[2];
    y2 = line2[3];
    double slope2 = (y2 - y1) / (x2 - x1);

    cv::Point2f point2 = cv::Point2f(x1 + rightDiff, y1);
    cv::Point2f point3 = cv::Point2f(x2 + rightDiff, y2);

    //std::cout << "point2 " << x1 + rightDiff << " " << y1 << std::endl;
    //std::cout << "point3 " << x2 + rightDiff << " " << y2 << std::endl;

    cv::Point2f points[4] = { point0, point1, point2, point3 };

    // 将四个点放入vector容器中，以符合fitLine函数对输入数据格式的要求
    std::vector<cv::Point2f> pointSet(points, points + 4);

    cv::Vec4f line = fitLineRANSAC(pointSet);

    // 可视化结果
    visualizeLineFitting(imgColor, pointSet, line, angle, cv::Scalar(0, 0, 255));

    //cv::imwrite("img_color.png", imgColor);

    return imgColor;
}

int imageHandle(cv::Mat& img, std::vector<cv::Point>& group1, int num, int offset, int yoffset) {
    cv::Mat thresh, blurredImage;
    cv::GaussianBlur(img, blurredImage, cv::Size(9, 9), 1);
    //cv::threshold(img, thresh, 150, 255, cv::THRESH_BINARY);
    cv::Mat edges;
    cv::Canny(blurredImage, edges, 50, 150);
//cv::imwrite("Contours.png", edges);
    std::vector<cv::Point> *group;

    std::vector<std::vector<cv::Point>> contours;
    std::vector<cv::Vec4i> hierarchy;
    cv::findContours(edges, contours, hierarchy, cv::RETR_TREE, cv::CHAIN_APPROX_SIMPLE);

    cv::Mat imgColor;
    cv::cvtColor(img, imgColor, cv::COLOR_GRAY2BGR);

    int xMinRange = 10;
    int xMaxRange = 500;
    int yMinRange = 50;
    int yMaxRange = 800;

    std::vector<std::vector<cv::Point>> filteredContours;

    std::map<int, std::vector<cv::Point>> contourLengthMap;

    for (size_t i = 0; i < contours.size(); ++i) {
        
        if (contours[i].size() < num) {
            continue;
        }
        contourLengthMap[contours[i].size()] = contours[i];
        //std::cout << "contours[i].size() " << contours[i].size() << std::endl;

    }

    std::vector<int> sortedContourLengths;
    for (const auto& it : contourLengthMap) {
        sortedContourLengths.push_back(it.first);
    }
    std::sort(sortedContourLengths.begin(), sortedContourLengths.end(), std::greater<int>());

    if (sortedContourLengths.size() < 1) {

        return -1;
    }

    int length = sortedContourLengths[0];
    std::vector<cv::Point> groupContour = contourLengthMap[length];        

    for (const auto& point : groupContour) {
        int x = point.x;
        int y = point.y;
        int px = point.x + offset;
        int py = point.y + yoffset;
        //if (xMinRange <= x && x <= xMaxRange && yMinRange <= y && y <= yMaxRange) {
            //if(img.at<uchar>(point.y, point.x)<100)
                group1.push_back(cv::Point(px, py));
        //}
    }
    return 0;

}

double calculateResidual(const cv::RotatedRect& rr, const cv::Point& point)
{
    double x0 = rr.center.x;
    double y0 = rr.center.y;
    double a = rr.size.width / 2.0;
    double b = rr.size.height / 2.0;
    double theta = rr.angle * CV_PI / 180.0;  // 将角度转换为弧度

    double cosTheta = std::cos(theta);
    double sinTheta = std::sin(theta);

    double term1 = ((point.x - x0) * cosTheta + (point.y - y0) * sinTheta) * ((point.x - x0) * cosTheta + (point.y - y0) * sinTheta) / (a * a);
    double term2 = ((point.y - y0) * cosTheta - (point.x - x0) * sinTheta) * ((point.y - y0) * cosTheta - (point.x - x0) * sinTheta) / (b * b);

    return std::abs(term1 + term2 - 1);
}

int capturePointsBtoT(cv::Mat& edges, vector<Point2f>& points)
{
    // 从下向上遍历图像
    for (int y = edges.rows - 1; y >= 0; y--)
    {
        for (int x = 0; x < edges.cols; x++)
        {
            if (edges.at<uchar>(y, x) == 255)
            {
                points.push_back(Point2f(x, y));
                if (points.size() > 20)
                    break;
            }
        }
        if (points.size() > 20)
            break;
    }

    return 0;
}

int capturePointsTtoB(cv::Mat& edges, vector<Point2f>& points)
{
    // 从上向下遍历图像
    for (int y = 0; y < edges.rows; y++)
    {
        for (int x = 0; x < edges.cols; x++)
        {
            if (edges.at<uchar>(y, x) == 255)
            {
                points.push_back(Point2f(x, y));
                if (points.size() > 20)
                    break;
            }
        }
        if (points.size() > 20)
            break;
    }

    return 0;
}

int capturePointsRtoL(cv::Mat& edges, vector<Point2f>& points)
{
    // 从下向上遍历图像
    for (int x = edges.cols-1; x >=0 ; x--)
    {
        for (int y = edges.rows - 1; y >= 0; y--)
        {
            if (edges.at<uchar>(y, x) == 255)
            {
                points.push_back(Point2f(x, y));
                if (points.size() > 20)
                    break;
            }
        }
        if (points.size() > 20)
            break;
    }

    return 0;
}

int capturePointsLtoR(cv::Mat& edges, vector<Point2f>& points)
{
    // 从下向上遍历图像
    for (int x = 0; x < edges.cols; x++)
    {
        for (int y = edges.rows - 1; y >= 0; y--)
        {
            if (edges.at<uchar>(y, x) == 255)
            {
                points.push_back(Point2f(x, y));
                if (points.size() > 20)
                    break;
            }
        }
        if (points.size() > 20)
            break;
    }

    return 0;
}

bool calculateIntersection(cv::Mat& imgColor, vector<Point2f>& points1, vector<Point2f>& points2, double& x, double& y)
{
    bool flag = false;
    auto l1 = fitLineRANSAC(points1, 3, 50);
    double sin_theta1 = l1[0];
    double cos_theta1 = l1[1];
    double x0_1 = l1[2];
    double y0_1 = l1[3];
    cout << "x0_1:" << x0_1 << " y0_1:" << y0_1 << endl;
    double A1 = cos_theta1;
    double B1 = sin_theta1;
    double C1 = -(x0_1 * cos_theta1 + y0_1 * sin_theta1);   
    if (B1 < 0.9 && B1 > -0.9)
        return false;

    auto l2 = fitLineRANSAC(points2, 3, 50);
    double sin_theta2 = l2[0];
    double cos_theta2 = l2[1];
    double x0_2 = l2[2];
    double y0_2 = l2[3];

    double A2 = cos_theta2;
    double B2 = sin_theta2;
    double C2 = -(x0_2 * cos_theta2 + y0_2 * sin_theta2);
    if (A2 < 0.9 && A2 > -0.9)
        return false;

    cout << "x0_2:" << x0_2 << " y0_2:" << y0_2 << endl;

    cout << "A1:" << A1 << "B1:" << B1 << "C1:" << C1 << endl;
    cout << "A2:" << A2 << "B2:" << B2 << "C2:" << C2 << endl;

    

    if (B1 == 0)
    {
        // 直线1是垂直线
        x = -C1;
        if (B2 == 0)
        {
            // 两条直线都是垂直线，平行，无交点
            flag = false;
        }
        else if (A2 == 0)
        {
            // 直线2是水平线
            y = -C2;
            flag = true;
        }
        else
        {
            y = (-A2 * x - C2) / B2;
            flag = true;
        }
    }
    else if (A1 == 0)
    {
        // 直线1是水平线
        y = -C1;
        if (B2 == 0)
        {
            // 直线2是垂直线
            x = -C2;
            flag = true;
        }
        else if (A2 == 0)
        {
            // 两条直线都是水平线，平行（重合也算平行情况），无交点
            flag = false;
        }
        else
        {
            x = (-B1 * y - C1) / A1;
            flag = true;
        }
    }
    else if (B2 == 0)
    {
        // 直线2是垂直线
        x = -C2;
        y = (-A1 * x - C1) / B1;
        flag = true;
    }
    else if (A2 == 0)
    {
        // 直线2是水平线
        y = -C2;
        x = (-B2 * y - C2) / A2;
        flag = true;
    }
    else
    {        
        x = (B1 * C2 - B2 * C1) / (A1 * B2 - A2 * B1);
        y = (A2 * C1 - A1 * C2) / (A1 * B2 - A2 * B1);
        cout << x << " " << y << endl;
        flag = true;
    }
    if (flag == true)
    {
        cv::circle(imgColor, cv::Point2f(x, y), 2, cv::Scalar(255, 0, 255), -1);
        line(imgColor, Point(x, y), Point(x0_1, y0_1), cv::Scalar(0, 255, 0), 1, cv::LINE_AA);
        line(imgColor, Point(x, y), Point(x0_2, y0_2), cv::Scalar(0, 255, 0), 1, cv::LINE_AA);
    }

    return flag;
}

// 0 左上 1 右上  2 左下  3 右下    
cv::Mat imageLinePreCheck(cv::Mat& img, double& x, double& y, int type) {
    cv::Mat blurredImage;
    cv::GaussianBlur(img, blurredImage, cv::Size(15, 15), 1);
    if (type == 3)
        cv::GaussianBlur(img, blurredImage, cv::Size(9, 9), 1);
    cv::Mat edges;
    cv::Canny(blurredImage, edges, 30, 70);
    cv::Mat imgColor;
    cv::cvtColor(img, imgColor, cv::COLOR_GRAY2BGR);
    vector<Point2f> points1, points2;

    if (type == 0)
    {
        capturePointsTtoB(edges, points1);
        if (points1.size() < 20)
            return cv::Mat();
        capturePointsLtoR(edges, points2);
        if (points2.size() < 20)
            return cv::Mat();
        
    }

    if (type == 1)
    {
        capturePointsTtoB(edges, points1);
        if (points1.size() < 20)
            return cv::Mat();
        capturePointsRtoL(edges, points2);
        if (points2.size() < 20)
            return cv::Mat();

        bool res = calculateIntersection(imgColor, points1, points2, x, y);
        if (!res)
            return cv::Mat();
    }

    if (type == 2)
    {
        capturePointsBtoT(edges, points1);
        if (points1.size() < 20)
            return cv::Mat();
        capturePointsLtoR(edges, points2);
        if (points2.size() < 20)
            return cv::Mat();

        double x, y;
        bool res = calculateIntersection(imgColor, points1, points2, x, y);
        if (!res)
            return cv::Mat();
    }

    if (type == 3)
    {        
        capturePointsBtoT(edges, points1);
        if(points1.size() <20)
            return cv::Mat();
        capturePointsRtoL(edges, points2);
        if (points2.size() < 20)
            return cv::Mat();
    }

    bool res = calculateIntersection(imgColor, points1, points2, x, y);
    if (!res)
        return cv::Mat();

    /*
    cv::namedWindow("Contours Image", cv::WINDOW_NORMAL);
    cv::imshow("Contours Image", imgColor);
    cv::waitKey(0);
    cv::destroyAllWindows();
    */

    return imgColor;

    
    cv::imwrite("img.jpg", img);

    std::vector<cv::Vec4i> lines;

    int times = 0;
    while (times < 3) {
        int v1 = 50, v2 = 90;
        if (type == 2)
        {
            v1 = 40;
            v2 = 70;
        }
        if (type == 3)
        {
            v1 = 30;
            v2 = 30;
        }
        int ret = imageLineFind(edges, lines, v1 - times * 8, v2 - times * 10);
        if (ret == 0)
            break;
        times++;
    }

    std::cout << "imageLineHandleLT 1 " << std::endl;
    
    std::vector<cv::Vec4i> verticalLines;
    std::vector<cv::Vec4i> horizontalLines;

    std::vector<double> lineRight;

    if (!lines.empty()) {
        for (const auto& line : lines) {
            int x1 = line[0];
            int y1 = line[1];
            int x2 = line[2];
            int y2 = line[3];
            std::cout << x1 << " " << y1 << " " << x2 << " " << y2 << std::endl;
            cv::line(imgColor, cv::Point(x1, y1), cv::Point(x2, y2), cv::Scalar(0, 0, 255), 2, 16);

            double slope;
            if (x2 - x1 == 0) {
                slope = std::numeric_limits<double>::infinity();
            }
            else {
                slope = static_cast<double>(y2 - y1) / (x2 - x1);
            }

            std::cout << "slope " << slope << std::endl;

            if (-0.2 <= slope && slope <= 0.2) {

                //cv::line(imgColor, cv::Point(x1, y1), cv::Point(x2, y2), cv::Scalar(0, 0, 255), 2);
                horizontalLines.push_back(line);
            }

            if (slope<-10 || slope>10)
                verticalLines.push_back(line);
        }
    }
    else
    {
        std::cout << "imageLineHandleL empty " << std::endl;
        return cv::Mat();
    }

    cv::namedWindow("Contours Image", cv::WINDOW_NORMAL);
    cv::imshow("Contours Image", imgColor);
    cv::waitKey(0);
    cv::destroyAllWindows();

    std::cout << "horizontalLines.size() " << horizontalLines.size() << std::endl;
    if(horizontalLines.size()==0 || verticalLines.size() == 0)
        return cv::Mat();

    std::vector<std::vector<double>> linesss;
    for (const auto& line : horizontalLines) {
        double x1 = line[0];
        double y1 = line[1];
        double x2 = line[2];
        double y2 = line[3];

        double rho = 0;
        if (x1 == x2) {
            rho = x1;
        }
        else
        {
            double k = (y2 - y1) / (x2 - x1);
            // 计算截距b
            double b = y1 - k * x1;
            // 计算y=0时的x值
            rho = b + 200*k;
        }
        std::cout << "rho " << rho << std::endl;
        double theta = std::atan2(y2 - y1, x2 - x1);
        linesss.push_back({ rho, theta });
    }

    std::vector<double> firstLine;
    cv::Vec4i v1;
    int pos = -1;

    std::vector<std::vector<double>> linessss;
    for (const auto& line : verticalLines) {
        double x1 = line[0];
        double y1 = line[1];
        double x2 = line[2];
        double y2 = line[3];

        double rho = 0;
        rho = (x1 + x2) / 2;
        std::cout << "rho " << rho << std::endl;
        double theta = std::atan2(y2 - y1, x2 - x1);
        linessss.push_back({ rho, theta });
    }

    for (const auto& line : horizontalLines) {
        pos++;
        if (type == 0 || type == 1)
        {
            if (firstLine.empty() || linesss[pos][0] < firstLine[0]) {
                v1 = line;
                firstLine = linesss[pos];
            }
        }
        else
        {
            if (firstLine.empty() || linesss[pos][0] > firstLine[0]) {
                v1 = line;
                firstLine = linesss[pos];
            }
        }
    }

    std::vector<double> secondLine;
    cv::Vec4i v2;
    pos = -1;

    for (const auto& line : verticalLines) {
        pos++;
        if (type == 0 || type == 2)
        {
            if (secondLine.empty() || linessss[pos][0] < secondLine[0]) {
                v2 = line;
                secondLine = linessss[pos];
            }
        }
        else
        {
            if (secondLine.empty() || linessss[pos][0] > secondLine[0]) {
                v2 = line;
                secondLine = linessss[pos];
            }
        }
    }

    int x11 = v1[0];
    int y11 = v1[1];
    int x22 = v1[2];
    int y22 = v1[3];

    cv::line(imgColor, cv::Point(x11, y11), cv::Point(x22, y22), cv::Scalar(0, 255, 0), 2, 16);

    x11 = v2[0];
    y11 = v2[1];
    x22 = v2[2];
    y22 = v2[3];

    cv::line(imgColor, cv::Point(x11, y11), cv::Point(x22, y22), cv::Scalar(0, 255, 0), 2, 16);

    double A1, B1, C1;
    getLineCoefficients(v2[0], v2[1], v2[2], v2[3], A1, B1, C1);

    double A2, B2, C2;
    getLineCoefficients(v1[0], v1[1], v1[2], v1[3], A2, B2, C2);

    //double x = (B1 * C2 - B2 * C1) / (A1 * B2 - A2 * B1);
    // 计算交点的y坐标
    //double y = (A2 * C1 - A1 * C2) / (A1 * B2 - A2 * B1);

    cv::circle(imgColor, cv::Point2f(x, y), 5, cv::Scalar(255, 0, 255), -1);

    cv::namedWindow("Contours Image", cv::WINDOW_NORMAL);
    cv::imshow("Contours Image", imgColor);
    cv::waitKey(0);
    cv::destroyAllWindows();

    return imgColor;
}