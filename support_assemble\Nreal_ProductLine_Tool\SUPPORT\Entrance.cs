﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Text;
using System.Threading;
using System.Windows.Forms;
using ParamManager;
using support_assemble;

namespace Nreal_ProductLine_Tool.SUPPORT
{
	// Token: 0x02000013 RID: 19
	public partial class Entrance : Form
	{
		// Token: 0x06000086 RID: 134 RVA: 0x0000AAF4 File Offset: 0x00008CF4
		public Entrance()
		{
			this.InitializeComponent();
			base.Load += this.MainForm_Load;
			base.FormClosed += this.MainForm_FormClosed;
		}

		// Token: 0x06000087 RID: 135 RVA: 0x0000AB64 File Offset: 0x00008D64
		private void MainForm_Load(object sender, EventArgs e)
		{
			Logs logs = new Logs();
			Logs.WriteInfo("\r\n\r\n", true);
			Logs.WriteInfo("################ start ################", true);
			int num = Configure.Init_Local();
			bool flag = num != 0;
			if (flag)
			{
				MessageBox.Show("请检查本地配置文件 sys.ini ");
				Process.GetCurrentProcess().Kill();
			}
			num = XrPLCom.xrCommInit(1, 0, Encoding.UTF8.GetBytes(LOGINFO.user));
			bool flag2 = num != 0;
			if (flag2)
			{
				MessageBox.Show("请检查网络 ");
			}
			else
			{
				string text = Path.Combine(Application.StartupPath, "Images");
				bool flag3 = !Directory.Exists(text);
				if (flag3)
				{
					Directory.CreateDirectory(text);
				}
				this.device_init();
				this.preCheck = new PreCheck(this, -1, "点检");
				base.WindowState = FormWindowState.Maximized;
				Rectangle workingArea = Screen.FromControl(this).WorkingArea;
				this.mainPanel.Size = new Size(workingArea.Width, workingArea.Height - this.panel1.Height);
				this.mainPanel.Location = new Point(0, this.panel1.Height);
				int num2 = 0;
				FormDefault formDefault = new ScanSN(this, num2, "扫码");
				formDefault.Size = this.mainPanel.ClientSize;
				this.forms.Add(formDefault);
				this.MotionZIndex = num2;
				formDefault = new MotionZ(this, ++num2, "压下压杆");
				formDefault.Size = this.mainPanel.ClientSize;
				this.forms.Add(formDefault);
				this.ColimatorHandleIndex = num2;
				formDefault = new ColimatorHandle(this, ++num2, "准直调整");
				formDefault.Size = this.mainPanel.ClientSize;
				this.forms.Add(formDefault);
				formDefault = new LinkAdjust(this, ++num2, "调整偏心");
				formDefault.Size = this.mainPanel.ClientSize;
				this.forms.Add(formDefault);
				formDefault = new LinkPress(this, ++num2, "压合操作");
				formDefault.Size = this.mainPanel.ClientSize;
				this.forms.Add(formDefault);
				formDefault = new ColimatorUVHandle(this, num2 + 1, "UV操作");
				formDefault.Size = this.mainPanel.ClientSize;
				this.forms.Add(formDefault);
				this.Finish(-1, 0);
			}
		}

		// Token: 0x06000088 RID: 136 RVA: 0x0000ADCB File Offset: 0x00008FCB
		private void MainForm_FormClosed(object sender, FormClosedEventArgs e)
		{
			Environment.Exit(0);
		}

		// Token: 0x06000089 RID: 137 RVA: 0x0000ADD8 File Offset: 0x00008FD8
		private void device_init()
		{
			bool colimator_Use = Configure.Colimator_Use;
			int num;
			if (colimator_Use)
			{
				num = Colimator.Init();
				bool flag = num != 0;
				if (flag)
				{
					MessageBox.Show("准直仪初始化失败");
					Application.Exit();
				}
				Colimator.serialPort = Colimator.serialPort1;
				Colimator.TurnOnColimator();
				Colimator.serialPort = Colimator.serialPort2;
				Colimator.TurnOnColimator();
			}
			num = GSC_Controller.AutoInit();
			bool flag2 = num != 0;
			if (flag2)
			{
				MessageBox.Show("运动轴初始化失败");
			}
			else
			{
				MotionController.SetSpeedHigh();
				GSC_Controller.Home("w", "-+");
				int x_zero_positon;
				int num2;
				bool flag3;
				GSC_Controller.GetAxisStatus(out x_zero_positon, out num2, out flag3);
				GlobalData.x_zero_positon = x_zero_positon;
			}
			num = ZMotionController.Init();
			bool flag4 = num != 0;
			if (flag4)
			{
				MessageBox.Show("IO控制器初始化失败");
			}
			else
			{
				num = Vacuummeter.Init();
				bool flag5 = num != 0;
				if (flag5)
				{
					MessageBox.Show("真空计连接失败");
				}
				else
				{
					num = PressSensor.Init();
					bool flag6 = num != 0;
					if (flag6)
					{
						MessageBox.Show("压力传感器连接失败");
					}
					else
					{
						bool distance_sensor_enable = Configure.distance_sensor_enable;
						if (distance_sensor_enable)
						{
							num = Distance.Init();
							bool flag7 = num != 0;
							if (flag7)
							{
								MessageBox.Show("位移传感器连接失败");
								return;
							}
						}
						bool flag8 = Configure.left_thresh == 0;
						if (flag8)
						{
							Configure.left_thresh = 100;
						}
						bool flag9 = Configure.right_thresh == 0;
						if (flag9)
						{
							Configure.right_thresh = 130;
						}
						num = CameraImageLib.Init_support(Configure.left_thresh, Configure.right_thresh, Configure.roi_x, Configure.roi_y, Configure.roi_width);
						bool flag10 = num != 0;
						if (flag10)
						{
							MessageBox.Show("相机初始化失败 " + num.ToString());
						}
						else
						{
							bool flag11 = Configure.camera_exposure_time < 100;
							if (flag11)
							{
								MessageBox.Show("曝光时间设置过短，请重新设置");
							}
							else
							{
								CameraImageLib.SetExposureTime((double)Configure.camera_exposure_time);
								ZMotionController.VacuummeterGasOutClose();
								Thread.Sleep(100);
								ZMotionController.GasReleaseClose();
								Thread.Sleep(100);
								ZMotionController.VacuummeterPumpClose();
							}
						}
					}
				}
			}
		}

		// Token: 0x0600008A RID: 138 RVA: 0x0000AFC8 File Offset: 0x000091C8
		private void button1_Click(object sender, EventArgs e)
		{
		}

		// Token: 0x0600008B RID: 139 RVA: 0x0000AFCC File Offset: 0x000091CC
		private void sn_tb_KeyUp(object sender, KeyEventArgs e)
		{
			bool flag = e.KeyCode == Keys.Control || e.KeyCode == Keys.Return;
			if (flag)
			{
				this.prism_sn_label.BackColor = Color.Gray;
				string text = this.sn_tb.Text;
				bool flag2 = text.Length < 10;
				if (flag2)
				{
					this.prism_sn_label.Text = "sn长度不对，当前sn为" + text;
					this.prism_sn_label.BackColor = Color.Red;
				}
				else
				{
					bool flag3 = text[1] != 'B';
					if (flag3)
					{
						this.prism_sn_label.Text = "sn类型不对，请扫补偿镜。当前sn为" + text;
						this.prism_sn_label.BackColor = Color.Red;
					}
					else
					{
						GlobalData.sn_buchang_scan = text;
						this.prism_sn_label.Text = text;
						this.sn_tb.Text = "";
						bool flag4 = this.currentIndex == 0 && GlobalData.sn_toujing_scan != "";
						if (flag4)
						{
							this.forms[0].SetEnable(GlobalData.sn_buchang_scan, GlobalData.sn_toujing_scan);
						}
						this.len_sn_tb.Focus();
						Logs.WriteInfo("sn_buchang_scan " + GlobalData.sn_buchang_scan, true);
						Logs.WriteInfo("sn_toujing_scan " + GlobalData.sn_toujing_scan, true);
					}
				}
			}
		}

		// Token: 0x0600008C RID: 140 RVA: 0x0000B130 File Offset: 0x00009330
		private void len_sn_tb_KeyUp(object sender, KeyEventArgs e)
		{
			bool flag = e.KeyCode == Keys.Control || e.KeyCode == Keys.Return;
			if (flag)
			{
				this.len_sn_label.BackColor = Color.Gray;
				string text = this.len_sn_tb.Text;
				bool flag2 = text.Length < 10;
				if (flag2)
				{
					this.len_sn_label.Text = "sn长度不对，当前sn为" + text;
					this.len_sn_label.BackColor = Color.Red;
				}
				else
				{
					bool flag3 = text[1] != 'G';
					if (flag3)
					{
						this.len_sn_label.Text = "sn类型不对，请扫透镜。当前sn为" + text;
						this.len_sn_label.BackColor = Color.Red;
					}
					else
					{
						GlobalData.sn_toujing_scan = text;
						this.len_sn_label.Text = text;
						this.len_sn_tb.Text = "";
						bool flag4 = this.currentIndex == 0 && GlobalData.sn_buchang_scan != "";
						if (flag4)
						{
							this.forms[0].SetEnable(GlobalData.sn_buchang_scan, GlobalData.sn_toujing_scan);
						}
						this.sn_tb.Focus();
						Logs.WriteInfo("sn_buchang_scan " + GlobalData.sn_buchang_scan, true);
						Logs.WriteInfo("sn_toujing_scan " + GlobalData.sn_toujing_scan, true);
					}
				}
			}
		}

		// Token: 0x0600008D RID: 141 RVA: 0x0000B294 File Offset: 0x00009494
		public void Finish(int sequence, int type)
		{
			bool flag = type == 0;
			if (flag)
			{
				bool flag2 = this.currentIndex == 0;
				if (flag2)
				{
					this.len_sn_label.Text = "";
					this.prism_sn_label.Text = "";
				}
				bool flag3 = this.mainPanel.Controls.Count > 0;
				if (flag3)
				{
					this.mainPanel.Controls.Remove(this.forms[sequence]);
				}
				int num = sequence + 1;
				bool flag4 = num >= this.forms.Count;
				if (flag4)
				{
					num = 0;
				}
				this.currentIndex = num;
				this.sequence_label.Text = this.forms[num].GetName();
				this.mainPanel.Controls.Add(this.forms[num]);
				this.forms[num].Start();
			}
			bool flag5 = type == 1;
			if (flag5)
			{
				this.forms[this.currentIndex].ExcepStop();
				this.mainPanel.Controls.Remove(this.forms[this.currentIndex]);
				this.currentIndex = 0;
				this.sequence_label.Text = this.forms[0].GetName();
				this.mainPanel.Controls.Add(this.forms[0]);
				MotionZ.pulse = 0;
				MotionZ.ax_operate_sem.Release();
				this.forms[0].Start();
			}
			bool flag6 = type == 3 || type == 4;
			if (flag6)
			{
				bool flag7 = this.mainPanel.Controls.Count > 0;
				if (flag7)
				{
					this.mainPanel.Controls.Remove(this.forms[sequence]);
				}
				bool flag8 = type == 3;
				int num2;
				if (flag8)
				{
					num2 = this.MotionZIndex + 1;
				}
				else
				{
					num2 = this.ColimatorHandleIndex + 1;
				}
				bool flag9 = num2 >= this.forms.Count;
				if (flag9)
				{
					num2 = 0;
				}
				this.currentIndex = num2;
				this.sequence_label.Text = this.forms[num2].GetName();
				this.mainPanel.Controls.Add(this.forms[num2]);
				this.forms[num2].Start();
			}
			bool flag10 = type == 5;
			if (flag10)
			{
				bool flag11 = this.mainPanel.Controls.Count > 0;
				if (flag11)
				{
					this.mainPanel.Controls.Remove(this.preCheck);
				}
				this.preCheck.Hide();
				int num3 = sequence + 1;
				bool flag12 = num3 >= this.forms.Count;
				if (flag12)
				{
					num3 = 0;
				}
				this.currentIndex = num3;
				this.sequence_label.Text = this.forms[num3].GetName();
				this.mainPanel.Controls.Add(this.forms[num3]);
				this.forms[num3].Start();
			}
		}

		// Token: 0x0600008E RID: 142 RVA: 0x0000B5DC File Offset: 0x000097DC
		private void ReOpen_btn_Click(object sender, EventArgs e)
		{
			this.status_label.Text = "";
			GlobalData.sn_buchang_confirm = "";
			GlobalData.sn_buchang_scan = "";
			GlobalData.sn_toujing_confirm = "";
			GlobalData.sn_toujing_scan = "";
			this.Finish(this.currentIndex, 1);
		}

		// Token: 0x0600008F RID: 143 RVA: 0x0000B634 File Offset: 0x00009834
		private void precheck_btn_Click(object sender, EventArgs e)
		{
			bool flag = this.currentIndex != 0;
			if (flag)
			{
				MessageBox.Show("请回到开始界面");
			}
			else
			{
				bool flag2 = this.mainPanel.Controls.Count > 0;
				if (flag2)
				{
					this.mainPanel.Controls.Remove(this.forms[this.currentIndex]);
				}
				this.mainPanel.Controls.Add(this.preCheck);
				this.preCheck.Start();
			}
		}

		// Token: 0x0400018A RID: 394
		private int currentIndex = 0;

		// Token: 0x0400018B RID: 395
		private int toujing_scan_sequence = 0;

		// Token: 0x0400018C RID: 396
		private int MotionZIndex = 0;

		// Token: 0x0400018D RID: 397
		private int ColimatorHandleIndex = 0;

		// Token: 0x0400018E RID: 398
		private FormDefault preCheck;

		// Token: 0x0400018F RID: 399
		private List<FormDefault> forms = new List<FormDefault>();
	}
}
