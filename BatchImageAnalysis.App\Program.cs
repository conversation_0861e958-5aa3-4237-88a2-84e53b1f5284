using System.CommandLine;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Nreal.BatchImageAnalysis.Core.Services;
using BatchImageAnalysis.Interop;

namespace BatchImageAnalysis.App
{
    class Program
    {
        static async Task<int> Main(string[] args)
        {
            var inputOption = new Option<DirectoryInfo>(
                name: "--input",
                description: "The directory containing images to process.")
            {
                IsRequired = true
            };
            inputOption.AddAlias("-i");

            var outputOption = new Option<DirectoryInfo>(
                name: "--output",
                description: "The directory where processed images will be saved.")
            {
                IsRequired = true
            };
            outputOption.AddAlias("-o");

            var configOption = new Option<FileInfo>(
                name: "--config",
                description: "The path to the sys.ini configuration file.")
            {
                IsRequired = true
            };
            configOption.AddAlias("-c");

            var rootCommand = new RootCommand("Batch Image Analysis Application");
            rootCommand.AddOption(inputOption);
            rootCommand.AddOption(outputOption);
            rootCommand.AddOption(configOption);

            rootCommand.SetHandler(async (inputDir, outputDir, configFile) =>
            {
                await RunProcessing(inputDir.FullName, outputDir.FullName, configFile.FullName);
            }, inputOption, outputOption, configOption);

            return await rootCommand.InvokeAsync(args);
        }

        private static Task RunProcessing(string inputPath, string outputPath, string configPath)
        {
            var host = Host.CreateDefaultBuilder()
                .ConfigureLogging(logging =>
                {
                    logging.ClearProviders();
                    logging.AddConsole();
                })
                .ConfigureServices((context, services) =>
                {
                    services.AddSingleton<ConfigurationService>();
                    services.AddSingleton<IImageAdapterService, ImageAdapterService>();
                    services.AddSingleton<BatchProcessingService>();
                })
                .Build();

            var batchProcessor = host.Services.GetRequiredService<BatchProcessingService>();

            Console.WriteLine($"Starting image processing...");
            Console.WriteLine($"Input directory: {inputPath}");
            Console.WriteLine($"Output directory: {outputPath}");
            Console.WriteLine($"Configuration file: {configPath}");

            batchProcessor.ProcessImages(inputPath, outputPath, configPath);

            Console.WriteLine("Image processing completed successfully.");
            return Task.CompletedTask;
        }
    }
}
