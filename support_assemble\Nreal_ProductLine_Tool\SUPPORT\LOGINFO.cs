﻿using System;

namespace Nreal_ProductLine_Tool.SUPPORT
{
	// Token: 0x02000020 RID: 32
	public class LOGINFO
	{
		// Token: 0x040002CA RID: 714
		public static string mode = "";

		// Token: 0x040002CB RID: 715
		public static string token = "default";

		// Token: 0x040002CC RID: 716
		public static string user = "";

		// Token: 0x040002CD RID: 717
		public static string rname = "default";

		// Token: 0x040002CE RID: 718
		public static string lname = "default";

		// Token: 0x040002CF RID: 719
		public static string pname = "default";

		// Token: 0x040002D0 RID: 720
		public static string configMode = "net";

		// Token: 0x040002D1 RID: 721
		public static string dbWrite = "true";

		// Token: 0x040002D2 RID: 722
		public static string mesEnable = "true";

		// Token: 0x040002D3 RID: 723
		public static string pass = "";

		// Token: 0x040002D4 RID: 724
		public static string reserved2 = "";

		// Token: 0x040002D5 RID: 725
		public static string scan = "true";
	}
}
