#pragma once
#include <opencv2/opencv.hpp>
struct CirclePatternParams : public cv::SimpleBlobDetector::Params {
	/// size ratio from the image show on screen to eye camera
	double sizeRatio;
	/// the radius of the circle in screen image
	double circleRadius;
	/// golden image size
	int imageWidth;
	int imageHeight;

	/// area of the golden pattern in screen image
	double goldenSize;

	/// circle pattern size
	int grid_width;
	int grid_height;

	CirclePatternParams(cv::Size image_size);
};

class CirclePatternDetection {
private:
	cv::Ptr<cv::SimpleBlobDetector> blobDetector_;
	CirclePatternParams params_;

	/// sort the keypoints detect by blob detector
	void FindGrid(std::vector<cv::KeyPoint>& keypoints);

public:
	CirclePatternDetection(cv::Size image_size);
	~CirclePatternDetection();

	/// run blob detector
	void detect(cv::Mat image, std::vector<cv::KeyPoint>& keypoints);
	/// run findCirclesGrid
	void detect(cv::Mat image, std::vector<cv::Point2f>& keypoints);
};


