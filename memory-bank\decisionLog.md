# Decision Log

This file records architectural and implementation decisions using a list format.
2025-07-08 12:25:37 - Log of updates made.

*
      
## Decision

*   [2025-07-08 12:26:16] - 决定创建一个专门的 C# 互操作层 (Interop Layer) 来封装对 `BaslerController.dll` 的 P/Invoke 调用，而不是修改 DLL 或创建“模拟相机”。
      
## Rationale

*   **风险规避**: 修改一个没有源代码的、可能来自第三方的原生 DLL 风险很高，可能会引入不稳定因素或违反许可。
*   **关注点分离**: 将不安全的 P/Invoke 代码（指针操作、内存管理）隔离在一个独立的程序集 (`BatchImageAnalysis.Interop`) 中，使核心业务逻辑代码更清晰、更安全。
*   **利用 .NET 生态**: C# 提供了成熟且易于使用的库（如 `System.Drawing` 或 `ImageSharp`）来处理图像文件的加载和操作，这比在 C++ 中重新实现这些功能要高效得多。
*   **可维护性**: 封装层提供了一个稳定的 C# 接口。即使未来 `BaslerController.dll` 更新，我们也只需要修改这个封装层，而不会影响到上层业务逻辑。

## Implementation Details

*   **项目**: 创建一个名为 `BatchImageAnalysis.Interop` 的 C# 类库项目。
*   **图像加载**: 在该项目中，创建一个 `ImageAdapter` 类，使用 `ImageSharp` 库从文件路径加载图像，并将其数据转换为 `BaslerController.dll` 所需的非托管内存格式（例如，通过 `Marshal.AllocHGlobal` 分配内存并复制像素数据）。
*   **P/Invoke 封装**: 创建一个静态类 `BaslerPInvoke`，其中包含对 `BaslerController.dll` 中所有必需函数的 `[DllImport]` 声明。这些方法将接收 `IntPtr` 或其他非托管类型作为参数。
*   **数据封送**: 定义与 DLL 中使用的 C++ 结构体相匹配的 C# `struct`，并使用 `[StructLayout]` 特性来确保内存布局一致，以便在托管和非托管代码之间正确传递数据。