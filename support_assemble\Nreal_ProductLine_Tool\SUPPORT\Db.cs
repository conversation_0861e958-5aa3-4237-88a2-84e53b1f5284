﻿using System;
using System.IO;
using System.Windows.Forms;
using ParamManager;

namespace Nreal_ProductLine_Tool.SUPPORT
{
	// Token: 0x02000009 RID: 9
	internal class Db
	{
		// Token: 0x06000039 RID: 57 RVA: 0x000076D4 File Offset: 0x000058D4
		public static int dataSaveIntoDB()
		{
			string text = string.Concat(new string[]
			{
				"insert into g_support(SN, LenSN,Station, Project, user, Result,CompensationMirrorCenterX, CompensationMirrorCenterY, LensCenterX, LensCenterY, EdgeDistance, Angle, CompensationMirrorMaxResidual, CompensationMirrorAverageResidual, LensMaxResidual, LensAverageResidual, CenterDistance, Pressure, Distance, DestZ, ColimatorX13, ColimatorY13, ColimatorX2, ColimatorY2, ColimatorDiff, ColimatorX13_preUV, ColimatorY13_preUV, ColimatorX2_preUV, ColimatorY2_preUV, ColimatorDiff_preUV, ColimatorX13_afterUV, ColimatorY13_afterUV, ColimatorX2_afterUV, ColimatorY2_afterUV, ColimatorDiff_afterUV, VacuummeterVal, Peak1, Peak2) values (\"",
				GlobalData.saved_sn_buchang_confirm,
				"\",\"",
				GlobalData.saved_sn_toujing_confirm,
				"\",\"",
				Configure.StationNO,
				"\",\"",
				LOGINFO.pname,
				"\",\"",
				LOGINFO.user,
				"\",\"",
				GlobalData.result,
				"\",",
				GlobalData.CompensationMirrorCenterX.ToString(),
				",",
				GlobalData.CompensationMirrorCenterY.ToString(),
				",",
				GlobalData.LensCenterX.ToString(),
				",",
				GlobalData.LensCenterY.ToString(),
				",",
				GlobalData.EdgeDistance.ToString(),
				",",
				GlobalData.Angle.ToString(),
				",",
				GlobalData.CompensationMirrorMaxResidual.ToString(),
				",",
				GlobalData.CompensationMirrorAverageResidual.ToString(),
				",",
				GlobalData.LensMaxResidual.ToString(),
				",",
				GlobalData.LensAverageResidual.ToString(),
				",",
				GlobalData.CenterDistance.ToString(),
				",",
				GlobalData.pressure_val.ToString(),
				",",
				GlobalData.distance[0].ToString(),
				",",
				GlobalData.link_press_z.ToString(),
				",",
				GlobalData.phrase1_colimatorPoint13Avg[0].ToString(),
				",",
				GlobalData.phrase1_colimatorPoint13Avg[1].ToString(),
				",",
				GlobalData.phrase1_colimatorPoint2[0].ToString(),
				",",
				GlobalData.phrase1_colimatorPoint2[1].ToString(),
				",",
				GlobalData.phrase1_colimatorDiff.ToString(),
				",",
				GlobalData.phrase2_colimatorPoint13Avg[0].ToString(),
				",",
				GlobalData.phrase2_colimatorPoint13Avg[1].ToString(),
				",",
				GlobalData.phrase2_colimatorPoint2[0].ToString(),
				",",
				GlobalData.phrase2_colimatorPoint2[1].ToString(),
				",",
				GlobalData.phrase2_colimatorDiff.ToString(),
				",",
				GlobalData.phrase3_colimatorPoint13Avg[0].ToString(),
				",",
				GlobalData.phrase3_colimatorPoint13Avg[1].ToString(),
				",",
				GlobalData.phrase3_colimatorPoint2[0].ToString(),
				",",
				GlobalData.phrase3_colimatorPoint2[1].ToString(),
				",",
				GlobalData.phrase3_colimatorDiff.ToString(),
				",",
				GlobalData.vacuummeter_value.ToString(),
				",",
				GlobalData.distance[1].ToString(),
				",",
				GlobalData.distance[2].ToString(),
				")"
			});
			Logs.WriteInfo(text, true);
			int num = XrPLCom.excuteGinaDb(LOGINFO.mode, LOGINFO.dbWrite, text);
			bool flag = num != 0;
			int result;
			if (flag)
			{
				Logs.WriteInfo("excuteDb " + num.ToString(), true);
				MessageBox.Show("数据库写入失败");
				result = num;
			}
			else
			{
				result = 0;
			}
			return result;
		}

		// Token: 0x0600003A RID: 58 RVA: 0x00007AE4 File Offset: 0x00005CE4
		public static int dataSaveIntoPreCheckDB(double CenterX, double CenterY, double ColimatorX, double ColimatorY, double ColimatorD, double ColimatorX2, double ColimatorY2, double ColimatorD2)
		{
			string text = string.Concat(new string[]
			{
				"insert into g_support_dianjian(Station, CenterX, CenterY, ColimatorX, ColimatorY, ColimatorZ, ColimatorX2, ColimatorY2, ColimatorZ2) values (\"",
				Configure.StationNO,
				"\",",
				CenterX.ToString(),
				",",
				CenterY.ToString(),
				",",
				ColimatorX.ToString(),
				",",
				ColimatorY.ToString(),
				",",
				ColimatorD.ToString(),
				",",
				ColimatorX2.ToString(),
				",",
				ColimatorY2.ToString(),
				",",
				ColimatorD2.ToString(),
				")"
			});
			Logs.WriteInfo(text, true);
			int num = XrPLCom.excuteGinaDb(LOGINFO.mode, LOGINFO.dbWrite, text);
			bool flag = num != 0;
			int result;
			if (flag)
			{
				Logs.WriteInfo("excuteDb " + num.ToString(), true);
				MessageBox.Show("数据库写入失败");
				result = num;
			}
			else
			{
				result = 0;
			}
			return result;
		}

		// Token: 0x0600003B RID: 59 RVA: 0x00007C00 File Offset: 0x00005E00
		public static int SaveData()
		{
			string str = DateTime.Now.ToString("yyyy-MM-dd");
			string text = "data";
			string text2 = Path.Combine(text, str + ".csv");
			bool flag = !Directory.Exists(text);
			if (flag)
			{
				Directory.CreateDirectory(text);
			}
			try
			{
				bool flag2 = !File.Exists(text2);
				if (flag2)
				{
					using (StreamWriter streamWriter = new StreamWriter(text2))
					{
						streamWriter.WriteLine("time,prism_sn, len_sn, phrase1_p1_x,phrase1_p1_y,phrase1_p3_x,phrase1_p3_y,phrase1_p13_x,phrase1_p13_y,phrase1_p2_x,phrase1_p2_y,phrase1_diff,phrase2_p1_x,phrase2_p1_y, phrase2_p3_x, phrase2_p3_y, phrase2_p13_x, phrase2_p13_y,phrase2_p2_x,phrase2_p2_y,phrase2_diff,phrase3_p1_x,phrase3_p1_y,phrase3_p3_x,phrase3_p3_y,phrase3_p13_x,phrase3_p13_y,phrase3_p2_x,phrase3_p2_y,phrase3_diff");
					}
				}
				string text3 = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
				using (StreamWriter streamWriter2 = new StreamWriter(text2, true))
				{
					string text4 = string.Concat(new string[]
					{
						text3,
						",",
						GlobalData.saved_sn_buchang_confirm,
						",",
						GlobalData.saved_sn_toujing_confirm,
						",",
						string.Join<double>(",", GlobalData.phrase1_colimatorPoint1),
						",",
						string.Join<double>(",", GlobalData.phrase1_colimatorPoint3),
						",",
						string.Join<double>(",", GlobalData.phrase1_colimatorPoint13Avg),
						",",
						string.Join<double>(",", GlobalData.phrase1_colimatorPoint2),
						",",
						GlobalData.phrase1_colimatorDiff.ToString(),
						",",
						string.Join<double>(",", GlobalData.phrase2_colimatorPoint1),
						",",
						string.Join<double>(",", GlobalData.phrase2_colimatorPoint3),
						",",
						string.Join<double>(",", GlobalData.phrase2_colimatorPoint13Avg),
						",",
						string.Join<double>(",", GlobalData.phrase2_colimatorPoint2),
						",",
						GlobalData.phrase2_colimatorDiff.ToString(),
						",",
						string.Join<double>(",", GlobalData.phrase3_colimatorPoint1),
						",",
						string.Join<double>(",", GlobalData.phrase3_colimatorPoint3),
						",",
						string.Join<double>(",", GlobalData.phrase3_colimatorPoint13Avg),
						",",
						string.Join<double>(",", GlobalData.phrase3_colimatorPoint2),
						",",
						GlobalData.phrase3_colimatorDiff.ToString()
					});
					streamWriter2.WriteLine(string.Join(",", new string[]
					{
						text4
					}));
				}
				Console.WriteLine("数据已成功递增插入到CSV文件中。");
			}
			catch (Exception ex)
			{
				Console.WriteLine("插入数据到CSV文件时出错: " + ex.Message);
				return -1;
			}
			return 0;
		}
	}
}
