using IniParser.Model;

namespace Nreal.BatchImageAnalysis.Core.Models
{
    public class AnalysisConfig
    {
        public float CameraExposureTime { get; set; }
        public float CameraGain { get; set; }
        public int LeftThresh { get; set; }
        public int RightThresh { get; set; }
        public int RoiX { get; set; }
        public int RoiY { get; set; }
        public int RoiWidth { get; set; }
        public int RoiHeight { get; set; }

        public static AnalysisConfig FromIniData(KeyDataCollection iniData)
        {
            return new AnalysisConfig
            {
                CameraExposureTime = float.Parse(iniData["Product#camera_exposure_time"]),
                CameraGain = float.Parse(iniData["Product#camera_gain"]),
                LeftThresh = int.Parse(iniData["Product#left_thresh"]),
                RightThresh = int.Parse(iniData["Product#right_thresh"]),
                RoiX = int.Parse(iniData["Product#roi_x"]),
                RoiY = int.Parse(iniData["Product#roi_y"]),
                RoiWidth = int.Parse(iniData["Product#roi_width"]),
                RoiHeight = int.Parse(iniData["Product#roi_height"])
            };
        }
    }
}