﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.Threading;
using System.Windows.Forms;
using Nreal_ProductLine_Tool.SUPPORT;

namespace support_assemble
{
	// Token: 0x02000004 RID: 4
	public partial class Vacuum : FormDefault
	{
		// Token: 0x06000015 RID: 21 RVA: 0x000041F0 File Offset: 0x000023F0
		public Vacuum(Entrance entrance, int sequence, string title)
		{
			this.sequence = sequence;
			this.entrance = entrance;
			this.title = title;
			this.InitializeComponent();
			base.TopLevel = false;
			base.FormBorderStyle = FormBorderStyle.None;
			this.Dock = DockStyle.Fill;
			this.vacu_operate_sem = new SemaphoreSlim(0, 1);
			Thread thread = new Thread(new ThreadStart(this.vacu_operate));
			thread.Start();
			this.axis_operate_sem = new SemaphoreSlim(0, 1);
			Thread thread2 = new Thread(new ThreadStart(this.axis_operate));
			thread2.Start();
		}

		// Token: 0x06000016 RID: 22 RVA: 0x00004294 File Offset: 0x00002494
		public override string GetName()
		{
			return this.title;
		}

		// Token: 0x06000017 RID: 23 RVA: 0x000042AC File Offset: 0x000024AC
		public override void Start()
		{
			Vacuum.isExcepExited = false;
			this.status_label.Text = "等待关门信号";
			this.time_label.Text = "";
			this.vacum_val_label.Text = "";
			this.vacu_operate_sem.Release();
			base.Show();
		}

		// Token: 0x06000018 RID: 24 RVA: 0x00004308 File Offset: 0x00002508
		public override void ExcepStop()
		{
			Vacuum.isExcepExited = true;
			ZMotionController.VacuummeterPumpClose();
			Thread.Sleep(100);
			ZMotionController.GasReleaseClose();
			bool flag = this.motion_type == 1;
			if (flag)
			{
				GSC_Controller.Stop("1");
			}
			bool flag2 = this.motion_type == 2;
			if (flag2)
			{
				GSC_Controller.Stop("2");
			}
		}

		// Token: 0x06000019 RID: 25 RVA: 0x00004360 File Offset: 0x00002560
		private void vacu_button_Click(object sender, EventArgs e)
		{
			Vacuum.isExcepExited = true;
			base.Hide();
			this.entrance.Finish(this.sequence, 0);
		}

		// Token: 0x0600001A RID: 26 RVA: 0x00004384 File Offset: 0x00002584
		private void vacu_operate()
		{
			while (!Vacuum.isExited)
			{
				this.vacu_operate_sem.Wait();
				this.motion_type = 1;
				this.axis_operate_sem.Release();
				Logs.WriteInfo("开始抽真空", true);
				this.status_label.Invoke(new MethodInvoker(delegate()
				{
					this.status_label.Text = "开始抽真空";
					this.vacu_setting_time_label.Text = (Configure.vacuummeter_pump_time.ToString() ?? "");
				}));
				Logs.WriteInfo("打开真空泵", true);
				ZMotionController.VacuummeterPumpOpen();
				Thread.Sleep(100);
				Logs.WriteInfo("打开排气阀", true);
				ZMotionController.GasReleaseOpen();
				this.motion_type = 2;
				this.axis_operate_sem.Release();
				int count_down = Configure.vacuummeter_pump_time;
				double vacuummeter_val = 10000.0;
				while (count_down > 0)
				{
					Vacuummeter.getVacuummeter(ref vacuummeter_val);
					Logs.WriteInfo("真空值:" + vacuummeter_val.ToString(), true);
					base.Invoke(new MethodInvoker(delegate()
					{
						this.time_label.Text = (count_down.ToString() ?? "");
						this.vacum_val_label.Text = (vacuummeter_val.ToString() ?? "");
					}));
					int count_down2 = count_down;
					count_down = count_down2 - 1;
					Thread.Sleep(1000);
				}
				while (!Vacuum.isExcepExited)
				{
					bool flag = vacuummeter_val < Configure.vacuummeter_value;
					if (flag)
					{
						break;
					}
					Vacuummeter.getVacuummeter(ref vacuummeter_val);
					Logs.WriteInfo("真空值:" + vacuummeter_val.ToString(), true);
					base.Invoke(new MethodInvoker(delegate()
					{
						this.vacum_val_label.Text = (vacuummeter_val.ToString() ?? "");
					}));
					Thread.Sleep(100);
				}
				bool flag2 = Vacuum.isExcepExited;
				if (!flag2)
				{
					base.Invoke(new MethodInvoker(delegate()
					{
						base.Hide();
						this.entrance.Finish(this.sequence, 0);
					}));
				}
			}
		}

		// Token: 0x0600001B RID: 27 RVA: 0x0000454C File Offset: 0x0000274C
		private void axis_operate()
		{
			while (!Vacuum.isExited)
			{
				this.axis_operate_sem.Wait();
				int i;
				for (i = 0; i < 10; i++)
				{
					bool flag = !MotionController.running;
					if (flag)
					{
						break;
					}
					Thread.Sleep(200);
				}
				bool flag2 = i == 10;
				if (flag2)
				{
					MessageBox.Show("当前电机繁忙，请重新开始");
				}
				else
				{
					MotionController.running = true;
					bool flag3 = this.motion_type == 1;
					if (flag3)
					{
						GSC_Controller.Home("1", "-");
					}
					else
					{
						bool flag4 = this.motion_type == 2;
						if (flag4)
						{
							GSC_Controller.MoveAbs("2", Configure.zDest2);
						}
					}
					MotionController.running = false;
				}
			}
		}

		// Token: 0x0400002F RID: 47
		private Entrance entrance;

		// Token: 0x04000030 RID: 48
		private int sequence;

		// Token: 0x04000031 RID: 49
		private int type;

		// Token: 0x04000032 RID: 50
		public string title;

		// Token: 0x04000033 RID: 51
		protected SemaphoreSlim vacu_operate_sem;

		// Token: 0x04000034 RID: 52
		protected SemaphoreSlim axis_operate_sem;

		// Token: 0x04000035 RID: 53
		public static bool isExited;

		// Token: 0x04000036 RID: 54
		public static bool isExcepExited;

		// Token: 0x04000037 RID: 55
		private int motion_type = 0;
	}
}
