# 批量图像分析工具

## 项目简介

本项目是一个 C# 命令行工具，用于对指定目录中的图像进行批量分析和处理。它利用原生的 `BaslerController.dll` 库来执行核心图像分析任务，并通过一个分层的 .NET 应用程序架构进行封装和管理，实现了高效、可维护的图像处理流程。

## 架构概览

项目采用了清晰的三层架构，以实现关注点分离（Separation of Concerns）：

*   **`BatchImageAnalysis.App` (表示层)**
    *   这是一个 C# 控制台应用程序，作为整个工具的用户入口。
    *   它负责解析命令行参数（输入/输出路径、配置文件），并初始化和协调核心处理服务。
    *   使用了 `System.CommandLine` 库来构建用户友好的命令行界面。

*   **`BatchImageAnalysis.Core` (核心逻辑层)**
    *   这是一个 C# 类库，包含了应用程序的核心业务逻辑。
    *   `BatchProcessingService` 负责编排整个图像处理流程，包括遍历输入文件、调用分析服务以及保存结果。
    *   `ConfigurationService` 负责加载和解析 `sys.ini` 配置文件。

*   **`BatchImageAnalysis.Interop` (互操作层)**
    *   这是一个专门用于与原生 C++ `BaslerController.dll` 进行交互的 C# 类库。
    *   它使用 P/Invoke (`DllImport`) 来调用 DLL 中的原生函数。
    *   `ImageAdapterService` 负责将 .NET 图像对象（从文件加载）转换为 DLL 所需的非托管内存格式。
    *   通过将所有不安全和复杂的互操作代码隔离在该层，确保了核心业务逻辑的整洁和安全。

## 构建说明

你可以使用标准的 `dotnet build` 命令来构建整个解决方案。请在项目的根目录下（包含 `BatchImageAnalysis.sln` 文件的目录）运行以下命令：

```bash
dotnet build BatchImageAnalysis.sln --configuration Release
```

构建成功后，你可以在各个项目的 `bin/Release` 目录下找到相应的程序集。

## 使用方法

构建完成后，你可以使用 `dotnet run` 命令来启动应用程序。由于根目录中存在多个项目，你需要明确指定要运行的项目。

推荐使用以下命令格式，从项目根目录运行：

```bash
dotnet run --project BatchImageAnalysis.App/BatchImageAnalysis.App.csproj -- [arguments]
```

这可以确保 `dotnet` 命令知道要执行哪个项目。

该工具需要三个必需的命令行参数：

*   `--input` 或 `-i`: 指定包含待处理图像的输入目录。
*   `--output` 或 `-o`: 指定用于保存处理后图像的输出目录。
*   `--config` 或 `-c`: 指定 `sys.ini` 配置文件的路径。

### 命令行示例

以下是一个完整的使用示例。假设你的目录结构如下：

```
/my_project/
|-- images_to_process/
|   |-- image1.png
|   |-- image2.jpg
|-- processed_images/
|-- config/
|   |-- sys.ini
|-- BatchImageAnalysis.sln
|-- BatchImageAnalysis.App/
|   |-- BatchImageAnalysis.App.csproj
|-- ... (其他项目文件)
```

你可以运行以下命令来处理 `images_to_process` 目录中的所有图像，并将结果保存到 `processed_images` 目录：

```bash
dotnet run --project BatchImageAnalysis.App/BatchImageAnalysis.App.csproj -- --input "C:\path\to\your\images" --output "C:\path\to\save\results" --config "C:\path\to\your\sys.ini"
```

**注意**:
*   请将 `"C:\path\to..."` 替换为你的实际文件路径。
*   在 `--project` 参数后使用 `--` 分隔符至关重要。它告诉 `dotnet` 驱动程序，后面的所有参数（如 `--input`, `--output`）都应直接传递给你的应用程序，而不是由 `dotnet` 命令自身解析。