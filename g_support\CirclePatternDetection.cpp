#include "pch.h"
#include "CirclePatternDetection.h"
#include <stdint.h>
#

CirclePatternParams::CirclePatternParams(cv::Size image_size) {
    grid_width = 5;
    grid_height = 5;

    circleRadius = 50.0;
    imageWidth = 1920;
    imageHeight = 1080;

    sizeRatio = (double)image_size.width / (double)imageWidth;

    /// Use different thresholds for binarization, Split out the blob
    minThreshold = 50;
    maxThreshold = 120;
    thresholdStep = 30;
    /// The min number of occurrences of each point
    minRepeatability = 1;

    minDistBetweenBlobs = 10.0 * sizeRatio;

    /// reject outlier by color
    filterByColor = false;
    blobColor = 255;

    /// reject outlier by size
    filterByArea = true;
    goldenSize = CV_PI * (circleRadius * sizeRatio * circleRadius * sizeRatio);
    maxArea = 2 * goldenSize;
    minArea = 0.4 * goldenSize;

    /// reject outlier by Circularity
    filterByCircularity = true;
    minCircularity = 0.8f;
    maxCircularity = std::numeric_limits<float>::max();

    /// reject outlier by rotational inertia
    filterByInertia = true;
    minInertiaRatio = 0.8f;
    maxInertiaRatio = std::numeric_limits<float>::max();

    /// reject outlier by convexity
    filterByConvexity = true;
    minConvexity = 0.9f;
    maxConvexity = std::numeric_limits<float>::max();
}

CirclePatternDetection::CirclePatternDetection(cv::Size image_size)
    : params_(image_size) {
    blobDetector_ = cv::SimpleBlobDetector::create(params_);
}

CirclePatternDetection::~CirclePatternDetection() {}

void CirclePatternDetection::detect(cv::Mat image,
    std::vector<cv::KeyPoint>& keypoints) {
    blobDetector_->detect(image, keypoints);
    //cv::Mat vis;
    //cv::cvtColor(image, vis, cv::COLOR_GRAY2BGR);
    //for (auto p : keypoints) {
    //  cv::circle(vis, p.pt, p.size / 2, cv::Scalar(0, 0, 255), 3);
    //}
    //cv::resize(vis, resizeMat, cv::Size(960, 850));
    //cv::imshow("resizeMat", resizeMat);
    //std::cout << "blob detector keypoints count = " << keypoints.size() << std::endl;
    //cv::waitKey(0);

    std::vector<cv::KeyPoint> inliers;
    int check_points = 32;
    double delta_angle = CV_2PI / (double)check_points;
    for (int i = 0; i < keypoints.size(); i++) {
        double r_in = keypoints[i].size / 2 * 0.9;
        double r_out = keypoints[i].size / 2 * 1.05;
        int miss_count_in = 0;
        int miss_count_out = 0;
        for (int j = 0; j < check_points; j++) {
            cv::Point2f p(std::cos(delta_angle * j), std::sin(delta_angle * j));
            cv::Point2f p_in = p * r_in + keypoints[i].pt;
            cv::Point2f p_out = p * r_out + keypoints[i].pt;
            if (p_out.x < 0 || p_out.y < 0 || p_out.x > image.cols - 1 ||
                p_out.y > image.rows - 1) {
                miss_count_in++;
                continue;
            }
            miss_count_in += image.at<uint8_t>(p_in) < params_.maxThreshold ? 1 : 0;
            miss_count_out += image.at<uint8_t>(p_out) > params_.minThreshold ? 1 : 0;
        }
        if (miss_count_in > check_points / 4 && miss_count_out > check_points / 4) {
            continue;
        }
        inliers.push_back(keypoints[i]);
    }
    keypoints = inliers;

    //for (auto p : keypoints) {
    //  cv::circle(vis, p.pt, p.size / 2, cv::Scalar(0, 255, 0), 3);
    //}
    //cv::Mat resizeMat;
    //cv::resize(vis, resizeMat, cv::Size(960, 850));
    //cv::imshow("resizeMat", resizeMat);
    //std::cout << "inliers keypoints count = " << keypoints.size() << std::endl;
    //cv::waitKey(0);

    FindGrid(keypoints);
}

void CirclePatternDetection::detect(cv::Mat image,
    std::vector<cv::Point2f>& keypoints) {
    cv::findCirclesGrid(image, cv::Size(params_.grid_width, params_.grid_height),
        keypoints, cv::CALIB_CB_SYMMETRIC_GRID, blobDetector_);
}

void CirclePatternDetection::FindGrid(std::vector<cv::KeyPoint>& keypoints) {
    if (keypoints.size() != params_.grid_width * params_.grid_height) {
        return;
    }
    std::vector<cv::KeyPoint> sorted_keypoints = keypoints;
    /// sort by distance to left top corner
    std::sort(sorted_keypoints.begin(), sorted_keypoints.end(),
        [](const cv::KeyPoint& a, const cv::KeyPoint& b) -> bool {
            return a.pt.dot(a.pt) < b.pt.dot(b.pt);
        });
    cv::KeyPoint next_row_first_point = sorted_keypoints[0];
    /// sort every row
    for (int i = 0; i < params_.grid_height - 1; i++) {
        cv::KeyPoint first_point = next_row_first_point;
        /// distance to current row first corner
        std::sort(
            sorted_keypoints.begin() + i * params_.grid_width,
            sorted_keypoints.end(),
            [first_point](const cv::KeyPoint& a, const cv::KeyPoint& b) -> bool {
                auto diff_a = first_point.pt - a.pt;
                auto diff_b = first_point.pt - b.pt;
                return diff_a.dot(diff_a) < diff_b.dot(diff_b);
            });
        /// find the points in current row. Two points form a straight line
        cv::KeyPoint second_point = sorted_keypoints[i * params_.grid_width + 2];
        next_row_first_point = sorted_keypoints[i * params_.grid_width + 1];
        cv::Point2f delta_p = second_point.pt - first_point.pt;
        /// A*x + B*y + C = 0
        /// d = abs(A*x0+B*y0+C) / sqrt(A*A+B*B)
        /// when [A, B].norm = 1, d = abs(A*x0+B*y0+C)
        double A = delta_p.y / cv::norm(delta_p);
        double B = -delta_p.x / cv::norm(delta_p);
        double C = (second_point.pt.y * first_point.pt.x -
            second_point.pt.x * first_point.pt.y);
        /// sort by the distance from point to line
        std::sort(sorted_keypoints.begin() + i * params_.grid_width,
            sorted_keypoints.end(),
            [A, B, C](const cv::KeyPoint& a, const cv::KeyPoint& b) -> bool {
                return fabs(A * a.pt.x + B * a.pt.y + C) <
                    fabs(A * b.pt.x + B * b.pt.y + C);
            });
        /// current row from left to right
        std::sort(sorted_keypoints.begin() + i * params_.grid_width,
            sorted_keypoints.begin() + (i + 1) * params_.grid_width,
            [](const cv::KeyPoint& a, const cv::KeyPoint& b) -> bool {
                return a.pt.x < b.pt.x;
            });
    }
    /// The remaining points make up the last line, sort from left to right
    std::sort(sorted_keypoints.begin() +
        (params_.grid_height - 1) * params_.grid_width,
        sorted_keypoints.end(),
        [](const cv::KeyPoint& a, const cv::KeyPoint& b) -> bool {
            return a.pt.x < b.pt.x;
        });
    keypoints = sorted_keypoints;
}

