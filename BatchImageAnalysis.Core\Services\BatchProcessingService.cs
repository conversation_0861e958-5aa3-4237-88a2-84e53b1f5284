using BatchImageAnalysis.Interop;
using Nreal.BatchImageAnalysis.Core.Models;
using System;
using System.IO;

namespace Nreal.BatchImageAnalysis.Core.Services
{
    public class BatchProcessingService
    {
        private readonly IImageAdapterService _imageAdapterService;
        private readonly ConfigurationService _configurationService;

        public BatchProcessingService(IImageAdapterService imageAdapterService, ConfigurationService configurationService)
        {
            _imageAdapterService = imageAdapterService;
            _configurationService = configurationService;
        }

        public void ProcessImages(string inputPath, string outputPath, string configPath)
        {
            Console.WriteLine("Loading configuration...");
            var config = _configurationService.LoadConfig(configPath);

            Console.WriteLine("Initializing image processor...");
            // 使用配置参数初始化图像适配器服务
            _imageAdapterService.Initialize(
                config.LeftThresh,
                config.RightThresh,
                config.RoiX,
                config.RoiY,
                config.RoiWidth,
                config.RoiX, // 使用 RoiX 作为 leftX 的默认值
                config.RoiX + config.RoiWidth // 使用 RoiX + RoiWidth 作为 rightX 的默认值
            );

            var imageFiles = Directory.GetFiles(inputPath, "*.png");
            Console.WriteLine($"Found {imageFiles.Length} images to process.");

            foreach (var imageFile in imageFiles)
            {
                try
                {
                    Console.WriteLine($"Processing {Path.GetFileName(imageFile)}...");
                    var result = _imageAdapterService.AnalyzeImage(imageFile);
                    // For now, just log success or failure
                    Console.WriteLine($" -> Success: {result.Success}, Message: {result.ErrorMessage}");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($" -> Error processing {Path.GetFileName(imageFile)}: {ex.Message}");
                }
            }
            Console.WriteLine("Batch processing complete.");
        }
    }
}