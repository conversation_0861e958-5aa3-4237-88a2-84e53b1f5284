using BatchImageAnalysis.Interop;
using Nreal.BatchImageAnalysis.Core.Models;
using System;
using System.IO;

namespace Nreal.BatchImageAnalysis.Core.Services
{
    public class BatchProcessingService
    {
        private readonly IImageAdapterService _imageAdapterService;
        private readonly ConfigurationService _configurationService;

        public BatchProcessingService(IImageAdapterService imageAdapterService, ConfigurationService configurationService)
        {
            _imageAdapterService = imageAdapterService;
            _configurationService = configurationService;
        }

        public void ProcessImages(string inputPath, string outputPath, string configPath)
        {
            Console.WriteLine("Loading configuration...");
            var config = _configurationService.LoadConfig(configPath);

            Console.WriteLine("Initializing image processor...");
            // 使用配置参数初始化图像适配器服务
            _imageAdapterService.Initialize(
                config.LeftThresh,
                config.RightThresh,
                config.RoiX,
                config.RoiY,
                config.RoiWidth,
                config.RoiX, // 使用 RoiX 作为 leftX 的默认值
                config.RoiX + config.RoiWidth // 使用 RoiX + RoiWidth 作为 rightX 的默认值
            );

            // 确保输出目录存在
            if (!Directory.Exists(outputPath))
            {
                Directory.CreateDirectory(outputPath);
                Console.WriteLine($"Created output directory: {outputPath}");
            }

            var imageFiles = Directory.GetFiles(inputPath, "*.png");
            Console.WriteLine($"Found {imageFiles.Length} images to process.");

            // 创建结果日志文件
            var logFilePath = Path.Combine(outputPath, "analysis_results.txt");
            using (var logWriter = new StreamWriter(logFilePath))
            {
                logWriter.WriteLine($"Batch Image Analysis Results - {DateTime.Now}");
                logWriter.WriteLine($"Input Directory: {inputPath}");
                logWriter.WriteLine($"Output Directory: {outputPath}");
                logWriter.WriteLine($"Configuration File: {configPath}");
                logWriter.WriteLine(new string('-', 50));

                foreach (var imageFile in imageFiles)
                {
                    try
                    {
                        Console.WriteLine($"Processing {Path.GetFileName(imageFile)}...");
                        var result = _imageAdapterService.AnalyzeImage(imageFile);

                        // 记录结果到控制台和日志文件
                        var resultMessage = $" -> Success: {result.Success}, Message: {result.ErrorMessage}";
                        Console.WriteLine(resultMessage);

                        logWriter.WriteLine($"File: {Path.GetFileName(imageFile)}");
                        logWriter.WriteLine($"  Success: {result.Success}");
                        if (!string.IsNullOrEmpty(result.ErrorMessage))
                        {
                            logWriter.WriteLine($"  Error: {result.ErrorMessage}");
                        }
                        logWriter.WriteLine();

                        // 复制原始图像到输出目录（作为处理记录）
                        var outputImagePath = Path.Combine(outputPath, Path.GetFileName(imageFile));
                        File.Copy(imageFile, outputImagePath, overwrite: true);
                    }
                    catch (Exception ex)
                    {
                        var errorMessage = $" -> Error processing {Path.GetFileName(imageFile)}: {ex.Message}";
                        Console.WriteLine(errorMessage);

                        logWriter.WriteLine($"File: {Path.GetFileName(imageFile)}");
                        logWriter.WriteLine($"  Error: {ex.Message}");
                        logWriter.WriteLine();
                    }
                }
            }

            Console.WriteLine("Batch processing complete.");
            Console.WriteLine($"Results saved to: {logFilePath}");
        }
    }
}