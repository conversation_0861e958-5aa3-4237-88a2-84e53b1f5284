using BatchImageAnalysis.Core.Models;
using BatchImageAnalysis.Interop;
using System;
using System.IO;

namespace BatchImageAnalysis.Core.Services
{
    public class BatchProcessingService
    {
        private readonly IImageAdapterService _imageAdapterService;
        private readonly ConfigurationService _configurationService;

        public BatchProcessingService(IImageAdapterService imageAdapterService, ConfigurationService configurationService)
        {
            _imageAdapterService = imageAdapterService;
            _configurationService = configurationService;
        }

        public void ProcessImages(string inputPath, string outputPath, string configPath)
        {
            Console.WriteLine("Loading configuration...");
            var config = _configurationService.LoadConfig(configPath);

            Console.WriteLine("Initializing image processor...");
            // Initialize is no longer part of the service, it's handled by the implementation

            var imageFiles = Directory.GetFiles(inputPath, "*.png");
            Console.WriteLine($"Found {imageFiles.Length} images to process.");

            foreach (var imageFile in imageFiles)
            {
                try
                {
                    Console.WriteLine($"Processing {Path.GetFileName(imageFile)}...");
                    var result = _imageAdapterService.AnalyzeImage(imageFile);
                    // For now, just log success or failure
                    Console.WriteLine($" -> Success: {result.Success}, Message: {result.ErrorMessage}");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($" -> Error processing {Path.GetFileName(imageFile)}: {ex.Message}");
                }
            }
            Console.WriteLine("Batch processing complete.");
        }
    }
}