# System Patterns *Optional*

This file documents recurring patterns and standards used in the project.
It is optional, but recommended to be updated as the project evolves.
2025-07-08 12:25:45 - Log of updates made.

*

## Coding Patterns

*   

## Architectural Patterns

*   **解决方案结构**:
    *   **`BatchImageAnalysis.sln`** (解决方案文件)
        *   **`BatchImageAnalysis.App`** (C# 控制台应用, .NET)
            *   *职责*: 用户交互、命令行解析、流程启动。
        *   **`BatchImageAnalysis.Core`** (C# 类库, .NET)
            *   *职责*: 核心业务逻辑、服务编排。
        *   **`BatchImageAnalysis.Interop`** (C# 类库, .NET)
            *   *职责*: 封装 P/Invoke 调用，与 `BaslerController.dll` 交互。
*   **命名空间 (Namespaces)**:
    *   `Nreal.BatchImageAnalysis.App`
    *   `Nreal.BatchImageAnalysis.Core`
    *   `Nreal.BatchImageAnalysis.Interop`

## Testing Patterns

*