﻿namespace Nreal_ProductLine_Tool.SUPPORT
{
	// Token: 0x02000013 RID: 19
	public partial class Entrance : global::System.Windows.Forms.Form
	{
		// Token: 0x06000090 RID: 144 RVA: 0x0000B6BC File Offset: 0x000098BC
		protected override void Dispose(bool disposing)
		{
			bool flag = disposing && this.components != null;
			if (flag)
			{
				this.components.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x06000091 RID: 145 RVA: 0x0000B6F4 File Offset: 0x000098F4
		private void InitializeComponent()
		{
			this.mainPanel = new global::System.Windows.Forms.Panel();
			this.panel1 = new global::System.Windows.Forms.Panel();
			this.prism_sn_label = new global::System.Windows.Forms.Label();
			this.len_sn_label = new global::System.Windows.Forms.Label();
			this.len_sn_tb = new global::System.Windows.Forms.TextBox();
			this.label1 = new global::System.Windows.Forms.Label();
			this.sequence_label = new global::System.Windows.Forms.Label();
			this.ReOpen_btn = new global::System.Windows.Forms.Button();
			this.status_label = new global::System.Windows.Forms.Label();
			this.sn_tb = new global::System.Windows.Forms.TextBox();
			this.sn_label = new global::System.Windows.Forms.Label();
			this.precheck_btn = new global::System.Windows.Forms.Button();
			this.panel1.SuspendLayout();
			base.SuspendLayout();
			this.mainPanel.Location = new global::System.Drawing.Point(51, 109);
			this.mainPanel.Name = "mainPanel";
			this.mainPanel.Size = new global::System.Drawing.Size(666, 495);
			this.mainPanel.TabIndex = 0;
			this.panel1.Controls.Add(this.precheck_btn);
			this.panel1.Controls.Add(this.prism_sn_label);
			this.panel1.Controls.Add(this.len_sn_label);
			this.panel1.Controls.Add(this.len_sn_tb);
			this.panel1.Controls.Add(this.label1);
			this.panel1.Controls.Add(this.sequence_label);
			this.panel1.Controls.Add(this.ReOpen_btn);
			this.panel1.Controls.Add(this.status_label);
			this.panel1.Controls.Add(this.sn_tb);
			this.panel1.Controls.Add(this.sn_label);
			this.panel1.Font = new global::System.Drawing.Font("宋体", 12f, global::System.Drawing.FontStyle.Regular, global::System.Drawing.GraphicsUnit.Point, 134);
			this.panel1.Location = new global::System.Drawing.Point(37, 3);
			this.panel1.Name = "panel1";
			this.panel1.Size = new global::System.Drawing.Size(1351, 86);
			this.panel1.TabIndex = 0;
			this.prism_sn_label.AutoSize = true;
			this.prism_sn_label.Font = new global::System.Drawing.Font("宋体", 13.8f, global::System.Drawing.FontStyle.Regular, global::System.Drawing.GraphicsUnit.Point, 134);
			this.prism_sn_label.Location = new global::System.Drawing.Point(423, 9);
			this.prism_sn_label.Name = "prism_sn_label";
			this.prism_sn_label.Size = new global::System.Drawing.Size(82, 23);
			this.prism_sn_label.TabIndex = 8;
			this.prism_sn_label.Text = "label1";
			this.len_sn_label.AutoSize = true;
			this.len_sn_label.Font = new global::System.Drawing.Font("宋体", 13.8f, global::System.Drawing.FontStyle.Regular, global::System.Drawing.GraphicsUnit.Point, 134);
			this.len_sn_label.Location = new global::System.Drawing.Point(423, 50);
			this.len_sn_label.Name = "len_sn_label";
			this.len_sn_label.Size = new global::System.Drawing.Size(82, 23);
			this.len_sn_label.TabIndex = 7;
			this.len_sn_label.Text = "label1";
			this.len_sn_tb.Font = new global::System.Drawing.Font("宋体", 12f, global::System.Drawing.FontStyle.Regular, global::System.Drawing.GraphicsUnit.Point, 134);
			this.len_sn_tb.Location = new global::System.Drawing.Point(201, 46);
			this.len_sn_tb.Name = "len_sn_tb";
			this.len_sn_tb.Size = new global::System.Drawing.Size(216, 30);
			this.len_sn_tb.TabIndex = 6;
			this.len_sn_tb.KeyUp += new global::System.Windows.Forms.KeyEventHandler(this.len_sn_tb_KeyUp);
			this.label1.AutoSize = true;
			this.label1.Font = new global::System.Drawing.Font("宋体", 12f, global::System.Drawing.FontStyle.Regular, global::System.Drawing.GraphicsUnit.Point, 134);
			this.label1.Location = new global::System.Drawing.Point(82, 53);
			this.label1.Name = "label1";
			this.label1.Size = new global::System.Drawing.Size(89, 20);
			this.label1.TabIndex = 5;
			this.label1.Text = "透镜扫码";
			this.sequence_label.AutoSize = true;
			this.sequence_label.Location = new global::System.Drawing.Point(720, 15);
			this.sequence_label.Name = "sequence_label";
			this.sequence_label.Size = new global::System.Drawing.Size(69, 20);
			this.sequence_label.TabIndex = 1;
			this.sequence_label.Text = "label1";
			this.ReOpen_btn.Location = new global::System.Drawing.Point(1169, 12);
			this.ReOpen_btn.Name = "ReOpen_btn";
			this.ReOpen_btn.Size = new global::System.Drawing.Size(121, 53);
			this.ReOpen_btn.TabIndex = 4;
			this.ReOpen_btn.Text = "重新开始";
			this.ReOpen_btn.UseVisualStyleBackColor = true;
			this.ReOpen_btn.Click += new global::System.EventHandler(this.ReOpen_btn_Click);
			this.status_label.AutoSize = true;
			this.status_label.Font = new global::System.Drawing.Font("宋体", 13.8f, global::System.Drawing.FontStyle.Regular, global::System.Drawing.GraphicsUnit.Point, 134);
			this.status_label.Location = new global::System.Drawing.Point(720, 53);
			this.status_label.Name = "status_label";
			this.status_label.Size = new global::System.Drawing.Size(82, 23);
			this.status_label.TabIndex = 3;
			this.status_label.Text = "label1";
			this.sn_tb.Font = new global::System.Drawing.Font("宋体", 12f, global::System.Drawing.FontStyle.Regular, global::System.Drawing.GraphicsUnit.Point, 134);
			this.sn_tb.Location = new global::System.Drawing.Point(201, 5);
			this.sn_tb.Name = "sn_tb";
			this.sn_tb.Size = new global::System.Drawing.Size(216, 30);
			this.sn_tb.TabIndex = 2;
			this.sn_tb.KeyUp += new global::System.Windows.Forms.KeyEventHandler(this.sn_tb_KeyUp);
			this.sn_label.AutoSize = true;
			this.sn_label.Font = new global::System.Drawing.Font("宋体", 12f, global::System.Drawing.FontStyle.Regular, global::System.Drawing.GraphicsUnit.Point, 134);
			this.sn_label.Location = new global::System.Drawing.Point(72, 9);
			this.sn_label.Name = "sn_label";
			this.sn_label.Size = new global::System.Drawing.Size(109, 20);
			this.sn_label.TabIndex = 1;
			this.sn_label.Text = "补偿镜扫码";
			this.precheck_btn.Location = new global::System.Drawing.Point(3, 22);
			this.precheck_btn.Name = "precheck_btn";
			this.precheck_btn.Size = new global::System.Drawing.Size(63, 33);
			this.precheck_btn.TabIndex = 9;
			this.precheck_btn.Text = "点检";
			this.precheck_btn.UseVisualStyleBackColor = true;
			this.precheck_btn.Click += new global::System.EventHandler(this.precheck_btn_Click);
			base.AutoScaleDimensions = new global::System.Drawing.SizeF(8f, 15f);
			base.AutoScaleMode = global::System.Windows.Forms.AutoScaleMode.Font;
			base.ClientSize = new global::System.Drawing.Size(1400, 722);
			base.Controls.Add(this.panel1);
			base.Controls.Add(this.mainPanel);
			base.Name = "Entrance";
			this.Text = "补偿镜装调";
			this.panel1.ResumeLayout(false);
			this.panel1.PerformLayout();
			base.ResumeLayout(false);
		}

		// Token: 0x04000190 RID: 400
		private global::System.ComponentModel.IContainer components = null;

		// Token: 0x04000191 RID: 401
		private global::System.Windows.Forms.Panel mainPanel;

		// Token: 0x04000192 RID: 402
		private global::System.Windows.Forms.Panel panel1;

		// Token: 0x04000193 RID: 403
		private global::System.Windows.Forms.Label sn_label;

		// Token: 0x04000194 RID: 404
		private global::System.Windows.Forms.TextBox sn_tb;

		// Token: 0x04000195 RID: 405
		private global::System.Windows.Forms.Label status_label;

		// Token: 0x04000196 RID: 406
		private global::System.Windows.Forms.Button ReOpen_btn;

		// Token: 0x04000197 RID: 407
		public global::System.Windows.Forms.Label sequence_label;

		// Token: 0x04000198 RID: 408
		private global::System.Windows.Forms.Label label1;

		// Token: 0x04000199 RID: 409
		private global::System.Windows.Forms.Label len_sn_label;

		// Token: 0x0400019A RID: 410
		private global::System.Windows.Forms.TextBox len_sn_tb;

		// Token: 0x0400019B RID: 411
		private global::System.Windows.Forms.Label prism_sn_label;

		// Token: 0x0400019C RID: 412
		private global::System.Windows.Forms.Button precheck_btn;
	}
}
