﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;

namespace Nreal_ProductLine_Tool.SUPPORT
{
	// Token: 0x02000019 RID: 25
	public partial class FormDefault : Form
	{
		// Token: 0x060000DE RID: 222 RVA: 0x0001323A File Offset: 0x0001143A
		public FormDefault()
		{
			this.InitializeComponent();
		}

		// Token: 0x060000DF RID: 223 RVA: 0x00013252 File Offset: 0x00011452
		public virtual void SetType(int type)
		{
		}

		// Token: 0x060000E0 RID: 224 RVA: 0x00013255 File Offset: 0x00011455
		public virtual void Start()
		{
		}

		// Token: 0x060000E1 RID: 225 RVA: 0x00013258 File Offset: 0x00011458
		public virtual void ExcepStop()
		{
		}

		// Token: 0x060000E2 RID: 226 RVA: 0x0001325B File Offset: 0x0001145B
		public virtual void SetEnable(string prism_sn, string len_sn)
		{
		}

		// Token: 0x060000E3 RID: 227 RVA: 0x00013260 File Offset: 0x00011460
		public virtual string GetName()
		{
			return "";
		}

		// Token: 0x060000E4 RID: 228 RVA: 0x00013277 File Offset: 0x00011477
		public virtual void ShowImage(Bitmap pic)
		{
		}

		// Token: 0x060000E5 RID: 229 RVA: 0x0001327A File Offset: 0x0001147A
		public virtual void Stop()
		{
		}
	}
}
