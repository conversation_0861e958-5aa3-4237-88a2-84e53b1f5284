﻿using System;
using System.IO;
using log4net;
using log4net.Appender;
using log4net.Config;
using log4net.Repository;

namespace Nreal_ProductLine_Tool.SUPPORT
{
	// Token: 0x0200001D RID: 29
	public class Logs
	{
		// Token: 0x06000130 RID: 304 RVA: 0x00017AE0 File Offset: 0x00015CE0
		public Logs()
		{
			XmlConfigurator.Configure(new FileInfo("log4net.config"));
			bool flag = !Directory.Exists(Logs.filePath);
			if (flag)
			{
				Directory.CreateDirectory(Logs.filePath);
			}
		}

		// Token: 0x06000131 RID: 305 RVA: 0x00017B24 File Offset: 0x00015D24
		public static void WriteLog(string msg, bool isWrite, Action<object> action)
		{
			if (isWrite)
			{
				string text = DateTime.Now.ToString("yyyy年MM月dd日") + ".log";
				ILoggerRepository repository = LogManager.GetRepository();
				IAppender[] appenders = repository.GetAppenders();
				bool flag = appenders.Length != 0;
				if (flag)
				{
					RollingFileAppender rollingFileAppender = null;
					foreach (IAppender appender in appenders)
					{
						bool flag2 = appender.Name == "mylog4";
						if (flag2)
						{
							rollingFileAppender = (appender as RollingFileAppender);
							break;
						}
					}
					bool flag3 = rollingFileAppender.Name == "mylog4";
					if (flag3)
					{
						bool flag4 = rollingFileAppender != null;
						if (flag4)
						{
							bool flag5 = !rollingFileAppender.File.Contains(text);
							if (flag5)
							{
								rollingFileAppender.File = "Logs\\" + text;
								rollingFileAppender.ActivateOptions();
							}
						}
					}
				}
				action(msg);
			}
		}

		// Token: 0x06000132 RID: 306 RVA: 0x00017C1E File Offset: 0x00015E1E
		public static void WriteError(string msg, bool isWrite)
		{
			Logs.WriteLog(msg, isWrite, new Action<object>(Logs.log.Error));
		}

		// Token: 0x06000133 RID: 307 RVA: 0x00017C3A File Offset: 0x00015E3A
		public static void WriteInfo(string msg, bool isWrite)
		{
			Logs.WriteLog(msg, isWrite, new Action<object>(Logs.log.Info));
		}

		// Token: 0x06000134 RID: 308 RVA: 0x00017C56 File Offset: 0x00015E56
		public static void WriteWarn(string msg, bool isWrite)
		{
			Logs.WriteLog(msg, isWrite, new Action<object>(Logs.log.Warn));
		}

		// Token: 0x06000135 RID: 309 RVA: 0x00017C72 File Offset: 0x00015E72
		public static void WriteDebug(string msg, bool isWrite)
		{
			Logs.WriteLog(msg, isWrite, new Action<object>(Logs.log.Debug));
		}

		// Token: 0x040002C5 RID: 709
		private static string filePath = AppDomain.CurrentDomain.BaseDirectory + "Logs\\";

		// Token: 0x040002C6 RID: 710
		private static readonly ILog log = LogManager.GetLogger("mylog4");
	}
}
