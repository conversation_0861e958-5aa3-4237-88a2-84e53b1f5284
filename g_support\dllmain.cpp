﻿// dllmain.cpp : 定义 DLL 应用程序的入口点。
#include "pch.h"
#include "MyPylon.h"
#ifdef BASLERCONTROLLER_EXPORTS
#define BASLER_API __declspec(dllexport)
#else
#define BASLER_API __declspec(dllimport)
#endif

BOOL APIENTRY DllMain( HMODULE hModule,
                       DWORD  ul_reason_for_call,
                       LPVOID lpReserved
                     )
{
    switch (ul_reason_for_call)
    {
    case DLL_PROCESS_ATTACH:
    case DLL_THREAD_ATTACH:
    case DLL_THREAD_DETACH:
    case DLL_PROCESS_DETACH:
        break;
    }
    return TRUE;
}

static MyPylon* myPylon = nullptr;
extern "C" { 
    BASLER_API int Init_pb(char* basePath, int max_width, int min_width, int max_number) {
        int status = 0;
        if (nullptr == myPylon) {
            myPylon = new MyPylon();
            status = myPylon->InitPB(basePath, max_width, min_width, max_number);
            if (status != 0) {
                delete myPylon;
                myPylon = nullptr;
            }
        }
        return status;
    }

    BASLER_API int Init_pbs(char* basePath, int *para) {
        int status = 0;
        if (nullptr == myPylon) {
            myPylon = new MyPylon();
            status = myPylon->InitPBS(basePath, para);
            if (status != 0) {
                //delete myPylon;
                //myPylon = nullptr;
            }
        }
        return status;
    }

    BASLER_API int Init_spb(char* basePath, int roi_upper_y, int roi_left_x, int roi_left_y, int roi_left_width, int roi_left_height, int roi_right_x, int roi_right_y, int roi_right_width, int roi_right_height)
    {
        int status = 0;
        if (nullptr == myPylon) {
            myPylon = new MyPylon();
            status = myPylon->InitSPB(basePath, roi_upper_y, roi_left_x, roi_left_y, roi_left_width, roi_left_height, roi_right_x, roi_right_y, roi_right_width, roi_right_height);
            if (status != 0) {
                //delete myPylon;
                //myPylon = nullptr;
            }
        }
        else
        {
            status = myPylon->InitSPB(basePath, roi_upper_y, roi_left_x, roi_left_y, roi_left_width, roi_left_height, roi_right_x, roi_right_y, roi_right_width, roi_right_height);

        }
        return status;
    }    

    BASLER_API int Init_pm() {
        int status = 0;
        if (nullptr == myPylon) {
            myPylon = new MyPylon();
            status = myPylon->InitPM();
            if (status != 0) {
                delete myPylon;
                myPylon = nullptr;
            }
        }
        return status;
    }

    BASLER_API int Init_support(int left_thresh, int right_thresh, int roi_x, int roi_y, int roi_width, int left_x, int right_x) {
        int status = 0;
        if (nullptr == myPylon) {
            myPylon = new MyPylon();
            status = myPylon->InitSupport(left_thresh, right_thresh, roi_x, roi_y, roi_width, left_x, right_x);
            if (status != 0) {
                //delete myPylon;
                //myPylon = nullptr;
            }
        }
        else
        {
            status = myPylon->InitSupport(left_thresh, right_thresh, roi_x, roi_y, roi_width, left_x, right_x);
        }
        return status;
    }

    BASLER_API int SetPicPath(char* path) {
        if (nullptr == myPylon)
            return -1;
        
        myPylon->SetImgPath(path);

        return 0;
    }

    BASLER_API int Init_support_preCheck(int *para, int len) {
        int status = 0;
        if (nullptr == myPylon) {
            return status;
            
        }
        status = myPylon->InitSupportPreCheck(para, len);
        if (status != 0) {
            //delete myPylon;
            //myPylon = nullptr;
        }
        return status;
    }

    BASLER_API int Init_prism1(int* para) {
        int status = 0;
        if (nullptr == myPylon) {
            myPylon = new MyPylon();
            status = myPylon->InitPrism(para);
            if (status != 0) {
                //delete myPylon;
                //myPylon = nullptr;
            }
        }
        else
        {
            status = myPylon->InitPrism(para);

        }
        return status;
    }

    BASLER_API int Init_prism2(int* para) {
        int status = 0;
        if (nullptr == myPylon) {
            myPylon = new MyPylon();
            status = myPylon->InitPrism2(para);
            if (status != 0) {
                //delete myPylon;
                //myPylon = nullptr;
            }
        }
        else
        {
            status = myPylon->InitPrism2(para);

        }
        return status;
    }

    BASLER_API int Uninit() {
        int status = 0;
        if (nullptr != myPylon) {
            status = myPylon->Uninit();
            delete myPylon;
            myPylon = nullptr;
        }
        return status;
    }

    BASLER_API void GetExposureTime(double& value) {
        value = 0;
        if (nullptr == myPylon)
            return;
        myPylon->GetExposureTime(value);
    }

    BASLER_API void SetExposureTime(double value) {
        if (nullptr == myPylon)
            return;
        myPylon->SetExposureTime(value);
    }

    BASLER_API void GetGain(double& value) {
        value = 0;
        if (nullptr == myPylon)
            return;
        myPylon->GetGain(value);
    }

    BASLER_API void SetGain(double value) {
        if (nullptr == myPylon)
            return;
        myPylon->SetGain(value);
    }

    BASLER_API void SetParamD(char* key, double value) {
        if (nullptr == myPylon)
            return;
        myPylon->SetParamD(key,value);
    }

    BASLER_API void SetParamS(char* key, char* value) {
        if (nullptr == myPylon)
            return;
        myPylon->SetParamS(key, value);
    }

    //BASLER_API int CaptureAndAnalysis(int type, double* result) {
    //    if (nullptr == myPylon)
    //        myPylon = new MyPylon(); 
    //    return myPylon->CaptureAndAnalysis(type, result);
    //}

    BASLER_API int Capture() {
        if (nullptr == myPylon)
            return -1;
        return myPylon->Capture();
    }

    BASLER_API int Analysis(int type, double* result) {
        if (nullptr == myPylon)
            myPylon = new MyPylon();     
        return myPylon->Analysis(type, result);
    }

    BASLER_API int AnalysisPBS(int type, double* result) {
        if (nullptr == myPylon)
            myPylon = new MyPylon();
        return myPylon->AnalysisPBS(type, result);
    }

    BASLER_API int AnalysisSPB(int type, double* result) {
        if (nullptr == myPylon)
            myPylon = new MyPylon();
        return myPylon->AnalysisSPB(type, result);
    }

    BASLER_API int AnalysisPB(int type, double* result) {
        if (nullptr == myPylon)
            myPylon = new MyPylon();
        return myPylon->AnalysisPB(type, result);
    }

    BASLER_API int AnalysisSupport(int type, double* result) {
        if (nullptr == myPylon)
            myPylon = new MyPylon();
        return myPylon->AnalysisSupport(type, result);
    }

    BASLER_API int AnalysisSupportPreCheck(int type, double* result) {
        if (nullptr == myPylon)
            myPylon = new MyPylon();
        return myPylon->AnalysisSupportPreCheck(type, result);
    }

    BASLER_API int AnalysisPrism1(int type, int* result) {
        if (nullptr == myPylon)
            myPylon = new MyPylon();
        return myPylon->AnalysisPrism1(type, result);
    }

    BASLER_API int AnalysisPrism2(int type, int* pos, double *res) {
        if (nullptr == myPylon)
            myPylon = new MyPylon();
        return myPylon->AnalysisPrism2(type, pos, res);
    }

    BASLER_API int PixelDistantCali(int type, double* result) {
        if (nullptr == myPylon)
            myPylon = new MyPylon();
        return myPylon->PixelDistantCali(type, result);
    }

    BASLER_API void Get81Sharpness(double* result) {
        if (nullptr == myPylon)
            myPylon = new MyPylon();
        myPylon->Get81Sharpness(result);
    }

    BASLER_API void Get41SharpnessH(double* result) {
        if (nullptr == myPylon)
            myPylon = new MyPylon();
        myPylon->Get41SharpnessH(result);
    }

    BASLER_API void Get41SharpnessV(double* result) {
        if (nullptr == myPylon)
            myPylon = new MyPylon();
        myPylon->Get41SharpnessV(result);
    }

    BASLER_API int RegisterCallBackShowImage(CALL_BACK_SHOW_IMAGE func) {
        if (nullptr == myPylon)
            return -1;
        return myPylon->RegisterCallBackShowImage(func);
    }
    BASLER_API int ShowImage() {
        myPylon->show();
        return 0;
    }

    BASLER_API int RegisterCallBackShowChart(CALL_BACK_FUNC_CHART func) {
        if (nullptr == myPylon)
            return -1;
        return myPylon->RegisterCallBackShowChart(func);
    }

    BASLER_API int ClearRecord() {
        if (nullptr == myPylon)
            return -1;
        myPylon->ClearRecord();
        return 0;
    }

    BASLER_API int AddRecord(double focus, double value) {
        if (nullptr == myPylon)
            return -1;
        myPylon->AddRecord(focus, value);
        return 0;
    }

    BASLER_API int CaculateFitting(double focusMin, double focusMax, double step, int n, double& focusResult, double& valueResult) {
        if (nullptr == myPylon)
            return -1;
        myPylon->Caculate(focusMin, focusMax, step, n, focusResult, valueResult);
        return 0;
    }

    BASLER_API void SaveImage() {
        if (nullptr == myPylon)
            return;
        myPylon->SaveImage();
    }
}