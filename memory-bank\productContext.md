# Product Context

This file provides a high-level overview of the project and the expected product that will be created. Initially it is based upon projectBrief.md (if provided) and all other available project-related information in the working directory. This file is intended to be updated as the project evolves, and should be used to inform all other modes of the project's goals and context.
2025-07-08 12:25:14 - Log of updates made will be appended as footnotes to the end of this file.

*

## Project Goal

*   

## Key Features

*   

## Overall Architecture

*   **分层架构**:
    *   **表示层 (Application Layer)**: 一个 C# 控制台应用 (`BatchImageAnalysis.App`)，作为用户交互的入口。
    *   **核心逻辑层 (Core Layer)**: 一个 C# 类库 (`BatchImageAnalysis.Core`)，包含主要的业务逻辑和流程编排。
    *   **互操作层 (Interop Layer)**: 一个专门的 C# 类库 (`BatchImageAnalysis.Interop`)，用于封装对原生 `BaslerController.dll` 的 P/Invoke 调用，并处理图像数据格式的转换。
    *   **原生依赖 (Native Dependency)**: `BaslerController.dll` 本身。
*   **数据流**:
    1.  `App` 接收输入/输出路径。
    2.  `App` 调用 `Core` 层的 `ImageProcessingService`。
    3.  `ImageProcessingService` 遍历输入目录中的所有图像。
    4.  对于每个图像，`Core` 调用 `Interop` 层的 `ImageAdapter` 来从文件加载图像并将其转换为 DLL 可接受的内存格式（如 `IntPtr` 或 `byte[]`）。
    5.  `Core` 调用 `Interop` 层的 `BaslerPInvoke` 封装器，将图像数据指针传递给 `BaslerController.dll` 中的分析函数。
    6.  `DLL` 返回分析结果（可能是结构体指针）。
    7.  `Interop` 层将结果从非托管内存封送到 C# 结构体中。
    8.  `Core` 层处理分析结果，并调用 `AnnotatedImageGenerator` 生成带注释的图像，保存到输出目录。