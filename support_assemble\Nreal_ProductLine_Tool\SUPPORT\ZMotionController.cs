﻿using System;

namespace Nreal_ProductLine_Tool.SUPPORT
{
	// Token: 0x02000024 RID: 36
	public class ZMotionController
	{
		// Token: 0x060002B6 RID: 694 RVA: 0x000181F0 File Offset: 0x000163F0
		public static int Init()
		{
			Logs.WriteInfo("zmotion init", true);
			int num = zmcaux.ZAux_OpenEth("192.168.0.11", out ZMotionController.handle);
			bool flag = num == 0;
			if (flag)
			{
				ZMotionController.inited = true;
			}
			else
			{
				ZMotionController.inited = false;
			}
			return num;
		}

		// Token: 0x060002B7 RID: 695 RVA: 0x00018238 File Offset: 0x00016438
		public static void Uninit()
		{
			bool flag = ZMotionController.inited;
			if (flag)
			{
				int num = zmcaux.ZAux_Close(ZMotionController.handle);
			}
		}

		// Token: 0x060002B8 RID: 696 RVA: 0x0001825C File Offset: 0x0001645C
		public static void uvIdel()
		{
			int num = 0;
			int num2 = 0;
			zmcaux.ZAux_Direct_SetOp(ZMotionController.handle, num, 1U);
			zmcaux.ZAux_Direct_SetOp(ZMotionController.handle, num2, 0U);
			Logs.WriteInfo(string.Format("UV空闲位置：{0}:1,{1}:0", num, num2), true);
		}

		// Token: 0x060002B9 RID: 697 RVA: 0x000182A8 File Offset: 0x000164A8
		public static void uvWork()
		{
			int num = 0;
			int num2 = 0;
			zmcaux.ZAux_Direct_SetOp(ZMotionController.handle, num, 0U);
			zmcaux.ZAux_Direct_SetOp(ZMotionController.handle, num2, 1U);
			Logs.WriteInfo(string.Format("UV工作位置：{0}:0,{1}:1", num, num2), true);
		}

		// Token: 0x060002BA RID: 698 RVA: 0x000182F4 File Offset: 0x000164F4
		public static int VacuummeterDoorSignalRd(ref uint sig)
		{
			sig = 1U;
			return zmcaux.ZAux_Direct_GetIn(ZMotionController.handle, Configure.vacuummeter_door_close_in, ref sig);
		}

		// Token: 0x060002BB RID: 699 RVA: 0x0001831C File Offset: 0x0001651C
		public static int GasReleaseOpen()
		{
			return zmcaux.ZAux_Direct_SetOp(ZMotionController.handle, Configure.gas_release_out, 1U);
		}

		// Token: 0x060002BC RID: 700 RVA: 0x00018340 File Offset: 0x00016540
		public static int GasReleaseClose()
		{
			return zmcaux.ZAux_Direct_SetOp(ZMotionController.handle, Configure.gas_release_out, 0U);
		}

		// Token: 0x060002BD RID: 701 RVA: 0x00018364 File Offset: 0x00016564
		public static int VacuummeterPumpOpen()
		{
			return zmcaux.ZAux_Direct_SetOp(ZMotionController.handle, Configure.vacuummeter_pump_out, 1U);
		}

		// Token: 0x060002BE RID: 702 RVA: 0x00018388 File Offset: 0x00016588
		public static int VacuummeterPumpClose()
		{
			return zmcaux.ZAux_Direct_SetOp(ZMotionController.handle, Configure.vacuummeter_pump_out, 0U);
		}

		// Token: 0x060002BF RID: 703 RVA: 0x000183AC File Offset: 0x000165AC
		public static int VacuummeterGasOutOpen()
		{
			return zmcaux.ZAux_Direct_SetOp(ZMotionController.handle, Configure.gas_out, 1U);
		}

		// Token: 0x060002C0 RID: 704 RVA: 0x000183D0 File Offset: 0x000165D0
		public static int VacuummeterGasOutClose()
		{
			return zmcaux.ZAux_Direct_SetOp(ZMotionController.handle, Configure.gas_out, 0U);
		}

		// Token: 0x060002C1 RID: 705 RVA: 0x000183F4 File Offset: 0x000165F4
		public static int StartUV()
		{
			return zmcaux.ZAux_Direct_SetOp(ZMotionController.handle, Configure.uv_out, 1U);
		}

		// Token: 0x060002C2 RID: 706 RVA: 0x00018418 File Offset: 0x00016618
		public static int StopUV()
		{
			return zmcaux.ZAux_Direct_SetOp(ZMotionController.handle, Configure.uv_out, 0U);
		}

		// Token: 0x040002D9 RID: 729
		public static IntPtr handle;

		// Token: 0x040002DA RID: 730
		public static bool inited;
	}
}
