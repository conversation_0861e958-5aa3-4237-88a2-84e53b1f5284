using SixLabors.ImageSharp;
using SixLabors.ImageSharp.PixelFormats;
using System;
using System.IO;
using System.Runtime.InteropServices;

namespace BatchImageAnalysis.Interop
{
    public class AnalysisResult
    {
        public bool Success { get; set; }
        public string ErrorMessage { get; set; }
    }
    public interface IImageAdapterService
    {
        AnalysisResult AnalyzeImage(string imagePath);
    }

    public class ImageAdapterService : IImageAdapterService, IDisposable
    {
        private bool _isInitialized = false;

        public ImageAdapterService()
        {
            int result = NativeMethods.Init_support();
            if (result != 0)
            {
                throw new InvalidOperationException($"Failed to initialize BaslerController.dll. Error code: {result}");
            }
            _isInitialized = true;
        }

        public AnalysisResult AnalyzeImage(string imagePath)
        {
            if (!_isInitialized)
            {
                throw new InvalidOperationException("Service is not initialized.");
            }

            if (!File.Exists(imagePath))
            {
                return new AnalysisResult { Success = false, ErrorMessage = "Image file not found." };
            }

            try
            {
                using (Image<Bgr24> image = Image.Load<Bgr24>(imagePath))
                {
                    byte[] pixelData = new byte[image.Width * image.Height * image.PixelType.BitsPerPixel / 8];
                    image.CopyPixelDataTo(pixelData);

                    GCHandle handle = GCHandle.Alloc(pixelData, GCHandleType.Pinned);
                    try
                    {
                        IntPtr imageDataPtr = handle.AddrOfPinnedObject();
                        int resultCode = NativeMethods.AnalysisSupport(imageDataPtr, image.Width, image.Height);
                        return new AnalysisResult { Success = resultCode == 0, ErrorMessage = resultCode != 0 ? $"Analysis failed with code {resultCode}" : null };
                    }
                    finally
                    {
                        if (handle.IsAllocated)
                        {
                            handle.Free();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                return new AnalysisResult { Success = false, ErrorMessage = ex.Message };
            }
        }

        public void Dispose()
        {
            // Placeholder for Deinit_support if it exists
            _isInitialized = false;
        }
    }
}