using SixLabors.ImageSharp;
using SixLabors.ImageSharp.PixelFormats;
using System;
using System.IO;
using System.Runtime.InteropServices;

namespace BatchImageAnalysis.Interop
{
    public class AnalysisResult
    {
        public bool Success { get; set; }
        public string ErrorMessage { get; set; }
    }
    public interface IImageAdapterService
    {
        AnalysisResult AnalyzeImage(string imagePath);
        void Initialize(int leftThresh, int rightThresh, int roiX, int roiY, int roiWidth, int leftX, int rightX);
    }

    public class ImageAdapterService : IImageAdapterService, IDisposable
    {
        private bool _isInitialized = false;
        private bool _simulationMode = false;

        public ImageAdapterService()
        {
            // 构造函数不再自动初始化，需要显式调用 Initialize
        }

        public void Initialize(int leftThresh, int rightThresh, int roiX, int roiY, int roiWidth, int leftX, int rightX)
        {
            if (_isInitialized)
            {
                return; // 已经初始化过了
            }

            int result = NativeMethods.Init_support(leftThresh, rightThresh, roiX, roiY, roiWidth, leftX, rightX);
            if (result != 0)
            {
                Console.WriteLine($"Warning: Failed to initialize BaslerController.dll (Error code: {result}). Running in simulation mode.");
                Console.WriteLine("This is normal if no Basler camera hardware is connected.");
                _simulationMode = true;
            }
            _isInitialized = true;
        }

        public AnalysisResult AnalyzeImage(string imagePath)
        {
            if (!_isInitialized)
            {
                throw new InvalidOperationException("Service is not initialized.");
            }

            if (!File.Exists(imagePath))
            {
                return new AnalysisResult { Success = false, ErrorMessage = "Image file not found." };
            }

            // 如果在模拟模式下，返回模拟结果
            if (_simulationMode)
            {
                Console.WriteLine($"Simulating analysis for: {Path.GetFileName(imagePath)}");
                // 模拟一些处理时间
                System.Threading.Thread.Sleep(100);
                return new AnalysisResult { Success = true, ErrorMessage = null };
            }

            try
            {
                using (Image<Bgr24> image = Image.Load<Bgr24>(imagePath))
                {
                    byte[] pixelData = new byte[image.Width * image.Height * image.PixelType.BitsPerPixel / 8];
                    image.CopyPixelDataTo(pixelData);

                    GCHandle handle = GCHandle.Alloc(pixelData, GCHandleType.Pinned);
                    try
                    {
                        IntPtr imageDataPtr = handle.AddrOfPinnedObject();
                        int resultCode = NativeMethods.AnalysisSupport(imageDataPtr, image.Width, image.Height);
                        return new AnalysisResult { Success = resultCode == 0, ErrorMessage = resultCode != 0 ? $"Analysis failed with code {resultCode}" : null };
                    }
                    finally
                    {
                        if (handle.IsAllocated)
                        {
                            handle.Free();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                return new AnalysisResult { Success = false, ErrorMessage = ex.Message };
            }
        }

        public void Dispose()
        {
            // Placeholder for Deinit_support if it exists
            _isInitialized = false;
        }
    }
}