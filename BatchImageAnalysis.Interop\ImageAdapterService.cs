using SixLabors.ImageSharp;
using SixLabors.ImageSharp.PixelFormats;
using System;
using System.IO;
using System.Runtime.InteropServices;

namespace BatchImageAnalysis.Interop
{
    public class AnalysisResult
    {
        public bool Success { get; set; }
        public string ErrorMessage { get; set; }
        public byte[] ProcessedImageData { get; set; } // 处理后的图像数据
        public int ImageWidth { get; set; }
        public int ImageHeight { get; set; }
        public string OriginalImagePath { get; set; }
    }
    public interface IImageAdapterService
    {
        AnalysisResult AnalyzeImage(string imagePath);
        void Initialize(int leftThresh, int rightThresh, int roiX, int roiY, int roiWidth, int leftX, int rightX);
    }

    public class ImageAdapterService : IImageAdapterService, IDisposable
    {
        private bool _isInitialized = false;
        private bool _simulationMode = false;
        private byte[] _lastProcessedImageData = null;
        private NativeMethods.ImageTransferCallback _imageCallback;

        public ImageAdapterService()
        {
            // 构造函数不再自动初始化，需要显式调用 Initialize
            _imageCallback = new NativeMethods.ImageTransferCallback(OnImageReceived);
        }

        public void Initialize(int leftThresh, int rightThresh, int roiX, int roiY, int roiWidth, int leftX, int rightX)
        {
            if (_isInitialized)
            {
                return; // 已经初始化过了
            }

            int result = NativeMethods.Init_support(leftThresh, rightThresh, roiX, roiY, roiWidth, leftX, rightX);
            if (result != 0)
            {
                Console.WriteLine($"Warning: Failed to initialize BaslerController.dll (Error code: {result}). Running in simulation mode.");
                Console.WriteLine("This is normal if no Basler camera hardware is connected.");
                _simulationMode = true;
            }
            else
            {
                // 注册图像回调函数
                int callbackResult = NativeMethods.RegisterCallBackShowImage(_imageCallback);
                if (callbackResult != 0)
                {
                    Console.WriteLine($"Warning: Failed to register image callback (Error code: {callbackResult}).");
                }
            }
            _isInitialized = true;
        }

        public AnalysisResult AnalyzeImage(string imagePath)
        {
            if (!_isInitialized)
            {
                throw new InvalidOperationException("Service is not initialized.");
            }

            if (!File.Exists(imagePath))
            {
                return new AnalysisResult { Success = false, ErrorMessage = "Image file not found." };
            }

            // 即使在模拟模式下，也尝试调用 DLL 的 AnalysisSupport 函数
            // 因为 DLL 可能有内置的图像处理功能，不依赖于硬件初始化

            try
            {
                // 清除之前的图像数据
                _lastProcessedImageData = null;

                // 方法：将图像复制到 DLL 期望的固定位置
                // 根据 C++ 代码，DLL 可能期望特定名称的文件
                string dllImagePath = Path.Combine(Path.GetDirectoryName(imagePath), "temp_analysis_image.bmp");

                try
                {
                    // 将原图像复制到 DLL 期望的位置
                    File.Copy(imagePath, dllImagePath, overwrite: true);
                    Console.WriteLine($"Copied image to DLL working path: {dllImagePath}");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Warning: Failed to copy image to DLL path: {ex.Message}");
                    // 继续尝试使用原路径
                    dllImagePath = imagePath;
                }

                // 如果 DLL 支持 SetPicPath，使用它
                if (!_simulationMode)
                {
                    int setPathResult = NativeMethods.SetPicPath(dllImagePath);
                    Console.WriteLine($"SetPicPath result: {setPathResult}");
                }

                // 调用分析函数，type=1 表示进行分析并生成辅助线
                double[] analysisResults = new double[11]; // 根据原代码，结果数组大小为11
                int resultCode = NativeMethods.AnalysisSupport(1, analysisResults);

                if (resultCode == 0)
                {
                    // 等待一小段时间让回调函数处理图像数据
                    System.Threading.Thread.Sleep(500);

                    if (_lastProcessedImageData != null)
                    {
                        // 成功获取到处理后的图像数据
                        return new AnalysisResult
                        {
                            Success = true,
                            ErrorMessage = null,
                            ProcessedImageData = _lastProcessedImageData,
                            ImageWidth = 0, // 这些值会在回调中设置
                            ImageHeight = 0,
                            OriginalImagePath = imagePath
                        };
                    }
                    else
                    {
                        return new AnalysisResult
                        {
                            Success = false,
                            ErrorMessage = "Analysis completed but no processed image data received"
                        };
                    }
                }
                else
                {
                    return new AnalysisResult
                    {
                        Success = false,
                        ErrorMessage = $"Analysis failed with code {resultCode}"
                    };
                }
            }
            catch (Exception ex)
            {
                return new AnalysisResult { Success = false, ErrorMessage = ex.Message };
            }
        }

        public void Dispose()
        {
            // Placeholder for Deinit_support if it exists
            _isInitialized = false;
        }

        private void OnImageReceived(IntPtr data, int count)
        {
            try
            {
                // 将接收到的图像数据复制到托管数组
                _lastProcessedImageData = new byte[count];
                Marshal.Copy(data, _lastProcessedImageData, 0, count);
                Console.WriteLine($"Received processed image data: {count} bytes");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in image callback: {ex.Message}");
                _lastProcessedImageData = null;
            }
        }

        private (byte[] imageData, int width, int height) GenerateSimulatedAnalysisImage(string imagePath)
        {
            try
            {
                using (var originalImage = Image.Load<Bgr24>(imagePath))
                {
                    // 创建一个副本用于绘制辅助线
                    var processedImage = originalImage.Clone();

                    // 模拟绘制一些分析辅助线
                    DrawSimulatedAnalysisLines(processedImage);

                    // 将图像转换为字节数组
                    using (var memoryStream = new MemoryStream())
                    {
                        processedImage.SaveAsPng(memoryStream);
                        return (memoryStream.ToArray(), processedImage.Width, processedImage.Height);
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error generating simulated analysis image: {ex.Message}");
                return (null, 0, 0);
            }
        }

        private void DrawSimulatedAnalysisLines(Image<Bgr24> image)
        {
            var width = image.Width;
            var height = image.Height;

            // 绘制中心十字线
            var centerX = width / 2;
            var centerY = height / 2;

            // 绘制水平中心线 (红色)
            for (int x = 0; x < width; x++)
            {
                if (centerY < height)
                    image[x, centerY] = new Bgr24(0, 0, 255); // 红色
            }

            // 绘制垂直中心线 (红色)
            for (int y = 0; y < height; y++)
            {
                if (centerX < width)
                    image[centerX, y] = new Bgr24(0, 0, 255); // 红色
            }

            // 绘制ROI区域框 (绿色)
            var roiX = width / 4;
            var roiY = height / 4;
            var roiWidth = width / 2;
            var roiHeight = height / 2;

            // 绘制ROI矩形边框
            DrawRectangle(image, roiX, roiY, roiWidth, roiHeight, new Bgr24(0, 255, 0)); // 绿色

            // 绘制一些检测点 (蓝色圆点)
            DrawCircle(image, centerX - 100, centerY - 50, 5, new Bgr24(255, 0, 0)); // 蓝色
            DrawCircle(image, centerX + 100, centerY - 50, 5, new Bgr24(255, 0, 0)); // 蓝色
            DrawCircle(image, centerX - 100, centerY + 50, 5, new Bgr24(255, 0, 0)); // 蓝色
            DrawCircle(image, centerX + 100, centerY + 50, 5, new Bgr24(255, 0, 0)); // 蓝色
        }

        private void DrawRectangle(Image<Bgr24> image, int x, int y, int width, int height, Bgr24 color)
        {
            // 绘制矩形边框
            for (int i = 0; i < width; i++)
            {
                if (x + i < image.Width && y >= 0 && y < image.Height)
                    image[x + i, y] = color; // 上边
                if (x + i < image.Width && y + height - 1 >= 0 && y + height - 1 < image.Height)
                    image[x + i, y + height - 1] = color; // 下边
            }
            for (int i = 0; i < height; i++)
            {
                if (x >= 0 && x < image.Width && y + i >= 0 && y + i < image.Height)
                    image[x, y + i] = color; // 左边
                if (x + width - 1 >= 0 && x + width - 1 < image.Width && y + i >= 0 && y + i < image.Height)
                    image[x + width - 1, y + i] = color; // 右边
            }
        }

        private void DrawCircle(Image<Bgr24> image, int centerX, int centerY, int radius, Bgr24 color)
        {
            for (int x = centerX - radius; x <= centerX + radius; x++)
            {
                for (int y = centerY - radius; y <= centerY + radius; y++)
                {
                    if (x >= 0 && x < image.Width && y >= 0 && y < image.Height)
                    {
                        int dx = x - centerX;
                        int dy = y - centerY;
                        if (dx * dx + dy * dy <= radius * radius)
                        {
                            image[x, y] = color;
                        }
                    }
                }
            }
        }
    }
}