{"version": 3, "targets": {"net6.0": {"ini-parser-netstandard/2.5.3": {"type": "package", "compile": {"lib/netstandard2.0/INIFileParser.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/INIFileParser.dll": {"related": ".xml"}}}, "SixLabors.ImageSharp/3.1.10": {"type": "package", "compile": {"lib/net6.0/SixLabors.ImageSharp.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/SixLabors.ImageSharp.dll": {"related": ".xml"}}, "build": {"build/_._": {}}}, "BatchImageAnalysis.Interop/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v6.0", "dependencies": {"SixLabors.ImageSharp": "3.1.10"}, "compile": {"bin/placeholder/BatchImageAnalysis.Interop.dll": {}}, "runtime": {"bin/placeholder/BatchImageAnalysis.Interop.dll": {}}}}}, "libraries": {"ini-parser-netstandard/2.5.3": {"sha512": "Ht3UqRCqizBPbB5pvQ/yCNZIChY6Cy5nLn7TKIlJzWnf4pbqGKqDO+AY6MDTD+fXgEzzMb64Qzl4/Hy7vEqfLA==", "type": "package", "path": "ini-parser-netstandard/2.5.3", "files": [".nupkg.metadata", ".signature.p7s", "ini-parser-netstandard.2.5.3.nupkg.sha512", "ini-parser-netstandard.nuspec", "lib/net20/INIFileParser.dll", "lib/net20/INIFileParser.xml", "lib/net40/INIFileParser.dll", "lib/net40/INIFileParser.xml", "lib/netstandard2.0/INIFileParser.dll", "lib/netstandard2.0/INIFileParser.xml"]}, "SixLabors.ImageSharp/3.1.10": {"sha512": "R1HEPcqx3v+kvlOTPouP0g/Nzzud9pHtjlgGbFax3Ivaz8kkaGfS2EPfyDGpmfoTUQ3nQ5wxdhYyYa9fwYA9cw==", "type": "package", "path": "sixlabors.imagesharp/3.1.10", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "build/SixLabors.ImageSharp.props", "lib/net6.0/SixLabors.ImageSharp.dll", "lib/net6.0/SixLabors.ImageSharp.xml", "sixlabors.imagesharp.128.png", "sixlabors.imagesharp.3.1.10.nupkg.sha512", "sixlabors.imagesharp.nuspec"]}, "BatchImageAnalysis.Interop/1.0.0": {"type": "project", "path": "../BatchImageAnalysis.Interop/BatchImageAnalysis.Interop.csproj", "msbuildProject": "../BatchImageAnalysis.Interop/BatchImageAnalysis.Interop.csproj"}}, "projectFileDependencyGroups": {"net6.0": ["BatchImageAnalysis.Interop >= 1.0.0", "ini-parser-netstandard >= 2.5.3"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\csharp\\support_analyze\\BatchImageAnalysis.Core\\BatchImageAnalysis.Core.csproj", "projectName": "BatchImageAnalysis.Core", "projectPath": "D:\\csharp\\support_analyze\\BatchImageAnalysis.Core\\BatchImageAnalysis.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\csharp\\support_analyze\\BatchImageAnalysis.Core\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"D:\\csharp\\support_analyze\\BatchImageAnalysis.Interop\\BatchImageAnalysis.Interop.csproj": {"projectPath": "D:\\csharp\\support_analyze\\BatchImageAnalysis.Interop\\BatchImageAnalysis.Interop.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"ini-parser-netstandard": {"target": "Package", "version": "[2.5.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}}}}