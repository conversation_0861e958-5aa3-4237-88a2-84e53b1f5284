﻿using System;
using System.IO.Ports;

namespace Nreal_ProductLine_Tool.SUPPORT
{
	// Token: 0x0200000D RID: 13
	internal class Position
	{
		// Token: 0x06000068 RID: 104 RVA: 0x000093D8 File Offset: 0x000075D8
		public static int Init()
		{
			return 0;
		}

		// Token: 0x06000069 RID: 105 RVA: 0x000093EC File Offset: 0x000075EC
		public static int Uninit()
		{
			return 0;
		}

		// Token: 0x0600006A RID: 106 RVA: 0x00009400 File Offset: 0x00007600
		public static int getPosition(ref double val)
		{
			bool flag = !Position.inited;
			int result;
			if (flag)
			{
				result = -1;
			}
			else
			{
				val = 1.0;
				result = 0;
			}
			return result;
		}

		// Token: 0x0400008B RID: 139
		private static SerialPort serialPort;

		// Token: 0x0400008C RID: 140
		private static bool inited;
	}
}
