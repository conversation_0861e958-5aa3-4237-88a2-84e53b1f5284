﻿using System;
using System.IO.Ports;
using Modbus.Device;

namespace Nreal_ProductLine_Tool.SUPPORT
{
	// Token: 0x0200000E RID: 14
	internal class PressSensor
	{
		// Token: 0x0600006C RID: 108 RVA: 0x00009438 File Offset: 0x00007638
		public static int Init()
		{
			try
			{
				Logs.WriteInfo("PressSensor init: " + Configure.press_sensor_SerialPort, true);
				PressSensor.serialPort = new SerialPort(Configure.press_sensor_SerialPort, 19200, 0, 8, 1);
				PressSensor.serialPort.Open();
				PressSensor.master = ModbusSerialMaster.CreateRtu(PressSensor.serialPort);
				PressSensor.inited = true;
				Logs.WriteInfo("PressSensor init success", true);
			}
			catch (Exception ex)
			{
				Console.WriteLine("发生错误: " + ex.Message);
				return -1;
			}
			return 0;
		}

		// Token: 0x0600006D RID: 109 RVA: 0x000094D4 File Offset: 0x000076D4
		public static int Uninit()
		{
			bool flag = !PressSensor.inited;
			int result;
			if (flag)
			{
				result = 0;
			}
			else
			{
				try
				{
					PressSensor.serialPort.Close();
					PressSensor.inited = false;
				}
				catch (Exception ex)
				{
					Console.WriteLine("发生错误: " + ex.Message);
					return -1;
				}
				result = 0;
			}
			return result;
		}

		// Token: 0x0600006E RID: 110 RVA: 0x0000953C File Offset: 0x0000773C
		public static int getPressValue(ref double val)
		{
			bool flag = !PressSensor.inited;
			int result;
			if (flag)
			{
				result = -1;
			}
			else
			{
				try
				{
					int num = 0;
					ushort[] array = PressSensor.master.ReadHoldingRegisters(PressSensor.slaveAddress, PressSensor.startAddress, PressSensor.numberOfRegisters);
					bool flag2 = array != null;
					if (flag2)
					{
						for (int i = 0; i < array.Length; i++)
						{
							num <<= 16;
							num += (int)array[i];
						}
						val = (double)num;
						val /= 1000.0;
					}
				}
				catch (Exception ex)
				{
					return -1;
				}
				result = 0;
			}
			return result;
		}

		// Token: 0x0400008D RID: 141
		private static byte slaveAddress = 1;

		// Token: 0x0400008E RID: 142
		private static ushort startAddress = 420;

		// Token: 0x0400008F RID: 143
		private static ushort numberOfRegisters = 2;

		// Token: 0x04000090 RID: 144
		private static IModbusSerialMaster master;

		// Token: 0x04000091 RID: 145
		private static SerialPort serialPort = null;

		// Token: 0x04000092 RID: 146
		private static bool inited = false;
	}
}
