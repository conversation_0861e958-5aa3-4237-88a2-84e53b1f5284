﻿using System;
using System.Collections.Generic;
using System.Threading;

namespace Nreal_ProductLine_Tool.SUPPORT
{
	// Token: 0x0200001E RID: 30
	public class MotionController
	{
		// Token: 0x06000137 RID: 311 RVA: 0x00017CB8 File Offset: 0x00015EB8
		public static int Init()
		{
			int num = GSC_Controller.AutoInit();
			bool flag = num == 0;
			int result;
			if (flag)
			{
				MotionController.SetSpeedHigh();
				result = num;
			}
			else
			{
				result = num;
			}
			return result;
		}

		// Token: 0x06000138 RID: 312 RVA: 0x00017CE8 File Offset: 0x00015EE8
		public static void SetSpeedHigh()
		{
			int ySpeedHigh = Configure.ySpeedHigh;
			int zSpeedHigh = Configure.zSpeedHigh;
			string arg = GSC_Controller.SetSpeed(2, zSpeedHigh / 10, zSpeedHigh, 100);
			Logs.WriteInfo(string.Format("SetSpeed:2,{0}, rsult:{1}", zSpeedHigh, arg), true);
		}

		// Token: 0x06000139 RID: 313 RVA: 0x00017D28 File Offset: 0x00015F28
		public static int SetZSpeedHigh()
		{
			int zSpeedHigh = Configure.zSpeedHigh;
			string text = GSC_Controller.SetSpeed(2, zSpeedHigh / 10, zSpeedHigh, 100);
			Logs.WriteInfo(string.Format("SetSpeed:2,{0}, result:{1}", zSpeedHigh, text), true);
			bool flag = text == "" || text.IndexOf("NG") != -1;
			int result;
			if (flag)
			{
				result = 1;
			}
			else
			{
				result = 0;
			}
			return result;
		}

		// Token: 0x0600013A RID: 314 RVA: 0x00017D90 File Offset: 0x00015F90
		public static int SetSpeedPressure()
		{
			int ySpeedLow = Configure.ySpeedLow;
			int pressureZSpeed = Configure.pressureZSpeed;
			string text = GSC_Controller.SetSpeed(2, pressureZSpeed, pressureZSpeed, 0);
			Logs.WriteInfo(string.Format("SetSpeed:2, {0}, result:{1}", pressureZSpeed, text), true);
			bool flag = text == "" || text.IndexOf("NG") != -1;
			int result;
			if (flag)
			{
				result = 1;
			}
			else
			{
				result = 0;
			}
			return result;
		}

		// Token: 0x0600013B RID: 315 RVA: 0x00017E00 File Offset: 0x00016000
		public static void SetSpeed()
		{
			int ySpeedLow = Configure.ySpeedLow;
			int zSpeed = Configure.zSpeed3;
			GSC_Controller.SetSpeed(2, ySpeedLow, ySpeedLow, 0, zSpeed, zSpeed, 0);
			Logs.WriteInfo(string.Format("SetSpeed:2,{0},{1}", ySpeedLow, zSpeed), true);
		}

		// Token: 0x0600013C RID: 316 RVA: 0x00017E44 File Offset: 0x00016044
		public void Home()
		{
			MotionController.running = true;
			this.t = new Thread(new ParameterizedThreadStart(this.MoveFunc));
			this.t.Start(99);
		}

		// Token: 0x0600013D RID: 317 RVA: 0x00017E77 File Offset: 0x00016077
		public void Moduel0_0()
		{
			MotionController.running = true;
			this.t = new Thread(new ParameterizedThreadStart(this.MoveFunc));
			this.t.Start(0);
		}

		// Token: 0x0600013E RID: 318 RVA: 0x00017EA9 File Offset: 0x000160A9
		public void Moduel0_1()
		{
			MotionController.running = true;
			this.t = new Thread(new ParameterizedThreadStart(this.MoveFunc));
			this.t.Start(1);
		}

		// Token: 0x0600013F RID: 319 RVA: 0x00017EDB File Offset: 0x000160DB
		public void Moduel1_0()
		{
			MotionController.running = true;
			this.t = new Thread(new ParameterizedThreadStart(this.MoveFunc));
			this.t.Start(10);
		}

		// Token: 0x06000140 RID: 320 RVA: 0x00017F0E File Offset: 0x0001610E
		public void Moduel1_1()
		{
			MotionController.running = true;
			this.t = new Thread(new ParameterizedThreadStart(this.MoveFunc));
			this.t.Start(11);
		}

		// Token: 0x06000141 RID: 321 RVA: 0x00017F41 File Offset: 0x00016141
		public void MoveAndCheck()
		{
			MotionController.running = true;
			this.t = new Thread(new ThreadStart(this.MoveAndCheckFunc));
			this.t.Priority = ThreadPriority.Highest;
			this.t.Start();
		}

		// Token: 0x06000142 RID: 322 RVA: 0x00017F7A File Offset: 0x0001617A
		public void ZMoveRel()
		{
			GSC_Controller.MoveRel("2", 1000);
		}

		// Token: 0x06000143 RID: 323 RVA: 0x00017F90 File Offset: 0x00016190
		private void MoveFunc(object param)
		{
			int num = (int)param;
			int num2 = num;
			int num3 = num2;
			if (num3 == 99)
			{
				GSC_Controller.Home("w", "-+");
			}
			MotionController.running = false;
		}

		// Token: 0x06000144 RID: 324 RVA: 0x00017FC8 File Offset: 0x000161C8
		private void MoveAndCheckFunc()
		{
			MotionController.SetSpeedPressure();
			MotionController.running = false;
		}

		// Token: 0x040002C7 RID: 711
		public static bool running;

		// Token: 0x040002C8 RID: 712
		private Thread t;

		// Token: 0x040002C9 RID: 713
		private Dictionary<string, string> axis = new Dictionary<string, string>
		{
			{
				"y",
				"1"
			},
			{
				"z",
				"2"
			}
		};
	}
}
