﻿using System;
using System.IO.Ports;
using System.Threading;

namespace Nreal_ProductLine_Tool.SUPPORT
{
	// Token: 0x0200000C RID: 12
	public class GSC_Controller
	{
		// Token: 0x06000054 RID: 84 RVA: 0x00008C78 File Offset: 0x00006E78
		public static int Init(string portName)
		{
			GSC_Controller.serialPort.PortName = portName;
			GSC_Controller.serialPort.BaudRate = 9600;
			GSC_Controller.serialPort.DataBits = 8;
			GSC_Controller.serialPort.StopBits = 1;
			GSC_Controller.serialPort.Parity = 0;
			GSC_Controller.serialPort.WriteTimeout = -1;
			GSC_Controller.serialPort.ReadTimeout = 100;
			GSC_Controller.serialPort.DtrEnable = true;
			GSC_Controller.serialPort.RtsEnable = true;
			try
			{
				GSC_Controller.serialPort.Open();
			}
			catch (Exception)
			{
				GSC_Controller.serialPort.Close();
				return -1;
			}
			GSC_Controller.serialPort.Close();
			GSC_Controller.version = GSC_Controller.GetVersion();
			bool flag = GSC_Controller.version == 0.0;
			int result;
			if (flag)
			{
				result = -2;
			}
			else
			{
				result = 0;
			}
			return result;
		}

		// Token: 0x06000055 RID: 85 RVA: 0x00008D5C File Offset: 0x00006F5C
		public static int AutoInit()
		{
			string[] portNames = SerialPort.GetPortNames();
			string[] array = portNames;
			foreach (string portName in array)
			{
				bool flag = GSC_Controller.Init(portName) == 0;
				if (flag)
				{
					GSC_Controller.GetStatus();
					break;
				}
			}
			bool flag2 = GSC_Controller.version > 0.0;
			int result;
			if (flag2)
			{
				result = 0;
			}
			else
			{
				result = -1;
			}
			return result;
		}

		// Token: 0x06000056 RID: 86 RVA: 0x00008DC8 File Offset: 0x00006FC8
		public static double GetVersion()
		{
			string text = GSC_Controller.SendMsg("?:V", true);
			bool flag = text == "";
			double result;
			if (flag)
			{
				result = 0.0;
			}
			else
			{
				string[] array = text.Split(new char[]
				{
					'V'
				});
				bool flag2 = array.Length != 2;
				if (flag2)
				{
					result = 0.0;
				}
				else
				{
					result = Convert.ToDouble(array[1]);
				}
			}
			return result;
		}

		// Token: 0x06000057 RID: 87 RVA: 0x00008E38 File Offset: 0x00007038
		public static int GetSpeed(ref int speed)
		{
			string text = GSC_Controller.SendMsg("?:D2", true);
			bool flag = text == "";
			int result;
			if (flag)
			{
				result = -1;
			}
			else
			{
				Logs.WriteInfo("speed:" + text, true);
				int num = text.IndexOf('F') + 1;
				bool flag2 = num < 0;
				if (flag2)
				{
					result = -1;
				}
				else
				{
					int num2 = text.IndexOf('R') - num;
					bool flag3 = num2 < 0;
					if (flag3)
					{
						result = -1;
					}
					else
					{
						string s = text.Substring(num, num2);
						try
						{
							speed = int.Parse(s);
						}
						catch (FormatException)
						{
							return -1;
						}
						result = 0;
					}
				}
			}
			return result;
		}

		// Token: 0x06000058 RID: 88 RVA: 0x00008EE4 File Offset: 0x000070E4
		public static void GetAxisStatus(out int p1, out int p2, out bool b)
		{
			GSC_Controller.GetStatus();
			p1 = GSC_Controller.axis1p;
			p2 = GSC_Controller.axis2p;
			b = GSC_Controller.busy;
		}

		// Token: 0x06000059 RID: 89 RVA: 0x00008F04 File Offset: 0x00007104
		public static string SetSpeed(int type, int s1_1, int s1_2, int s1_3, int s2_1, int s2_2, int s2_3)
		{
			string msg = string.Format("D:{0}S{1}F{2}R{3}S{4}F{5}R{6}", new object[]
			{
				type,
				s1_1,
				s1_2,
				s1_3,
				s2_1,
				s2_2,
				s2_3
			});
			return GSC_Controller.SendMsg(msg, true);
		}

		// Token: 0x0600005A RID: 90 RVA: 0x00008F74 File Offset: 0x00007174
		public static string SetSpeed(int type, int s1_1, int s1_2, int s1_3)
		{
			string msg = string.Format("D:{0}S{1}F{2}R{3}", new object[]
			{
				type,
				s1_1,
				s1_2,
				s1_3
			});
			return GSC_Controller.SendMsg(msg, true);
		}

		// Token: 0x0600005B RID: 91 RVA: 0x00008FC4 File Offset: 0x000071C4
		public static void ContinueMove(string axis, string direction)
		{
			GSC_Controller.SendMsg("J:" + axis + direction, false);
			GSC_Controller.SendMsg("G", false);
		}

		// Token: 0x0600005C RID: 92 RVA: 0x00008FE8 File Offset: 0x000071E8
		public static void Stop(string axis)
		{
			string msg = "L:" + axis;
			GSC_Controller.SendMsg(msg, false);
		}

		// Token: 0x0600005D RID: 93 RVA: 0x0000900C File Offset: 0x0000720C
		public static void StopE()
		{
			string msg = "L:E";
			GSC_Controller.SendMsg(msg, false);
		}

		// Token: 0x0600005E RID: 94 RVA: 0x00009028 File Offset: 0x00007228
		public static void Home(string axis, string direction)
		{
			string msg = "H:" + axis + direction;
			string text = GSC_Controller.SendMsg(msg, false);
			GSC_Controller.busy = true;
			while (GSC_Controller.busy)
			{
				GSC_Controller.GetStatus();
				Thread.Sleep(50);
			}
		}

		// Token: 0x0600005F RID: 95 RVA: 0x0000906C File Offset: 0x0000726C
		public static void MoveAbs(string axis, int pulse)
		{
			bool flag = !GSC_Controller.GetStatus();
			if (!flag)
			{
				bool flag2 = axis == "1";
				int pulse2;
				if (flag2)
				{
					pulse2 = pulse - GSC_Controller.axis1p;
				}
				else
				{
					bool flag3 = !(axis == "2");
					if (flag3)
					{
						return;
					}
					pulse2 = pulse - GSC_Controller.axis2p;
				}
				GSC_Controller.MoveRel(axis, pulse2);
			}
		}

		// Token: 0x06000060 RID: 96 RVA: 0x000090D0 File Offset: 0x000072D0
		public static void MoveRel(string axis, int pulse)
		{
			string arg = (pulse < 0) ? "-" : "+";
			int num = Math.Abs(pulse);
			string text = GSC_Controller.SendMsg(string.Format("M:{0}{1}P{2}", axis, arg, num), false);
			text = GSC_Controller.SendMsg("G", false);
			GSC_Controller.busy = true;
			while (GSC_Controller.busy)
			{
				GSC_Controller.GetStatus();
				Thread.Sleep(50);
			}
		}

		// Token: 0x06000061 RID: 97 RVA: 0x0000913C File Offset: 0x0000733C
		public static void MoveBothAbs(int pulse1, int pulse2)
		{
			bool status = GSC_Controller.GetStatus();
			if (status)
			{
				int pulse3 = pulse1 - GSC_Controller.axis1p;
				int pulse4 = pulse2 - GSC_Controller.axis2p;
				GSC_Controller.MoveBothRel(pulse3, pulse4);
			}
		}

		// Token: 0x06000062 RID: 98 RVA: 0x00009170 File Offset: 0x00007370
		public static void MoveBothRel(int pulse1, int pulse2)
		{
			string text = (pulse1 < 0) ? "-" : "+";
			string text2 = (pulse2 < 0) ? "-" : "+";
			int num = Math.Abs(pulse1);
			int num2 = Math.Abs(pulse2);
			string text3 = GSC_Controller.SendMsg(string.Format("M:W{0}P{1}{2}P{3}", new object[]
			{
				text,
				num,
				text2,
				num2
			}), false);
			text3 = GSC_Controller.SendMsg("G", false);
			GSC_Controller.busy = true;
			while (GSC_Controller.busy)
			{
				GSC_Controller.GetStatus();
				Thread.Sleep(50);
			}
		}

		// Token: 0x06000063 RID: 99 RVA: 0x00009210 File Offset: 0x00007410
		public static bool GetStatus()
		{
			string receive = GSC_Controller.SendMsg("Q:", true);
			return GSC_Controller.AnalysisStatus(receive, ref GSC_Controller.axis1p, ref GSC_Controller.axis2p, ref GSC_Controller.busy);
		}

		// Token: 0x06000064 RID: 100 RVA: 0x00009244 File Offset: 0x00007444
		private static bool AnalysisStatus(string receive, ref int p1, ref int p2, ref bool busy)
		{
			bool result;
			try
			{
				string[] array = receive.Split(new char[]
				{
					','
				});
				bool flag = array.Length != 5;
				if (flag)
				{
					result = false;
				}
				else
				{
					p1 = Convert.ToInt32(array[0].Trim().Replace(" ", ""));
					p2 = Convert.ToInt32(array[1].Trim().Replace(" ", ""));
					string a = array[4];
					bool flag2 = a == "B";
					if (flag2)
					{
						busy = true;
					}
					else
					{
						busy = false;
					}
					result = true;
				}
			}
			catch (Exception)
			{
				result = false;
			}
			return result;
		}

		// Token: 0x06000065 RID: 101 RVA: 0x000092F0 File Offset: 0x000074F0
		private static string SendMsg(string msg, bool haveReturn)
		{
			bool flag = false;
			int num = 0;
			string text = "";
			try
			{
				GSC_Controller.serialPort.Open();
				GSC_Controller.serialPort.Write(msg + "\r\n");
				if (haveReturn)
				{
					while (!flag && num < 3)
					{
						bool flag2 = GSC_Controller.serialPort.BytesToRead > 0;
						if (flag2)
						{
							text = GSC_Controller.serialPort.ReadLine();
							text = text.Replace("\r", "");
							text = text.Replace("\n", "");
							break;
						}
						Thread.Sleep(50);
						num++;
					}
				}
				GSC_Controller.serialPort.Close();
			}
			catch (Exception)
			{
			}
			return text;
		}

		// Token: 0x04000086 RID: 134
		private static SerialPort serialPort = new SerialPort();

		// Token: 0x04000087 RID: 135
		private static double version;

		// Token: 0x04000088 RID: 136
		private static int axis1p;

		// Token: 0x04000089 RID: 137
		private static int axis2p;

		// Token: 0x0400008A RID: 138
		private static bool busy;

		// Token: 0x02000028 RID: 40
		public enum axis
		{
			// Token: 0x040002EA RID: 746
			y = 1,
			// Token: 0x040002EB RID: 747
			x
		}
	}
}
