﻿using System;
using System.Runtime.InteropServices;

namespace Nreal_ProductLine_Tool.SUPPORT
{
	// Token: 0x02000022 RID: 34
	public class CameraImageLib
	{
		// Token: 0x06000150 RID: 336
		[DllImport("BaslerController.dll", CallingConvention = CallingConvention.Cdecl)]
		public static extern int Init([MarshalAs(UnmanagedType.LPStr)] string name, int max_width, int min_width, int max_number);

		// Token: 0x06000151 RID: 337
		[DllImport("BaslerController.dll", CallingConvention = CallingConvention.Cdecl)]
		public static extern int Init_spb([MarshalAs(UnmanagedType.LPStr)] string name, double upper_max_width, double upper_min_width, double left_max_width, double left_min_width, double right_max_width, double right_min_width, double upper_max_angle);

		// Token: 0x06000152 RID: 338
		[DllImport("BaslerController.dll", CallingConvention = CallingConvention.Cdecl)]
		public static extern int Init_support(int left_thresh, int right_thresh, int roi_x, int roi_y, int roi_width);

		// Token: 0x06000153 RID: 339
		[DllImport("BaslerController.dll", CallingConvention = CallingConvention.Cdecl)]
		public static extern int Init_support_preCheck(int[] val, int len);

		// Token: 0x06000154 RID: 340
		[DllImport("BaslerController.dll", CallingConvention = CallingConvention.Cdecl)]
		public static extern int Capture();

		// Token: 0x06000155 RID: 341
		[DllImport("BaslerController.dll", CallingConvention = CallingConvention.Cdecl)]
		public static extern int Analysis(int type, double[] result);

		// Token: 0x06000156 RID: 342
		[DllImport("BaslerController.dll", CallingConvention = CallingConvention.Cdecl)]
		public static extern int AnalysisSupport(int type, double[] result);

		// Token: 0x06000157 RID: 343
		[DllImport("BaslerController.dll", CallingConvention = CallingConvention.Cdecl)]
		public static extern int AnalysisSupportPreCheck(int type, double[] result);

		// Token: 0x06000158 RID: 344
		[DllImport("BaslerController.dll", CallingConvention = CallingConvention.Cdecl)]
		public static extern int PixelDistantCali(int type, double[] result);

		// Token: 0x06000159 RID: 345
		[DllImport("BaslerController.dll", CallingConvention = CallingConvention.Cdecl)]
		public static extern int RegisterCallBackShowImage(Delegate image_transfer);

		// Token: 0x0600015A RID: 346
		[DllImport("BaslerController.dll", CallingConvention = CallingConvention.Cdecl)]
		public static extern int ShowImage();

		// Token: 0x0600015B RID: 347
		[DllImport("BaslerController.dll", CallingConvention = CallingConvention.Cdecl)]
		public static extern void SetExposureTime(double value);

		// Token: 0x0600015C RID: 348
		[DllImport("BaslerController.dll", CallingConvention = CallingConvention.Cdecl)]
		public static extern void SetGain(double value);

		// Token: 0x0600015D RID: 349
		[DllImport("BaslerController.dll", CallingConvention = CallingConvention.Cdecl)]
		public static extern void SetParamD([MarshalAs(UnmanagedType.LPStr)] string key, double value);

		// Token: 0x0600015E RID: 350
		[DllImport("BaslerController.dll", CallingConvention = CallingConvention.Cdecl)]
		public static extern void SetParamS([MarshalAs(UnmanagedType.LPStr)] string key, [MarshalAs(UnmanagedType.LPStr)] string value);
	}
}
