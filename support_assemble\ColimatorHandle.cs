﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.Timers;
using System.Windows.Forms;
using Nreal_ProductLine_Tool.SUPPORT;

namespace support_assemble
{
	// Token: 0x02000002 RID: 2
	public partial class ColimatorHandle : FormDefault
	{
		// Token: 0x06000001 RID: 1 RVA: 0x00002048 File Offset: 0x00000248
		public ColimatorHandle(Entrance entrance, int sequence, string title)
		{
			this.sequence = sequence;
			this.entrance = entrance;
			this.title = title;
			this.InitializeComponent();
			this.show_timer.Elapsed += this.Show_Timer_Elapsed;
			this.show_timer.Interval = 200.0;
			this.show_timer.Enabled = false;
			base.TopLevel = false;
			base.FormBorderStyle = FormBorderStyle.None;
			this.Dock = DockStyle.Fill;
		}

		// Token: 0x06000002 RID: 2 RVA: 0x00002114 File Offset: 0x00000314
		private void p1_activate_btn_Click(object sender, EventArgs e)
		{
			this.check_flag = 1;
			this.colimator_p2_groupBox.Enabled = false;
			this.colimator_p1_groupBox.Enabled = true;
			this.colimator_1_btn.Enabled = true;
			this.show_timer.Enabled = true;
			this.p1_checked = true;
		}

		// Token: 0x06000003 RID: 3 RVA: 0x00002164 File Offset: 0x00000364
		private void InitStatus()
		{
			this.p1_x_label.Text = "";
			this.p1_y_label.Text = "";
			this.p3_x_label.Text = "";
			this.p3_y_label.Text = "";
			this.p13_avgx_label.Text = "";
			this.p13_avgy_label.Text = "";
			this.p2_x_label.Text = "";
			this.p2_y_label.Text = "";
			this.p1_x_label.BackColor = this.gray;
			this.p1_y_label.BackColor = this.gray;
			this.p3_x_label.BackColor = this.gray;
			this.p3_y_label.BackColor = this.gray;
			this.p13_avgx_label.BackColor = this.gray;
			this.p13_avgy_label.BackColor = this.gray;
			this.p2_x_label.BackColor = this.gray;
			this.p2_y_label.BackColor = this.gray;
			this.coli_diff_label.BackColor = this.gray;
			this.coli_diff_label.Text = "";
		}

		// Token: 0x06000004 RID: 4 RVA: 0x000022B0 File Offset: 0x000004B0
		public override void Start()
		{
			ColimatorHandle.timer_stop = false;
			Colimator.serialPort = Colimator.serialPort1;
			Colimator.TurnOnColimator();
			Colimator.serialPort = Colimator.serialPort2;
			Colimator.TurnOnColimator();
			this.colimator_1_btn.Enabled = false;
			this.InitStatus();
			this.show_timer.Enabled = true;
			base.Show();
		}

		// Token: 0x06000005 RID: 5 RVA: 0x0000230C File Offset: 0x0000050C
		public override void ExcepStop()
		{
			ColimatorHandle.timer_stop = true;
			this.show_timer.Enabled = false;
			Colimator.serialPort = Colimator.serialPort2;
			Colimator.TurnOffColimator();
			Colimator.serialPort = Colimator.serialPort1;
			Colimator.TurnOffColimator();
		}

		// Token: 0x06000006 RID: 6 RVA: 0x00002344 File Offset: 0x00000544
		private int FindEffectPoints()
		{
			int num = 0;
			bool flag = GlobalData.colimatorOrigDataP13[0] > Configure.x_except_min && GlobalData.colimatorOrigDataP13[0] < Configure.x_except_max && GlobalData.colimatorOrigDataP13[1] > Configure.y_except_min && GlobalData.colimatorOrigDataP13[1] < Configure.y_except_max;
			if (flag)
			{
				num++;
				GlobalData.phrase1_colimatorPoint1[0] = GlobalData.colimatorOrigDataP13[0];
				GlobalData.phrase1_colimatorPoint1[1] = GlobalData.colimatorOrigDataP13[1];
			}
			else
			{
				GlobalData.phrase1_colimatorPoint1[0] = Configure.x_except_max;
				GlobalData.phrase1_colimatorPoint1[1] = Configure.y_except_max;
			}
			bool flag2 = GlobalData.colimatorOrigDataP13[2] > Configure.x_except_min && GlobalData.colimatorOrigDataP13[2] < Configure.x_except_max && GlobalData.colimatorOrigDataP13[3] > Configure.y_except_min && GlobalData.colimatorOrigDataP13[3] < Configure.y_except_max;
			if (flag2)
			{
				num++;
				GlobalData.phrase1_colimatorPoint3[0] = GlobalData.colimatorOrigDataP13[2];
				GlobalData.phrase1_colimatorPoint3[1] = GlobalData.colimatorOrigDataP13[3];
			}
			else
			{
				GlobalData.phrase1_colimatorPoint3[0] = Configure.x_except_max;
				GlobalData.phrase1_colimatorPoint3[1] = Configure.y_except_max;
			}
			bool flag3 = GlobalData.colimatorOrigDataP13[4] > Configure.x_except_min && GlobalData.colimatorOrigDataP13[4] < Configure.x_except_max && GlobalData.colimatorOrigDataP13[5] > Configure.y_except_min && GlobalData.colimatorOrigDataP13[5] < Configure.y_except_max;
			if (flag3)
			{
				bool flag4 = num < 2;
				if (flag4)
				{
					bool flag5 = GlobalData.phrase1_colimatorPoint1[0] == Configure.x_except_max;
					if (flag5)
					{
						GlobalData.phrase1_colimatorPoint1[0] = GlobalData.colimatorOrigDataP13[4];
						GlobalData.phrase1_colimatorPoint1[1] = GlobalData.colimatorOrigDataP13[5];
					}
					bool flag6 = GlobalData.phrase1_colimatorPoint3[0] == Configure.x_except_max;
					if (flag6)
					{
						GlobalData.phrase1_colimatorPoint3[0] = GlobalData.colimatorOrigDataP13[4];
						GlobalData.phrase1_colimatorPoint3[1] = GlobalData.colimatorOrigDataP13[5];
					}
				}
				else
				{
					bool flag7 = GlobalData.phrase1_colimatorPoint1[0] > GlobalData.phrase1_colimatorPoint3[0];
					if (flag7)
					{
						bool flag8 = GlobalData.phrase1_colimatorPoint1[0] > GlobalData.colimatorOrigDataP13[4];
						if (flag8)
						{
							GlobalData.phrase1_colimatorPoint1[0] = GlobalData.colimatorOrigDataP13[4];
							GlobalData.phrase1_colimatorPoint1[1] = GlobalData.colimatorOrigDataP13[5];
						}
					}
					else
					{
						bool flag9 = GlobalData.phrase1_colimatorPoint3[0] > GlobalData.colimatorOrigDataP13[4];
						if (flag9)
						{
							GlobalData.phrase1_colimatorPoint3[0] = GlobalData.colimatorOrigDataP13[4];
							GlobalData.phrase1_colimatorPoint3[1] = GlobalData.colimatorOrigDataP13[5];
						}
					}
				}
				num++;
			}
			return num;
		}

		// Token: 0x06000007 RID: 7 RVA: 0x0000259C File Offset: 0x0000079C
		private void p2_operate()
		{
			GlobalData.phrase1_colimatorPoint2[0] = GlobalData.colimatorOrigDataP2[0];
			GlobalData.phrase1_colimatorPoint2[1] = GlobalData.colimatorOrigDataP2[1];
			this.p2_x_label.Text = GlobalData.colimatorOrigDataP2[0].ToString("F4");
			this.p2_y_label.Text = GlobalData.colimatorOrigDataP2[1].ToString("F4");
			bool flag = GlobalData.colimatorOrigDataP2[0] < Configure.x_point2_min || GlobalData.colimatorOrigDataP2[0] > Configure.x_point2_max;
			if (flag)
			{
				this.p2_x_label.BackColor = this.fail;
			}
			else
			{
				this.p2_x_label.BackColor = this.pass;
			}
			bool flag2 = GlobalData.colimatorOrigDataP2[1] < Configure.y_point2_min || GlobalData.colimatorOrigDataP2[1] > Configure.y_point2_max;
			if (flag2)
			{
				this.p2_y_label.BackColor = this.fail;
			}
			else
			{
				this.p2_y_label.BackColor = this.pass;
			}
		}

		// Token: 0x06000008 RID: 8 RVA: 0x0000269C File Offset: 0x0000089C
		private void Show_Timer_Elapsed(object sender, ElapsedEventArgs e)
		{
			bool flag = !ColimatorHandle.timer_stop;
			if (flag)
			{
				base.Invoke(new MethodInvoker(delegate()
				{
					this.InitStatus();
					int num = this.FindEffectPoints();
					bool flag2 = num == 0;
					if (flag2)
					{
						this.colimator_p13_status_label.Text = "当前没有有效点";
						this.colimator_p13_status_label.ForeColor = Color.Red;
						this.p2_operate();
					}
					else
					{
						this.colimator_p13_status_label.Text = "";
						this.colimator_p13_status_label.ForeColor = Color.Gray;
						bool flag3 = num == 1;
						if (flag3)
						{
							bool flag4 = GlobalData.phrase1_colimatorPoint1[0] != Configure.x_except_max;
							if (flag4)
							{
								GlobalData.phrase1_colimatorPoint3[0] = GlobalData.phrase1_colimatorPoint1[0];
								GlobalData.phrase1_colimatorPoint3[1] = GlobalData.phrase1_colimatorPoint1[1];
							}
							else
							{
								GlobalData.phrase1_colimatorPoint1[0] = GlobalData.phrase1_colimatorPoint3[0];
								GlobalData.phrase1_colimatorPoint1[1] = GlobalData.phrase1_colimatorPoint3[1];
							}
						}
						this.p1_x_label.Text = GlobalData.phrase1_colimatorPoint1[0].ToString("F4");
						this.p1_y_label.Text = GlobalData.phrase1_colimatorPoint1[1].ToString("F4");
						this.p3_x_label.Text = GlobalData.phrase1_colimatorPoint3[0].ToString("F4");
						this.p3_y_label.Text = GlobalData.phrase1_colimatorPoint3[1].ToString("F4");
						bool flag5 = GlobalData.phrase1_colimatorPoint1[0] < Configure.x_point1_min || GlobalData.phrase1_colimatorPoint1[0] > Configure.x_point1_max;
						if (flag5)
						{
							this.p1_x_label.BackColor = this.fail;
						}
						else
						{
							this.p1_x_label.BackColor = this.pass;
						}
						bool flag6 = GlobalData.phrase1_colimatorPoint1[1] < Configure.y_point1_min || GlobalData.phrase1_colimatorPoint1[1] > Configure.y_point1_max;
						if (flag6)
						{
							this.p1_y_label.BackColor = this.fail;
						}
						else
						{
							this.p1_y_label.BackColor = this.pass;
						}
						bool flag7 = GlobalData.phrase1_colimatorPoint3[0] < Configure.x_point3_min || GlobalData.phrase1_colimatorPoint3[0] > Configure.x_point3_max;
						if (flag7)
						{
							this.p3_x_label.BackColor = this.fail;
						}
						else
						{
							this.p3_x_label.BackColor = this.pass;
						}
						bool flag8 = GlobalData.phrase1_colimatorPoint3[1] < Configure.y_point3_min || GlobalData.phrase1_colimatorPoint3[1] > Configure.y_point3_max;
						if (flag8)
						{
							this.p3_y_label.BackColor = this.fail;
						}
						else
						{
							this.p3_y_label.BackColor = this.pass;
						}
						GlobalData.phrase1_colimatorPoint13Avg[0] = (GlobalData.phrase1_colimatorPoint1[0] + GlobalData.phrase1_colimatorPoint3[0]) / 2.0;
						GlobalData.phrase1_colimatorPoint13Avg[1] = (GlobalData.phrase1_colimatorPoint1[1] + GlobalData.phrase1_colimatorPoint3[1]) / 2.0;
						this.p13_avgx_label.Text = GlobalData.phrase1_colimatorPoint13Avg[0].ToString("F4");
						this.p13_avgy_label.Text = GlobalData.phrase1_colimatorPoint13Avg[1].ToString("F4");
						bool flag9 = GlobalData.phrase1_colimatorPoint13Avg[0] < Configure.x_point13_min || GlobalData.phrase1_colimatorPoint13Avg[0] > Configure.x_point13_max;
						if (flag9)
						{
							this.p13_avgx_label.BackColor = this.fail;
						}
						else
						{
							this.p13_avgx_label.BackColor = this.pass;
						}
						bool flag10 = GlobalData.phrase1_colimatorPoint13Avg[1] < Configure.y_point13_min || GlobalData.phrase1_colimatorPoint13Avg[1] > Configure.y_point13_max;
						if (flag10)
						{
							this.p13_avgy_label.BackColor = this.fail;
						}
						else
						{
							this.p13_avgy_label.BackColor = this.pass;
						}
						this.p2_operate();
						double x = GlobalData.phrase1_colimatorPoint13Avg[0] - GlobalData.colimatorOrigDataP2[0];
						double x2 = GlobalData.phrase1_colimatorPoint13Avg[1] - GlobalData.colimatorOrigDataP2[1];
						double num2 = Math.Pow(x, 2.0) + Math.Pow(x2, 2.0);
						num2 = Math.Sqrt(num2);
						this.coli_diff_label.Text = Math.Round(num2, 3).ToString();
						GlobalData.phrase1_colimatorDiff = num2;
						bool flag11 = num2 > Configure.diff_min && num2 < Configure.diff_max;
						if (flag11)
						{
							this.coli_diff_label.BackColor = this.pass;
						}
						else
						{
							this.coli_diff_label.BackColor = this.fail;
						}
						bool flag12 = this.p1_x_label.BackColor == this.pass && this.p1_y_label.BackColor == this.pass && this.p3_x_label.BackColor == this.pass && this.p3_y_label.BackColor == this.pass && this.p13_avgx_label.BackColor == this.pass && this.p13_avgy_label.BackColor == this.pass && this.p2_x_label.BackColor == this.pass && this.p2_y_label.BackColor == this.pass && this.coli_diff_label.BackColor == this.pass;
						if (flag12)
						{
							this.colimator_1_btn.Enabled = true;
						}
						else
						{
							this.colimator_1_btn.Enabled = false;
						}
					}
				}));
			}
			else
			{
				this.show_timer.Enabled = false;
			}
		}

		// Token: 0x06000009 RID: 9 RVA: 0x000026DC File Offset: 0x000008DC
		private void p3_activate_btn_Click(object sender, EventArgs e)
		{
			this.check_flag = 2;
			this.colimator_p2_groupBox.Enabled = true;
			this.colimator_p1_groupBox.Enabled = false;
			this.show_timer.Enabled = true;
			this.p2_checked = true;
		}

		// Token: 0x0600000A RID: 10 RVA: 0x00002714 File Offset: 0x00000914
		private void coli_pre_button_Click(object sender, EventArgs e)
		{
			ColimatorHandle.timer_stop = true;
			this.show_timer.Enabled = false;
			Colimator.serialPort = Colimator.serialPort1;
			Colimator.TurnOffColimator();
			Colimator.serialPort = Colimator.serialPort2;
			Colimator.TurnOffColimator();
			MotionZ.pulse = 0;
			MotionZ.ax_operate_sem.Release();
			this.entrance.Finish(this.sequence, 3);
		}

		// Token: 0x0600000B RID: 11 RVA: 0x0000277C File Offset: 0x0000097C
		private void colimator_1_btn_Click(object sender, EventArgs e)
		{
			ColimatorHandle.timer_stop = true;
			this.show_timer.Enabled = false;
			Colimator.serialPort = Colimator.serialPort1;
			Colimator.TurnOffColimator();
			Colimator.serialPort = Colimator.serialPort2;
			Colimator.TurnOffColimator();
			this.entrance.Finish(this.sequence, 0);
		}

		// Token: 0x04000001 RID: 1
		private Timer show_timer = new Timer();

		// Token: 0x04000002 RID: 2
		public static bool timer_stop;

		// Token: 0x04000003 RID: 3
		private int check_flag = 0;

		// Token: 0x04000004 RID: 4
		private Color fail = Color.OrangeRed;

		// Token: 0x04000005 RID: 5
		private Color pass = Color.Green;

		// Token: 0x04000006 RID: 6
		private Color gray = Color.Gray;

		// Token: 0x04000007 RID: 7
		private bool p1_checked = false;

		// Token: 0x04000008 RID: 8
		private bool p2_checked = false;

		// Token: 0x04000009 RID: 9
		private Entrance entrance;

		// Token: 0x0400000A RID: 10
		private int sequence;

		// Token: 0x0400000B RID: 11
		private int type;

		// Token: 0x0400000C RID: 12
		private string title;
	}
}
