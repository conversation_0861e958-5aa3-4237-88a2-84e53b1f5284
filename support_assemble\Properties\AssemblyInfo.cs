﻿using System;
using System.Diagnostics;
using System.Reflection;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;
using System.Runtime.Versioning;

[assembly: AssemblyVersion("*******")]
[assembly: AssemblyTitle("support_assemble")]
[assembly: AssemblyDescription("")]
[assembly: AssemblyConfiguration("")]
[assembly: AssemblyCompany("P R C")]
[assembly: AssemblyProduct("support_assemble")]
[assembly: AssemblyCopyright("Copyright © P R C 2024")]
[assembly: AssemblyTrademark("")]
[assembly: ComVisible(false)]
[assembly: Guid("4772358e-ae3b-4e58-b9bb-a6ed0d137052")]
[assembly: AssemblyFileVersion("*******")]
