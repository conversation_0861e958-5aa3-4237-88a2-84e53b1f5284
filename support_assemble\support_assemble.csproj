﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">x64</Platform>
    <ProjectGuid>{7D471186-1164-4851-B7B6-93D43EDE63D6}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <RootNamespace>support_assemble</RootNamespace>
    <AssemblyName>support_assemble</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <ApplicationManifest>app.manifest</ApplicationManifest>
    <StartupObject>Nreal_ProductLine_Tool.SUPPORT.Program</StartupObject>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|x64' ">
    <PlatformTarget>x64</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|x64' ">
    <PlatformTarget>x64</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="log4net">
      <HintPath>\\172.50.10.36\d\新流程\gina框架\log4net.dll</HintPath>
    </Reference>
    <Reference Include="NModbus4" />
    <Reference Include="ParamManager" />
    <Reference Include="STILSensors" />
    <Reference Include="System">
      <HintPath>..\..\..\工具\dnSpy\bin\System.dll</HintPath>
    </Reference>
    <Reference Include="System.Data" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Text.Json">
      <HintPath>\\172.50.10.36\d\新流程\gina框架\System.Text.Json.dll</HintPath>
    </Reference>
    <Reference Include="System.Windows.Forms" />
  </ItemGroup>
  <ItemGroup>
    <AppDesigner Include="Properties\" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="ColimatorHandle.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ColimatorHandle.Designer.cs">
      <DependentUpon>ColimatorHandle.cs</DependentUpon>
    </Compile>
    <Compile Include="controller\ImageHandle.cs" />
    <Compile Include="Nreal_ProductLine_Tool\SUPPORT\CameraImageLib.cs" />
    <Compile Include="Nreal_ProductLine_Tool\SUPPORT\cDistance.cs" />
    <Compile Include="Nreal_ProductLine_Tool\SUPPORT\Colimator.cs" />
    <Compile Include="Nreal_ProductLine_Tool\SUPPORT\ColimatorUVHandle.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Nreal_ProductLine_Tool\SUPPORT\ColimatorUVHandle.Designer.cs">
      <DependentUpon>ColimatorUVHandle.cs</DependentUpon>
    </Compile>
    <Compile Include="Nreal_ProductLine_Tool\SUPPORT\Configure.cs" />
    <Compile Include="Nreal_ProductLine_Tool\SUPPORT\Context.cs" />
    <Compile Include="Nreal_ProductLine_Tool\SUPPORT\Db.cs" />
    <Compile Include="Nreal_ProductLine_Tool\SUPPORT\Distance.cs" />
    <Compile Include="Nreal_ProductLine_Tool\SUPPORT\Entrance.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Nreal_ProductLine_Tool\SUPPORT\Entrance.Designer.cs">
      <DependentUpon>Entrance.cs</DependentUpon>
    </Compile>
    <Compile Include="Nreal_ProductLine_Tool\SUPPORT\FormDefault.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Nreal_ProductLine_Tool\SUPPORT\FormDefault.Designer.cs">
      <DependentUpon>FormDefault.cs</DependentUpon>
    </Compile>
    <Compile Include="Nreal_ProductLine_Tool\SUPPORT\GlobalData.cs" />
    <Compile Include="Nreal_ProductLine_Tool\SUPPORT\GSC_Controller.cs" />
    <Compile Include="Nreal_ProductLine_Tool\SUPPORT\LensOp.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Nreal_ProductLine_Tool\SUPPORT\LensOp.Designer.cs">
      <DependentUpon>LensOp.cs</DependentUpon>
    </Compile>
    <Compile Include="Nreal_ProductLine_Tool\SUPPORT\Link.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Nreal_ProductLine_Tool\SUPPORT\Link.Designer.cs">
      <DependentUpon>Link.cs</DependentUpon>
    </Compile>
    <Compile Include="Nreal_ProductLine_Tool\SUPPORT\LinkAdjust.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Nreal_ProductLine_Tool\SUPPORT\LinkAdjust.Designer.cs">
      <DependentUpon>LinkAdjust.cs</DependentUpon>
    </Compile>
    <Compile Include="Nreal_ProductLine_Tool\SUPPORT\LinkPress.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Nreal_ProductLine_Tool\SUPPORT\LinkPress.Designer.cs">
      <DependentUpon>LinkPress.cs</DependentUpon>
    </Compile>
    <Compile Include="Nreal_ProductLine_Tool\SUPPORT\LOGINFO.cs" />
    <Compile Include="Nreal_ProductLine_Tool\SUPPORT\Logs.cs" />
    <Compile Include="Nreal_ProductLine_Tool\SUPPORT\main.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Nreal_ProductLine_Tool\SUPPORT\main.Designer.cs">
      <DependentUpon>main.cs</DependentUpon>
    </Compile>
    <Compile Include="Nreal_ProductLine_Tool\SUPPORT\MotionController.cs" />
    <Compile Include="Nreal_ProductLine_Tool\SUPPORT\MotionZ.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Nreal_ProductLine_Tool\SUPPORT\MotionZ.Designer.cs">
      <DependentUpon>MotionZ.cs</DependentUpon>
    </Compile>
    <Compile Include="Nreal_ProductLine_Tool\SUPPORT\Position.cs" />
    <Compile Include="Nreal_ProductLine_Tool\SUPPORT\PreCheck.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Nreal_ProductLine_Tool\SUPPORT\PreCheck.Designer.cs">
      <DependentUpon>PreCheck.cs</DependentUpon>
    </Compile>
    <Compile Include="Nreal_ProductLine_Tool\SUPPORT\PressSensor.cs" />
    <Compile Include="Nreal_ProductLine_Tool\SUPPORT\Program.cs" />
    <Compile Include="Nreal_ProductLine_Tool\SUPPORT\ScanSN.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Nreal_ProductLine_Tool\SUPPORT\ScanSN.Designer.cs">
      <DependentUpon>ScanSN.cs</DependentUpon>
    </Compile>
    <Compile Include="Nreal_ProductLine_Tool\SUPPORT\uv.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Nreal_ProductLine_Tool\SUPPORT\uv.Designer.cs">
      <DependentUpon>uv.cs</DependentUpon>
    </Compile>
    <Compile Include="Nreal_ProductLine_Tool\SUPPORT\Vacuummeter.cs" />
    <Compile Include="Nreal_ProductLine_Tool\SUPPORT\zmcaux.cs" />
    <Compile Include="Nreal_ProductLine_Tool\SUPPORT\ZMotionController.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Properties\Resources.cs" />
    <Compile Include="Properties\Settings.Designer.cs">
      <DependentUpon>Settings.settings</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
    <Compile Include="test.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="test.Designer.cs">
      <DependentUpon>test.cs</DependentUpon>
    </Compile>
    <Compile Include="Vacuum.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Vacuum.Designer.cs">
      <DependentUpon>Vacuum.cs</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="ColimatorHandle.resources" />
    <EmbeddedResource Include="FormDefault.resources" />
    <EmbeddedResource Include="Nreal_ProductLine_Tool\SUPPORT\ColimatorUVHandle.resources" />
    <EmbeddedResource Include="Nreal_ProductLine_Tool\SUPPORT\Entrance.resources" />
    <EmbeddedResource Include="Nreal_ProductLine_Tool\SUPPORT\LensOp.resources" />
    <EmbeddedResource Include="Nreal_ProductLine_Tool\SUPPORT\Link.resources" />
    <EmbeddedResource Include="Nreal_ProductLine_Tool\SUPPORT\LinkAdjust.resources" />
    <EmbeddedResource Include="Nreal_ProductLine_Tool\SUPPORT\LinkPress.resources" />
    <EmbeddedResource Include="Nreal_ProductLine_Tool\SUPPORT\main.resources" />
    <EmbeddedResource Include="Nreal_ProductLine_Tool\SUPPORT\MotionZ.resources" />
    <EmbeddedResource Include="Nreal_ProductLine_Tool\SUPPORT\PreCheck.resources" />
    <EmbeddedResource Include="Nreal_ProductLine_Tool\SUPPORT\ScanSN.resources" />
    <EmbeddedResource Include="Properties\Resources.resources" />
    <EmbeddedResource Include="test.resources" />
    <EmbeddedResource Include="Vacuum.resources" />
  </ItemGroup>
  <ItemGroup>
    <None Include="app.manifest" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>