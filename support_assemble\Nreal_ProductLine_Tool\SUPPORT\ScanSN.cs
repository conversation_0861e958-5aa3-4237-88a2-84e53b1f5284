﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;

namespace Nreal_ProductLine_Tool.SUPPORT
{
	// Token: 0x0200001B RID: 27
	public partial class ScanSN : FormDefault
	{
		// Token: 0x060000F1 RID: 241 RVA: 0x00013A78 File Offset: 0x00011C78
		public ScanSN(Entrance entrance, int sequence, string title)
		{
			this.sequence = sequence;
			this.entrance = entrance;
			this.title = title;
			this.InitializeComponent();
			base.TopLevel = false;
			base.FormBorderStyle = FormBorderStyle.None;
			this.Dock = DockStyle.Fill;
			bool flag = sequence == 0;
			if (flag)
			{
				this.prism_code.Text = "请扫补偿镜SN码";
			}
			else
			{
				this.prism_code.Text = "请扫透镜SN码";
			}
		}

		// Token: 0x060000F2 RID: 242 RVA: 0x00013B00 File Offset: 0x00011D00
		public override void ExcepStop()
		{
			GlobalData.sn_buchang_confirm = this.prism_code.Text;
			GlobalData.sn_toujing_confirm = this.len_code.Text;
			GlobalData.sn_buchang_scan = "";
			GlobalData.sn_toujing_scan = "";
			this.prism_code.Text = "";
			this.len_code.Text = "";
			this.groupBox1.Text = "请扫码";
		}

		// Token: 0x060000F3 RID: 243 RVA: 0x00013B78 File Offset: 0x00011D78
		private void scan_finish_btn_Click(object sender, EventArgs e)
		{
			GlobalData.sn_buchang_confirm = this.prism_code.Text;
			GlobalData.sn_toujing_confirm = this.len_code.Text;
			GlobalData.saved_sn_buchang_confirm = GlobalData.sn_buchang_confirm;
			GlobalData.saved_sn_toujing_confirm = GlobalData.sn_toujing_confirm;
			GlobalData.sn_buchang_scan = "";
			GlobalData.sn_toujing_scan = "";
			this.prism_code.Text = "";
			this.len_code.Text = "";
			this.entrance.Finish(this.sequence, 0);
		}

		// Token: 0x060000F4 RID: 244 RVA: 0x00013C04 File Offset: 0x00011E04
		public override string GetName()
		{
			return this.title;
		}

		// Token: 0x060000F5 RID: 245 RVA: 0x00013C1C File Offset: 0x00011E1C
		public override void SetEnable(string prism_sn, string len_sn)
		{
			this.prism_code.Text = prism_sn;
			this.len_code.Text = len_sn;
			this.groupBox1.Text = "待绑定";
			this.scan_finish_btn.Enabled = true;
		}

		// Token: 0x060000F6 RID: 246 RVA: 0x00013C58 File Offset: 0x00011E58
		public override void Start()
		{
			bool flag = this.sequence == 0;
			if (flag)
			{
				this.scan_finish_btn.Enabled = false;
				this.prism_code.Text = GlobalData.sn_buchang_scan;
				this.len_code.Text = GlobalData.sn_toujing_scan;
				bool flag2 = this.prism_code.Text != "" && this.len_code.Text != "";
				if (flag2)
				{
					this.scan_finish_btn.Enabled = true;
					this.groupBox1.Text = "待绑定";
				}
				else
				{
					this.groupBox1.Text = "请扫码";
				}
			}
			else
			{
				bool flag3 = GlobalData.sn_toujing_confirm != "";
				if (flag3)
				{
					this.entrance.Finish(this.sequence, 0);
					return;
				}
				this.scan_finish_btn.Enabled = false;
			}
			base.Show();
		}

		// Token: 0x04000266 RID: 614
		private Entrance entrance;

		// Token: 0x04000267 RID: 615
		private int sequence;

		// Token: 0x04000268 RID: 616
		private int type;

		// Token: 0x04000269 RID: 617
		private string title;

		// Token: 0x0400026A RID: 618
		private string showInfo = "";
	}
}
