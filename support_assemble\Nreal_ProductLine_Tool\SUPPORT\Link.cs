﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.Threading;
using System.Windows.Forms;

namespace Nreal_ProductLine_Tool.SUPPORT
{
	// Token: 0x0200001A RID: 26
	public partial class Link : FormDefault
	{
		// Token: 0x060000E8 RID: 232 RVA: 0x00013358 File Offset: 0x00011558
		public Link(Entrance entrance, int sequence, string title)
		{
			this.sequence = sequence;
			this.entrance = entrance;
			this.title = title;
			this.InitializeComponent();
			base.TopLevel = false;
			base.FormBorderStyle = FormBorderStyle.None;
			this.Dock = DockStyle.Fill;
			this.link_operate_sem = new SemaphoreSlim(0, 1);
			Thread thread = new Thread(new ThreadStart(this.link_operate));
			thread.Start();
			this.motion_operate_sem = new SemaphoreSlim(0, 1);
			thread = new Thread(new ThreadStart(this.motion_operate));
			thread.Start();
		}

		// Token: 0x060000E9 RID: 233 RVA: 0x000133F4 File Offset: 0x000115F4
		public override string GetName()
		{
			return this.title;
		}

		// Token: 0x060000EA RID: 234 RVA: 0x0001340C File Offset: 0x0001160C
		public override void Start()
		{
			Link.axStoped = true;
			base.Show();
		}

		// Token: 0x060000EB RID: 235 RVA: 0x0001341C File Offset: 0x0001161C
		private void motion_operate()
		{
			while (!Link.isExited)
			{
				this.motion_operate_sem.Wait();
				int i;
				for (i = 0; i < 10; i++)
				{
					bool flag = !MotionController.running;
					if (flag)
					{
						break;
					}
					Thread.Sleep(200);
				}
				bool flag2 = i == 10;
				if (flag2)
				{
					MessageBox.Show("当前电机繁忙，请确认重试");
				}
				else
				{
					MotionController.running = true;
					MotionController.SetSpeedPressure();
					GSC_Controller.MoveAbs("2", Configure.zDest2);
					MotionController.SetSpeedHigh();
					MotionController.running = false;
					Logs.WriteInfo("z轴下压结束", true);
					Link.axStoped = true;
				}
			}
		}

		// Token: 0x060000EC RID: 236 RVA: 0x000134CB File Offset: 0x000116CB
		private void link_next_button_Click(object sender, EventArgs e)
		{
			Link.axStoped = true;
			base.Hide();
			this.entrance.Finish(this.sequence, 0);
		}

		// Token: 0x060000ED RID: 237 RVA: 0x000134F0 File Offset: 0x000116F0
		private void link_operate()
		{
			while (!Link.isExited)
			{
				this.link_operate_sem.Wait();
				this.motion_operate_sem.Release();
				double press_val = 0.0;
				double position_d2 = 0.0;
				while (!Link.axStoped)
				{
					PressSensor.getPressValue(ref press_val);
					Position.getPosition(ref position_d2);
					base.Invoke(new MethodInvoker(delegate()
					{
						this.status_label.Text = "电机下压...";
						this.press_val_label.Text = (press_val.ToString() ?? "");
						this.position_val_label.Text = (position_d2.ToString() ?? "");
						bool flag = press_val > Configure.pressure_val || position_d2 > Configure.pisition_val;
						if (flag)
						{
							bool flag2 = press_val > Configure.pressure_val;
							if (flag2)
							{
								Logs.WriteInfo("压力值" + press_val.ToString() + "超出设定值", true);
								this.status_label.Text = "压力值" + press_val.ToString() + "超出设定值,停止电机";
							}
							bool flag3 = position_d2 > Configure.pisition_val;
							if (flag3)
							{
								Logs.WriteInfo("位移值" + position_d2.ToString() + "超出设定值", true);
								this.status_label.Text = "位移值" + position_d2.ToString() + "超出设定值,停止电机";
							}
							GSC_Controller.Stop("2");
						}
					}));
					Thread.Sleep(50);
				}
				base.Invoke(new MethodInvoker(delegate()
				{
					this.status_label.Text = "电机停止,关闭真空泵，打开放气阀";
				}));
				ZMotionController.VacuummeterPumpClose();
				ZMotionController.VacuummeterGasOutOpen();
			}
		}

		// Token: 0x04000256 RID: 598
		private Entrance entrance;

		// Token: 0x04000257 RID: 599
		private int sequence;

		// Token: 0x04000258 RID: 600
		private int type;

		// Token: 0x04000259 RID: 601
		public string title;

		// Token: 0x0400025A RID: 602
		protected SemaphoreSlim link_operate_sem;

		// Token: 0x0400025B RID: 603
		protected SemaphoreSlim motion_operate_sem;

		// Token: 0x0400025C RID: 604
		public static bool isExited;

		// Token: 0x0400025D RID: 605
		public static bool isExcepExited;

		// Token: 0x0400025E RID: 606
		public static bool axStoped;
	}
}
