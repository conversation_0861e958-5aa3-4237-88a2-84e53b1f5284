﻿using System;

namespace Nreal_ProductLine_Tool.SUPPORT
{
	// Token: 0x02000021 RID: 33
	public class Context
	{
		// Token: 0x17000004 RID: 4
		// (get) Token: 0x06000149 RID: 329 RVA: 0x00018195 File Offset: 0x00016395
		// (set) Token: 0x0600014A RID: 330 RVA: 0x0001819D File Offset: 0x0001639D
		public string SN { get; set; }

		// Token: 0x17000005 RID: 5
		// (get) Token: 0x0600014B RID: 331 RVA: 0x000181A6 File Offset: 0x000163A6
		// (set) Token: 0x0600014C RID: 332 RVA: 0x000181AE File Offset: 0x000163AE
		public string S_SN { get; set; }

		// Token: 0x17000006 RID: 6
		// (get) Token: 0x0600014D RID: 333 RVA: 0x000181B7 File Offset: 0x000163B7
		// (set) Token: 0x0600014E RID: 334 RVA: 0x000181BF File Offset: 0x000163BF
		public double GWeight { get; set; }
	}
}
