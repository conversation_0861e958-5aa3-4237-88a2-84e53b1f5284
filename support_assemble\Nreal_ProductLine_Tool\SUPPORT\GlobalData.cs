﻿using System;

namespace Nreal_ProductLine_Tool.SUPPORT
{
	// Token: 0x02000011 RID: 17
	public class GlobalData
	{
		// Token: 0x04000153 RID: 339
		public static string sn_buchang_confirm = "";

		// Token: 0x04000154 RID: 340
		public static string sn_toujing_confirm = "";

		// Token: 0x04000155 RID: 341
		public static string saved_sn_buchang_confirm = "";

		// Token: 0x04000156 RID: 342
		public static string saved_sn_toujing_confirm = "";

		// Token: 0x04000157 RID: 343
		public static string sn_buchang_scan = "";

		// Token: 0x04000158 RID: 344
		public static string sn_toujing_scan = "";

		// Token: 0x04000159 RID: 345
		public static double[] colimatorOrigDataP13 = new double[6];

		// Token: 0x0400015A RID: 346
		public static double[] colimatorOrigDataP2 = new double[2];

		// Token: 0x0400015B RID: 347
		public static double[] phrase1_colimatorPoint1 = new double[2];

		// Token: 0x0400015C RID: 348
		public static double[] phrase1_colimatorPoint2 = new double[2];

		// Token: 0x0400015D RID: 349
		public static double[] phrase1_colimatorPoint3 = new double[2];

		// Token: 0x0400015E RID: 350
		public static double[] phrase1_colimatorPoint13Avg = new double[2];

		// Token: 0x0400015F RID: 351
		public static double phrase1_colimatorDiff;

		// Token: 0x04000160 RID: 352
		public static double[] phrase2_colimatorPoint1 = new double[2];

		// Token: 0x04000161 RID: 353
		public static double[] phrase2_colimatorPoint2 = new double[2];

		// Token: 0x04000162 RID: 354
		public static double[] phrase2_colimatorPoint3 = new double[2];

		// Token: 0x04000163 RID: 355
		public static double[] phrase2_colimatorPoint13Avg = new double[2];

		// Token: 0x04000164 RID: 356
		public static double phrase2_colimatorDiff;

		// Token: 0x04000165 RID: 357
		public static double[] phrase3_colimatorPoint1 = new double[2];

		// Token: 0x04000166 RID: 358
		public static double[] phrase3_colimatorPoint2 = new double[2];

		// Token: 0x04000167 RID: 359
		public static double[] phrase3_colimatorPoint3 = new double[2];

		// Token: 0x04000168 RID: 360
		public static double[] phrase3_colimatorPoint13Avg = new double[2];

		// Token: 0x04000169 RID: 361
		public static double phrase3_colimatorDiff;

		// Token: 0x0400016A RID: 362
		public static double[] phrase4_colimatorPoint1 = new double[2];

		// Token: 0x0400016B RID: 363
		public static double[] phrase4_colimatorPoint2 = new double[2];

		// Token: 0x0400016C RID: 364
		public static double[] phrase4_colimatorPoint3 = new double[2];

		// Token: 0x0400016D RID: 365
		public static double[] phrase4_colimatorPoint13Avg = new double[2];

		// Token: 0x0400016E RID: 366
		public static double phrase4_colimatorDiff;

		// Token: 0x0400016F RID: 367
		public static string sn = "";

		// Token: 0x04000170 RID: 368
		public static string LenSN = "";

		// Token: 0x04000171 RID: 369
		public static double CompensationMirrorCenterX;

		// Token: 0x04000172 RID: 370
		public static double CompensationMirrorCenterY;

		// Token: 0x04000173 RID: 371
		public static double LensCenterX;

		// Token: 0x04000174 RID: 372
		public static double LensCenterY;

		// Token: 0x04000175 RID: 373
		public static double EdgeDistance;

		// Token: 0x04000176 RID: 374
		public static double CenterDistance;

		// Token: 0x04000177 RID: 375
		public static double Angle;

		// Token: 0x04000178 RID: 376
		public static double CompensationMirrorMaxResidual;

		// Token: 0x04000179 RID: 377
		public static double CompensationMirrorAverageResidual;

		// Token: 0x0400017A RID: 378
		public static double LensMaxResidual;

		// Token: 0x0400017B RID: 379
		public static double LensAverageResidual;

		// Token: 0x0400017C RID: 380
		public static string result;

		// Token: 0x0400017D RID: 381
		public static double[] distance = new double[4];

		// Token: 0x0400017E RID: 382
		public static double pressure_val;

		// Token: 0x0400017F RID: 383
		public static int link_press_z;

		// Token: 0x04000180 RID: 384
		public static int x_zero_positon;

		// Token: 0x04000181 RID: 385
		public static double vacuummeter_value;

		// Token: 0x04000182 RID: 386
		public static double p1_d;

		// Token: 0x04000183 RID: 387
		public static double p2_d;
	}
}
