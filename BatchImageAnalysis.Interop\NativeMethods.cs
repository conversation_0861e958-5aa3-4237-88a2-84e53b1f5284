using System;
using System.Runtime.InteropServices;

namespace BatchImageAnalysis.Interop
{
    /// <summary>
    /// Contains P/Invoke method signatures for BaslerController.dll.
    /// </summary>
    internal static unsafe class NativeMethods
    {
        private const string DllName = "BaslerController.dll";

        /// <summary>
        /// Initializes the support library.
        /// </summary>
        /// <param name="left_thresh">Left threshold value</param>
        /// <param name="right_thresh">Right threshold value</param>
        /// <param name="roi_x">ROI X coordinate</param>
        /// <param name="roi_y">ROI Y coordinate</param>
        /// <param name="roi_width">ROI width</param>
        /// <param name="left_x">Left X coordinate</param>
        /// <param name="right_x">Right X coordinate</param>
        /// <returns>An integer indicating success (0) or failure (non-zero).</returns>
        [DllImport(DllName, CallingConvention = CallingConvention.Cdecl)]
        public static extern int Init_support(int left_thresh, int right_thresh, int roi_x, int roi_y, int roi_width, int left_x, int right_x);

        /// <summary>
        /// Analyzes the provided image data.
        /// </summary>
        /// <param name="imageData">A pointer to the raw image data.</param>
        /// <param name="width">The width of the image.</param>
        /// <param name="height">The height of the image.</param>
        /// <returns>An integer result from the analysis.</returns>
        [DllImport(DllName, CallingConvention = CallingConvention.Cdecl)]
        public static extern int AnalysisSupport(IntPtr imageData, int width, int height);

        // Add other required P/Invoke signatures here as they are identified.
    }
}