using System;
using System.Runtime.InteropServices;

namespace BatchImageAnalysis.Interop
{
    /// <summary>
    /// Contains P/Invoke method signatures for BaslerController.dll.
    /// </summary>
    internal static unsafe class NativeMethods
    {
        private const string DllName = "BaslerController.dll";

        /// <summary>
        /// Initializes the support library.
        /// </summary>
        /// <param name="left_thresh">Left threshold value</param>
        /// <param name="right_thresh">Right threshold value</param>
        /// <param name="roi_x">ROI X coordinate</param>
        /// <param name="roi_y">ROI Y coordinate</param>
        /// <param name="roi_width">ROI width</param>
        /// <param name="left_x">Left X coordinate</param>
        /// <param name="right_x">Right X coordinate</param>
        /// <returns>An integer indicating success (0) or failure (non-zero).</returns>
        [DllImport(DllName, CallingConvention = CallingConvention.Cdecl)]
        public static extern int Init_support(int left_thresh, int right_thresh, int roi_x, int roi_y, int roi_width, int left_x, int right_x);

        /// <summary>
        /// Analyzes the support and generates processed image with auxiliary lines.
        /// </summary>
        /// <param name="type">Analysis type (0 for display original, 1 for analysis with lines)</param>
        /// <param name="result">Array to store analysis results</param>
        /// <returns>An integer result from the analysis (0 = success)</returns>
        [DllImport(DllName, CallingConvention = CallingConvention.Cdecl)]
        public static extern int AnalysisSupport(int type, double[] result);

        /// <summary>
        /// Captures an image from the camera.
        /// </summary>
        /// <returns>An integer result from the capture (0 = success)</returns>
        [DllImport(DllName, CallingConvention = CallingConvention.Cdecl)]
        public static extern int Capture();

        /// <summary>
        /// Sets the path of the image file to be processed.
        /// </summary>
        /// <param name="path">Path to the image file</param>
        /// <returns>An integer result (0 = success)</returns>
        [DllImport(DllName, CallingConvention = CallingConvention.Cdecl, CharSet = CharSet.Ansi)]
        public static extern int SetPicPath(string path);

        /// <summary>
        /// Registers a callback function to receive processed image data.
        /// </summary>
        /// <param name="callback">Callback function to handle image data</param>
        /// <returns>An integer result from the registration (0 = success)</returns>
        [DllImport(DllName, CallingConvention = CallingConvention.Cdecl)]
        public static extern int RegisterCallBackShowImage(ImageTransferCallback callback);

        /// <summary>
        /// Delegate for image transfer callback.
        /// </summary>
        /// <param name="data">Pointer to image data</param>
        /// <param name="count">Size of image data in bytes</param>
        [UnmanagedFunctionPointer(CallingConvention.StdCall, CharSet = CharSet.Ansi)]
        public delegate void ImageTransferCallback(IntPtr data, int count);

        // Windows API functions for direct memory manipulation
        [DllImport("kernel32.dll", SetLastError = true)]
        public static extern IntPtr GetModuleHandle(string lpModuleName);

        [DllImport("kernel32.dll", CharSet = CharSet.Ansi, ExactSpelling = true, SetLastError = true)]
        public static extern IntPtr GetProcAddress(IntPtr hModule, string procName);

        [DllImport("kernel32.dll", SetLastError = true)]
        public static extern bool WriteProcessMemory(IntPtr hProcess, IntPtr lpBaseAddress, byte[] lpBuffer, uint nSize, out UIntPtr lpNumberOfBytesWritten);

        [DllImport("kernel32.dll", SetLastError = true)]
        public static extern IntPtr GetCurrentProcess();

        // Add other required P/Invoke signatures here as they are identified.
    }
}