﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.Threading;
using System.Timers;
using System.Windows.Forms;

namespace Nreal_ProductLine_Tool.SUPPORT
{
	// Token: 0x02000018 RID: 24
	public partial class LensOp : FormDefault
	{
		// Token: 0x060000D1 RID: 209 RVA: 0x000126A0 File Offset: 0x000108A0
		public LensOp(Entrance entrance, int sequence, string title)
		{
			this.sequence = sequence;
			this.entrance = entrance;
			this.title = title;
			this.InitializeComponent();
			base.TopLevel = false;
			base.FormBorderStyle = FormBorderStyle.None;
			this.Dock = DockStyle.Fill;
			this.show_timer.Elapsed += this.Show_Timer_Elapsed;
			this.show_timer.Interval = 200.0;
			this.show_timer.Enabled = false;
			this.ax_operate_sem = new SemaphoreSlim(0, 1);
			Thread thread = new Thread(new ThreadStart(this.ax_operate));
			thread.Start();
		}

		// Token: 0x060000D2 RID: 210 RVA: 0x00012798 File Offset: 0x00010998
		public override string GetName()
		{
			return this.title;
		}

		// Token: 0x060000D3 RID: 211 RVA: 0x000127B0 File Offset: 0x000109B0
		public override void Start()
		{
			this.show_timer.Enabled = true;
			this.timer_stop = false;
			this.point2_x_label.BackColor = this.gray;
			this.point2_y_label.BackColor = this.gray;
			this.point2_x_label.Text = "";
			this.point2_y_label.Text = "";
			this.move_x_btn.BackColor = this.gray;
			Colimator.serialPort = Colimator.serialPort2;
			Colimator.TurnOnColimator();
			base.Show();
		}

		// Token: 0x060000D4 RID: 212 RVA: 0x00012841 File Offset: 0x00010A41
		public override void ExcepStop()
		{
			this.show_timer.Enabled = false;
			Colimator.TurnOffColimator();
		}

		// Token: 0x060000D5 RID: 213 RVA: 0x00012858 File Offset: 0x00010A58
		private void Z_press_btn_Click(object sender, EventArgs e)
		{
			this.Z_press_btn.Enabled = false;
			this.Z_press_btn.BackColor = Color.Gray;
			this.ax_type = "2";
			this.pulse = Configure.zDest1;
			this.ax_operate_sem.Release();
		}

		// Token: 0x060000D6 RID: 214 RVA: 0x000128A8 File Offset: 0x00010AA8
		private void Show_Timer_Elapsed(object sender, ElapsedEventArgs e)
		{
			bool flag = !this.timer_stop;
			if (!flag)
			{
				this.show_timer.Enabled = false;
			}
		}

		// Token: 0x060000D7 RID: 215 RVA: 0x000128D8 File Offset: 0x00010AD8
		private void move_x_btn_Click(object sender, EventArgs e)
		{
			this.show_timer.Enabled = false;
			this.timer_stop = true;
			this.move_x_btn.Enabled = false;
			this.ax_type = "1";
			this.pulse = Configure.yDest1;
			this.ax_operate_sem.Release();
		}

		// Token: 0x060000D8 RID: 216 RVA: 0x00012929 File Offset: 0x00010B29
		private void Link_finish_btn_Click(object sender, EventArgs e)
		{
			this.show_timer.Enabled = false;
			Colimator.TurnOffColimator();
			this.entrance.Finish(this.sequence, 0);
		}

		// Token: 0x060000D9 RID: 217 RVA: 0x00012954 File Offset: 0x00010B54
		private void last_step_btn_Click(object sender, EventArgs e)
		{
			this.show_timer.Enabled = false;
			Colimator.TurnOffColimator();
			GlobalData.sn_toujing_scan = GlobalData.sn_toujing_confirm;
			GlobalData.sn_toujing_confirm = "";
			base.Hide();
			this.entrance.Finish(this.sequence - 3, 0);
		}

		// Token: 0x060000DA RID: 218 RVA: 0x000129A8 File Offset: 0x00010BA8
		private void ax_operate()
		{
			while (!this.isExited)
			{
				this.ax_operate_sem.Wait();
				int i;
				for (i = 0; i < 10; i++)
				{
					bool flag = !MotionController.running;
					if (flag)
					{
						break;
					}
					Thread.Sleep(200);
				}
				bool flag2 = i == 10;
				if (flag2)
				{
					this.Z_press_btn.Enabled = true;
					this.move_x_btn.Enabled = true;
					MessageBox.Show("当前电机繁忙，请确认重试");
				}
				else
				{
					MotionController.running = true;
					GSC_Controller.MoveAbs(this.ax_type, this.pulse);
					MotionController.running = false;
					this.Z_press_btn.Invoke(new MethodInvoker(delegate()
					{
						bool flag3 = this.ax_type == "2";
						if (flag3)
						{
							this.Z_press_btn.Enabled = true;
							this.Z_press_btn.BackColor = Color.Green;
						}
						else
						{
							this.move_x_btn.Enabled = true;
							this.move_x_btn.BackColor = this.pass;
							this.Link_finish_btn.Enabled = true;
						}
					}));
				}
			}
		}

		// Token: 0x0400023C RID: 572
		private Color fail = Color.OrangeRed;

		// Token: 0x0400023D RID: 573
		private Color pass = Color.Green;

		// Token: 0x0400023E RID: 574
		private Color gray = Color.Gray;

		// Token: 0x0400023F RID: 575
		private bool timer_stop = false;

		// Token: 0x04000240 RID: 576
		private Entrance entrance;

		// Token: 0x04000241 RID: 577
		private int sequence;

		// Token: 0x04000242 RID: 578
		private int type;

		// Token: 0x04000243 RID: 579
		public string title;

		// Token: 0x04000244 RID: 580
		protected SemaphoreSlim ax_operate_sem;

		// Token: 0x04000245 RID: 581
		private bool isExited = false;

		// Token: 0x04000246 RID: 582
		private Timer show_timer = new Timer();

		// Token: 0x04000247 RID: 583
		private string ax_type = "1";

		// Token: 0x04000248 RID: 584
		private int pulse;
	}
}
