﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Reflection;
using System.Runtime.InteropServices;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Timers;
using System.Windows.Forms;
using ParamManager;

namespace Nreal_ProductLine_Tool.SUPPORT
{
	// Token: 0x0200001C RID: 28
	public partial class main : Form
	{
		// Token: 0x060000F9 RID: 249 RVA: 0x0001420C File Offset: 0x0001240C
		public main()
		{
			base.FormBorderStyle = FormBorderStyle.FixedDialog;
			base.FormClosed += this.FrmMain_FormClosed;
			Logs logs = new Logs();
			Logs.WriteInfo("\r\n\r\n", true);
			Logs.WriteInfo("################ start ################", true);
			int num = this.system_init();
			bool flag = num != 0;
			if (flag)
			{
				MessageBox.Show("系统初始化失败");
				Logs.WriteError("系统初始化失败", true);
			}
			else
			{
				double num2 = Math.Atan(0.08) * 180.0 / 3.141592653589793;
				Control.CheckForIllegalCrossThreadCalls = false;
				this.InitializeComponent();
				this.disableUiFunc();
				this.Text = string.Concat(new string[]
				{
					LOGINFO.rname,
					" ",
					LOGINFO.pname,
					" Version:",
					Assembly.GetExecutingAssembly().GetName().Version.ToString(),
					" 线号：",
					LOGINFO.lname
				});
				base.Load += this.MainForm_Load;
				int num3 = Distance.Init();
				bool flag2 = num3 != 0;
				if (flag2)
				{
					MessageBox.Show("位移传感器连接失败");
				}
				else
				{
					this.imgTransfer = new main.ImgTransferCallback(this.imgTransferCallback);
					bool flag3 = Configure.left_thresh == 0;
					if (flag3)
					{
						Configure.left_thresh = 100;
					}
					bool flag4 = Configure.right_thresh == 0;
					if (flag4)
					{
						Configure.right_thresh = 130;
					}
					num = CameraImageLib.Init_support(Configure.left_thresh, Configure.right_thresh, Configure.roi_x, Configure.roi_y, Configure.roi_width);
					CameraImageLib.RegisterCallBackShowImage(this.imgTransfer);
					bool flag5 = num != 0;
					if (flag5)
					{
						MessageBox.Show("相机初始化失败 " + num.ToString());
					}
					else
					{
						CameraImageLib.RegisterCallBackShowImage(this.imgTransfer);
						bool flag6 = Configure.camera_exposure_time < 100;
						if (flag6)
						{
							MessageBox.Show("曝光时间设置过短，请重新设置");
						}
						else
						{
							CameraImageLib.SetExposureTime((double)Configure.camera_exposure_time);
							string text = Path.Combine(Environment.CurrentDirectory, "pic");
							bool flag7 = !Directory.Exists(text);
							if (flag7)
							{
								Directory.CreateDirectory(text);
							}
							this.message_sem = new SemaphoreSlim(0, 1);
							Thread thread = new Thread(new ThreadStart(this.capture_analyze));
							thread.Start();
							this.pressure_operate_sem = new SemaphoreSlim(0, 1);
							Thread thread2 = new Thread(new ThreadStart(this.pressure_operate));
							thread2.Start();
							this.pressure_value_sem = new SemaphoreSlim(0, 1);
							Thread thread3 = new Thread(new ThreadStart(this.pressure_value_operate));
							thread3.Start();
							this.vacum_value_sem = new SemaphoreSlim(0, 1);
							Thread thread4 = new Thread(new ThreadStart(this.vacum_value_operate));
							thread4.Start();
							this.uv_timer.Elapsed += this.UV_Timer_Elapsed;
							bool flag8 = Configure.uv_delay_time < 10;
							if (flag8)
							{
								Configure.uv_delay_time = 5000;
								MessageBox.Show("uv_delay_time 配置不对");
							}
							this.uv_timer.Interval = (double)Configure.uv_delay_time;
							this.uv_timer.Enabled = false;
						}
					}
				}
			}
		}

		// Token: 0x060000FA RID: 250 RVA: 0x000145B8 File Offset: 0x000127B8
		private int system_init()
		{
			int num = XrPLCom.xrCommInit(1, 0, Encoding.UTF8.GetBytes(LOGINFO.user));
			bool flag = num != 0;
			if (flag)
			{
				MessageBox.Show("请检查网络 ");
			}
			bool flag2 = LOGINFO.mode == "online" || (LOGINFO.mode == "offline" && LOGINFO.configMode == "net");
			if (flag2)
			{
				Configure.Init();
			}
			else
			{
				num = Configure.Init_Local();
				bool flag3 = num != 0;
				if (flag3)
				{
					MessageBox.Show("请检查本地配置文件 sys.ini ");
					Process.GetCurrentProcess().Kill();
				}
			}
			bool flag4 = this.mesEnable == 1;
			if (flag4)
			{
				num = this.mesOperate();
				bool flag5 = num != 0;
				if (flag5)
				{
					Process.GetCurrentProcess().Kill();
				}
			}
			bool flag6 = LOGINFO.scan == "true";
			if (flag6)
			{
				Thread thread = new Thread(new ThreadStart(this.test_Execute));
				thread.Start();
			}
			return 0;
		}

		// Token: 0x060000FB RID: 251 RVA: 0x000146C8 File Offset: 0x000128C8
		private void MainForm_Load(object sender, EventArgs e)
		{
			base.WindowState = FormWindowState.Maximized;
			this.groupBox1.Anchor = (AnchorStyles.Top | AnchorStyles.Right);
			this.groupBox1.Location = new Point(base.ClientSize.Width - this.groupBox1.Width - 20, 20);
			this.pictureBox1.Location = new Point(20, 20);
			this.pictureBox1.Size = new Size(base.ClientSize.Width - this.groupBox1.Width - 60, base.ClientSize.Height - 60);
		}

		// Token: 0x060000FC RID: 252 RVA: 0x00014770 File Offset: 0x00012970
		private void FrmMain_FormClosed(object sender, FormClosedEventArgs e)
		{
			Process.GetCurrentProcess().Kill();
		}

		// Token: 0x060000FD RID: 253 RVA: 0x00014780 File Offset: 0x00012980
		public void imgTransferCallback(IntPtr data, int count)
		{
			TimeSpan timeSpan = new TimeSpan(DateTime.Now.Ticks);
			byte[] array = new byte[count];
			Marshal.Copy(data, array, 0, count);
			MemoryStream stream = new MemoryStream(array);
			this.capture_times++;
			Image bitMap = Image.FromStream(stream);
			bool flag = this.capture_times % 600 == 0 || this.capture_times % 601 == 0;
			if (flag)
			{
				DateTime now = DateTime.Now;
				string str = now.ToString("yyyy_MM_dd_HH_mm_ss_");
				string str2 = now.Millisecond.ToString("D3");
				string path = str + str2 + ".jpg";
				string filename = Path.Combine("pic", path);
				bitMap.Save(filename);
			}
			this.pictureBox1.BeginInvoke(new MethodInvoker(delegate()
			{
				this.pictureBox1.Image = bitMap;
			}));
		}

		// Token: 0x060000FE RID: 254 RVA: 0x0001487C File Offset: 0x00012A7C
		private void test_Execute()
		{
			byte[] array = new byte[1024];
			byte[] array2 = new byte[128];
			Logs.WriteInfo("test_Execute", true);
			while (!this.isExited)
			{
				int count = 1024;
				int num = 128;
				Logs.WriteInfo("begin", true);
				int num2 = XrPLCom.SnWaitNotify(array, ref count);
				Logs.WriteInfo("SnWaitNotify ret " + num2.ToString(), true);
				bool flag = num2 != 0;
				if (flag)
				{
					base.TopMost = true;
					base.WindowState = FormWindowState.Normal;
					Logs.WriteError("SnWaitNotify error, ret " + num2.ToString(), true);
					MessageBox.Show("请重新扫码或联系管理员");
					base.WindowState = FormWindowState.Minimized;
					XrPLCom.TestAbortUpload(array2, ref num);
				}
				else
				{
					string @string = Encoding.Default.GetString(array, 0, count);
					Logs.WriteInfo("snInfo " + @string, true);
					try
					{
						Context context = JsonSerializer.Deserialize<Context>(@string, null);
						bool flag2 = context == null;
						if (flag2)
						{
							base.TopMost = true;
							base.WindowState = FormWindowState.Normal;
							Logs.WriteError("json字符串错误 " + @string, true);
							MessageBox.Show("请重新扫码或联系管理员");
							base.WindowState = FormWindowState.Minimized;
							XrPLCom.TestAbortUpload(array2, ref num);
						}
						else
						{
							this.snRemote = context.SN;
							Logs.WriteInfo("sn " + this.snRemote, true);
							bool flag3 = this.snRemote.Length != Configure.sn_length;
							if (flag3)
							{
								base.TopMost = true;
								base.WindowState = FormWindowState.Normal;
								Logs.WriteError("sn码长度错误,实际长度 " + this.snRemote.Length.ToString() + " 规格" + Configure.sn_length.ToString(), true);
								MessageBox.Show("请输入正确的SN码");
								base.WindowState = FormWindowState.Minimized;
								XrPLCom.TestAbortUpload(array2, ref num);
							}
							else
							{
								Logs.WriteInfo("db", true);
								this.isStopCapture = false;
								bool flag4 = this.message_sem.CurrentCount == 1;
								if (flag4)
								{
									Logs.WriteError("message_sem error", true);
								}
								else
								{
									this.message_sem.Release();
								}
							}
						}
					}
					catch (Exception ex)
					{
						Logs.WriteError("Exception:" + ex.Message, true);
						break;
					}
				}
			}
		}

		// Token: 0x060000FF RID: 255 RVA: 0x00014AF4 File Offset: 0x00012CF4
		private void capture_analyze()
		{
			while (!this.isExited)
			{
				this.trigger = 0;
				this.message_sem.Wait();
				while (!this.isStopCapture)
				{
					int ret = CameraImageLib.Capture();
					bool flag = ret == 0;
					if (flag)
					{
						ret = CameraImageLib.AnalysisSupport(this.show_flag, this.para);
						base.Invoke(new MethodInvoker(delegate()
						{
							bool flag2 = ret == 0;
							if (flag2)
							{
								this.label17.Text = "分析成功";
							}
							else
							{
								this.label17.Text = "分析失败";
								this.label17.BackColor = Color.Red;
							}
							GlobalData.CompensationMirrorCenterX = this.para[3];
							GlobalData.CompensationMirrorCenterY = this.para[4];
							GlobalData.LensCenterX = this.para[5];
							GlobalData.LensCenterY = this.para[6];
							GlobalData.EdgeDistance = this.para[0];
							GlobalData.CenterDistance = this.para[2];
							GlobalData.Angle = this.para[1];
							GlobalData.CompensationMirrorMaxResidual = this.para[7];
							GlobalData.CompensationMirrorAverageResidual = this.para[8];
							GlobalData.LensMaxResidual = this.para[9];
							GlobalData.LensAverageResidual = this.para[10];
							this.label6.Text = (this.para[0].ToString() ?? "");
							this.label4.Text = (this.para[1].ToString() ?? "");
							this.label8.Text = (this.para[2].ToString() ?? "");
							this.label11.Text = (this.para[3].ToString() ?? "");
							this.label13.Text = (this.para[5].ToString() ?? "");
							this.label15.Text = (this.para[4].ToString() ?? "");
							this.label16.Text = (this.para[6].ToString() ?? "");
							this.label21.Text = (this.para[7].ToString() ?? "");
							this.label23.Text = (this.para[8].ToString() ?? "");
							this.label25.Text = (this.para[9].ToString() ?? "");
							this.label27.Text = (this.para[10].ToString() ?? "");
						}));
					}
					Thread.Sleep(50);
				}
			}
		}

		// Token: 0x06000100 RID: 256 RVA: 0x00014B9C File Offset: 0x00012D9C
		private void pressure_operate()
		{
			while (!this.isExited)
			{
				this.pressure_operate_sem.Wait();
				Logs.WriteInfo("phrase:" + this.phrase.ToString(), true);
				bool flag = this.phrase == 2;
				if (flag)
				{
					MotionController.SetSpeedPressure();
					GSC_Controller.MoveAbs("2", Configure.zDest2);
					MotionController.SetSpeedHigh();
					this.button8.Invoke(new MethodInvoker(delegate()
					{
						this.button8.Enabled = true;
						this.button8.BackColor = Color.Green;
					}));
				}
				bool flag2 = this.phrase == 1;
				if (flag2)
				{
					MotionController.SetSpeed();
					GSC_Controller.MoveAbs("2", Configure.zDest3);
					this.button25.Invoke(new MethodInvoker(delegate()
					{
						this.button25.Enabled = true;
						this.button25.BackColor = Color.Green;
					}));
				}
			}
		}

		// Token: 0x06000101 RID: 257 RVA: 0x00014C6C File Offset: 0x00012E6C
		private void vacum_value_operate()
		{
			if (this.isExited)
			{
				return;
			}
			Logs.WriteInfo("vacum_value_operate", true);
			this.vacum_value_sem.Wait();
			for (;;)
			{
				double value = 0.0;
				Vacuummeter.getVacuummeter(ref value);
				this.label18.Invoke(new MethodInvoker(delegate()
				{
					this.label18.Text = (value.ToString() ?? "");
				}));
				Thread.Sleep(150);
			}
		}

		// Token: 0x06000102 RID: 258 RVA: 0x00014CF8 File Offset: 0x00012EF8
		private void pressure_value_operate()
		{
			if (this.isExited)
			{
				return;
			}
			Logs.WriteInfo("pressure_value_operate", true);
			this.pressure_value_sem.Wait();
			for (;;)
			{
				double value = 0.0;
				PressSensor.getPressValue(ref value);
				this.label19.Invoke(new MethodInvoker(delegate()
				{
					this.label19.Text = (value.ToString() ?? "");
				}));
				Thread.Sleep(150);
			}
		}

		// Token: 0x06000103 RID: 259 RVA: 0x00014D81 File Offset: 0x00012F81
		private void UV_Timer_Elapsed(object sender, ElapsedEventArgs e)
		{
			this.uv_timer.Stop();
			Logs.WriteInfo("UV_Timer_Elapsed:", true);
			ZMotionController.StopUV();
			Thread.Sleep(100);
			ZMotionController.StartUV();
			Thread.Sleep(100);
			ZMotionController.StopUV();
		}

		// Token: 0x06000104 RID: 260 RVA: 0x00014DC0 File Offset: 0x00012FC0
		public int mesOperate()
		{
			byte[] array = new byte[128];
			int count = array.Length;
			int num = XrPLCom.MesLinkCreate(array, ref count);
			bool flag = num != 0;
			int result;
			if (flag)
			{
				MessageBox.Show("MES建立连接失败，检查MES服务 ");
				Logs.WriteError("MES建立连接失败，ret " + num.ToString(), true);
				result = -1;
			}
			else
			{
				string text = string.Concat(new string[]
				{
					"select LeftResource, RightResource from mes_resource where StationID=\"",
					LOGINFO.rname,
					"\" and StationNo=\"",
					LOGINFO.lname,
					"\""
				});
				DataSet dataSet = new DataSet();
				VersionHandle.GetDbData(text, ref dataSet);
				bool flag2 = dataSet.Tables[0].Rows.Count < 1;
				if (flag2)
				{
					MessageBox.Show("MES资源不存在 ");
					Logs.WriteError("MES资源不存在 ", true);
					result = -2;
				}
				else
				{
					this.mes_resource.Add("L", dataSet.Tables[0].Rows[0]["LeftResource"].ToString());
					this.mes_resource.Add("R", dataSet.Tables[0].Rows[0]["RightResource"].ToString());
					Logs.WriteInfo(this.mes_resource["L"], true);
					Logs.WriteInfo(this.mes_resource["R"], true);
					count = array.Length;
					num = XrPLCom.MesLogIn(Encoding.UTF8.GetBytes(this.mes_resource["L"]), Encoding.UTF8.GetBytes(LOGINFO.user), Encoding.UTF8.GetBytes(LOGINFO.pass), array, ref count);
					string @string = Encoding.Default.GetString(array, 0, count);
					bool flag3 = num != 0 && @string.IndexOf("已登录") < 0;
					if (flag3)
					{
						MessageBox.Show("MES登录失败，失败原因 " + @string);
						Logs.WriteError("MES登录失败，失败原因 " + @string, true);
						result = -3;
					}
					else
					{
						result = 0;
					}
				}
			}
			return result;
		}

		// Token: 0x06000105 RID: 261 RVA: 0x00014FE1 File Offset: 0x000131E1
		public void enableUiFunc()
		{
		}

		// Token: 0x06000106 RID: 262 RVA: 0x00014FE4 File Offset: 0x000131E4
		public void disableUiFunc()
		{
		}

		// Token: 0x06000107 RID: 263 RVA: 0x00014FE8 File Offset: 0x000131E8
		public void errMsgShow(string message)
		{
			base.Invoke(new MethodInvoker(delegate()
			{
				MessageBox.Show(message);
			}));
		}

		// Token: 0x06000108 RID: 264 RVA: 0x00015016 File Offset: 0x00013216
		public void comWrite(string value)
		{
			this.com_val = value;
		}

		// Token: 0x06000109 RID: 265 RVA: 0x00015020 File Offset: 0x00013220
		public void messageShow(string message)
		{
		}

		// Token: 0x0600010A RID: 266 RVA: 0x00015023 File Offset: 0x00013223
		public void serialStatisticShow(ulong recvBytes, ulong sendBytes, ulong recvPackets, ulong sendPackets)
		{
			base.BeginInvoke(new MethodInvoker(delegate()
			{
			}));
		}

		// Token: 0x0600010B RID: 267 RVA: 0x0001504C File Offset: 0x0001324C
		public void timeValWrite(string val)
		{
		}

		// Token: 0x0600010C RID: 268 RVA: 0x0001504F File Offset: 0x0001324F
		public void productInfoWrite(string ipu, string sensor)
		{
		}

		// Token: 0x0600010D RID: 269 RVA: 0x00015052 File Offset: 0x00013252
		private void button1_Click(object sender, EventArgs e)
		{
		}

		// Token: 0x0600010E RID: 270 RVA: 0x00015058 File Offset: 0x00013258
		private void button3_Click(object sender, EventArgs e)
		{
			bool flag = this.begin_or_reset;
			if (flag)
			{
				this.begin_or_reset = false;
			}
			else
			{
				double num = 0.0;
				bool flag2 = num > this.previous_val || num + 1.0 < this.previous_val;
				if (flag2)
				{
					MessageBox.Show("当前胶重与上次胶重对比异常");
				}
				this.previous_val = num;
			}
		}

		// Token: 0x0600010F RID: 271 RVA: 0x000150BE File Offset: 0x000132BE
		private void button2_Click_1(object sender, EventArgs e)
		{
			this.begin_or_reset = true;
		}

		// Token: 0x06000110 RID: 272 RVA: 0x000150C8 File Offset: 0x000132C8
		private void button1_Click_1(object sender, EventArgs e)
		{
			byte[] array = new byte[128];
			this.isStopCapture = true;
			bool flag = LOGINFO.dbWrite == "true";
			if (flag)
			{
				string text = string.Concat(new string[]
				{
					"insert into g_small_prism_bonding(SN,Project,Station, Result, user) values (\"",
					this.snRemote,
					"\",\"",
					LOGINFO.pname,
					"\",\"",
					LOGINFO.lname,
					"\",\"True\",\"",
					LOGINFO.user,
					"\")"
				});
				int num = XrPLCom.excuteGinaDb(LOGINFO.mode, LOGINFO.dbWrite, text);
				bool flag2 = num != 0;
				if (flag2)
				{
					base.TopMost = true;
					base.WindowState = FormWindowState.Normal;
					Logs.WriteInfo("excuteDb " + num.ToString(), true);
					MessageBox.Show("数据库写入失败");
					base.WindowState = FormWindowState.Minimized;
				}
			}
			XrPLCom.UploadData(array, null);
			string str = DateTime.Now.ToString("yyyy_M_d_HH_mm_ss") + "_" + this.snRemote + ".jpg";
			string text2 = Path.Combine(Environment.CurrentDirectory, "image/" + DateTime.Now.ToString("yyyy_M_d"));
			bool flag3 = !Directory.Exists(text2);
			if (flag3)
			{
				Directory.CreateDirectory(text2);
			}
			text2 += "/";
			this.pictureBox1.Image.Save(text2 + str);
			XrPLCom.UploadData(array, null);
			this.dataClear();
		}

		// Token: 0x06000111 RID: 273 RVA: 0x00015258 File Offset: 0x00013458
		private void dataClear()
		{
			this.para[0] = 0.0;
			this.para[1] = 0.0;
			this.para[2] = 0.0;
			this.para[3] = 0.0;
			this.pictureBox1.Image = null;
		}

		// Token: 0x06000112 RID: 274 RVA: 0x000152B7 File Offset: 0x000134B7
		private void button1_Click_2(object sender, EventArgs e)
		{
			CameraImageLib.Capture();
		}

		// Token: 0x06000113 RID: 275 RVA: 0x000152C0 File Offset: 0x000134C0
		private void button2_Click(object sender, EventArgs e)
		{
			GSC_Controller.Home("1", "-");
		}

		// Token: 0x06000114 RID: 276 RVA: 0x000152D3 File Offset: 0x000134D3
		private void button3_Click_1(object sender, EventArgs e)
		{
			GSC_Controller.MoveAbs("1", Configure.yDest1);
		}

		// Token: 0x06000115 RID: 277 RVA: 0x000152E6 File Offset: 0x000134E6
		private void button4_Click(object sender, EventArgs e)
		{
			GSC_Controller.MoveAbs("1", Configure.yDest2);
		}

		// Token: 0x06000116 RID: 278 RVA: 0x000152F9 File Offset: 0x000134F9
		private void button5_Click(object sender, EventArgs e)
		{
			GSC_Controller.Stop("1");
		}

		// Token: 0x06000117 RID: 279 RVA: 0x00015307 File Offset: 0x00013507
		private void button6_Click(object sender, EventArgs e)
		{
			GSC_Controller.Home("2", "+");
		}

		// Token: 0x06000118 RID: 280 RVA: 0x0001531A File Offset: 0x0001351A
		private void button7_Click(object sender, EventArgs e)
		{
			GSC_Controller.MoveAbs("2", Configure.zDest1);
		}

		// Token: 0x06000119 RID: 281 RVA: 0x0001532D File Offset: 0x0001352D
		private void button8_Click(object sender, EventArgs e)
		{
			this.button8.BackColor = Color.Gray;
			this.button8.Enabled = false;
			this.phrase = 2;
			this.pressure_operate_sem.Release();
		}

		// Token: 0x0600011A RID: 282 RVA: 0x00015361 File Offset: 0x00013561
		private void button9_Click(object sender, EventArgs e)
		{
			GSC_Controller.Stop("2");
		}

		// Token: 0x0600011B RID: 283 RVA: 0x0001536F File Offset: 0x0001356F
		private void button10_Click(object sender, EventArgs e)
		{
			this.isStopCapture = false;
			this.message_sem.Release();
		}

		// Token: 0x0600011C RID: 284 RVA: 0x00015385 File Offset: 0x00013585
		private void button11_Click(object sender, EventArgs e)
		{
			this.isStopCapture = true;
		}

		// Token: 0x0600011D RID: 285 RVA: 0x0001538F File Offset: 0x0001358F
		private void button12_Click(object sender, EventArgs e)
		{
			GSC_Controller.Home("w", "-+");
			Db.dataSaveIntoDB();
		}

		// Token: 0x0600011E RID: 286 RVA: 0x000153A8 File Offset: 0x000135A8
		private void button15_Click(object sender, EventArgs e)
		{
			TimeSpan t = new TimeSpan(DateTime.Now.Ticks);
			int num = CameraImageLib.AnalysisSupport(this.show_flag, this.para);
			TimeSpan t2 = new TimeSpan(DateTime.Now.Ticks);
			Console.WriteLine(string.Format("ana程序执行时间：{0} 毫秒", (t2 - t).TotalMilliseconds));
			Logs.WriteInfo("分析结果:" + num.ToString(), true);
			bool flag = num == 0;
			if (flag)
			{
				this.label17.Text = "分析成功";
			}
			else
			{
				this.label17.Text = "分析失败";
				this.label17.BackColor = Color.Red;
			}
			this.label6.Text = (this.para[0].ToString() ?? "");
			this.label4.Text = (this.para[1].ToString() ?? "");
			this.label8.Text = (this.para[2].ToString() ?? "");
			this.label11.Text = (this.para[3].ToString() ?? "");
			this.label13.Text = (this.para[5].ToString() ?? "");
			this.label15.Text = (this.para[4].ToString() ?? "");
			this.label16.Text = (this.para[6].ToString() ?? "");
		}

		// Token: 0x0600011F RID: 287 RVA: 0x00015579 File Offset: 0x00013779
		private void button13_Click(object sender, EventArgs e)
		{
			this.button13.Enabled = false;
			this.vacum_value_sem.Release();
		}

		// Token: 0x06000120 RID: 288 RVA: 0x00015595 File Offset: 0x00013795
		private void button14_Click(object sender, EventArgs e)
		{
			this.button14.Enabled = false;
			this.pressure_value_sem.Release();
		}

		// Token: 0x06000121 RID: 289 RVA: 0x000155B1 File Offset: 0x000137B1
		private void button16_Click(object sender, EventArgs e)
		{
			ZMotionController.GasReleaseOpen();
		}

		// Token: 0x06000122 RID: 290 RVA: 0x000155BA File Offset: 0x000137BA
		private void button17_Click(object sender, EventArgs e)
		{
			ZMotionController.VacuummeterPumpOpen();
		}

		// Token: 0x06000123 RID: 291 RVA: 0x000155C3 File Offset: 0x000137C3
		private void button18_Click(object sender, EventArgs e)
		{
			ZMotionController.VacuummeterPumpClose();
		}

		// Token: 0x06000124 RID: 292 RVA: 0x000155CC File Offset: 0x000137CC
		private void button19_Click(object sender, EventArgs e)
		{
			ZMotionController.VacuummeterGasOutOpen();
		}

		// Token: 0x06000125 RID: 293 RVA: 0x000155D8 File Offset: 0x000137D8
		private void button20_Click(object sender, EventArgs e)
		{
			Logs.WriteInfo("startUV:" + ZMotionController.StartUV().ToString(), true);
			this.uv_timer.Start();
		}

		// Token: 0x06000126 RID: 294 RVA: 0x00015610 File Offset: 0x00013810
		private void button21_Click(object sender, EventArgs e)
		{
			Logs.WriteInfo("stopUV:" + ZMotionController.StopUV().ToString(), true);
		}

		// Token: 0x06000127 RID: 295 RVA: 0x0001563C File Offset: 0x0001383C
		private void button22_Click(object sender, EventArgs e)
		{
			bool flag = this.button22.Text == "显示辅助线";
			if (flag)
			{
				this.show_flag = 1;
				this.button22.Text = "取消辅助线";
			}
			else
			{
				bool flag2 = this.button22.Text == "取消辅助线";
				if (flag2)
				{
					this.show_flag = 0;
					this.button22.Text = "显示辅助线";
				}
			}
		}

		// Token: 0x06000128 RID: 296 RVA: 0x000156B2 File Offset: 0x000138B2
		private void button23_Click(object sender, EventArgs e)
		{
			ZMotionController.GasReleaseClose();
		}

		// Token: 0x06000129 RID: 297 RVA: 0x000156BB File Offset: 0x000138BB
		private void button24_Click(object sender, EventArgs e)
		{
			ZMotionController.VacuummeterGasOutClose();
		}

		// Token: 0x0600012A RID: 298 RVA: 0x000156C4 File Offset: 0x000138C4
		private void button25_Click(object sender, EventArgs e)
		{
			this.button25.BackColor = Color.Gray;
			this.button25.Enabled = false;
			this.phrase = 1;
			this.pressure_operate_sem.Release();
		}

		// Token: 0x0600012B RID: 299 RVA: 0x000156F8 File Offset: 0x000138F8
		private void button26_Click(object sender, EventArgs e)
		{
			this.label28.Text = (GlobalData.distance[0].ToString() ?? "");
		}

		// Token: 0x04000272 RID: 626
		private int mesEnable = 0;

		// Token: 0x04000273 RID: 627
		public const int WM_CLOSE = 16;

		// Token: 0x04000274 RID: 628
		private Form objform;

		// Token: 0x04000275 RID: 629
		private Label portDetectLabel;

		// Token: 0x04000276 RID: 630
		private string com_val;

		// Token: 0x04000277 RID: 631
		private int band_rate;

		// Token: 0x04000278 RID: 632
		private bool begin_or_reset = true;

		// Token: 0x04000279 RID: 633
		private double previous_val = 0.0;

		// Token: 0x0400027A RID: 634
		private bool isExited = false;

		// Token: 0x0400027B RID: 635
		private string snRemote;

		// Token: 0x0400027C RID: 636
		private string resMes;

		// Token: 0x0400027D RID: 637
		private bool isStopCapture = false;

		// Token: 0x0400027E RID: 638
		protected SemaphoreSlim message_sem;

		// Token: 0x0400027F RID: 639
		protected SemaphoreSlim pressure_operate_sem;

		// Token: 0x04000280 RID: 640
		protected SemaphoreSlim pressure_value_sem;

		// Token: 0x04000281 RID: 641
		protected SemaphoreSlim vacum_value_sem;

		// Token: 0x04000282 RID: 642
		private main.ImgTransferCallback imgTransfer = null;

		// Token: 0x04000283 RID: 643
		private double[] para = new double[11];

		// Token: 0x04000284 RID: 644
		private int trigger = 0;

		// Token: 0x04000285 RID: 645
		private int capture_times = 0;

		// Token: 0x04000286 RID: 646
		private int show_flag = 0;

		// Token: 0x04000287 RID: 647
		private Timer uv_timer = new Timer();

		// Token: 0x04000288 RID: 648
		private int pulse;

		// Token: 0x04000289 RID: 649
		private int speed;

		// Token: 0x0400028A RID: 650
		private int phrase = 0;

		// Token: 0x0400028B RID: 651
		private Dictionary<string, string> mes_resource = new Dictionary<string, string>();

		// Token: 0x02000032 RID: 50
		// (Invoke) Token: 0x060002E4 RID: 740
		[UnmanagedFunctionPointer(CallingConvention.StdCall, CharSet = CharSet.Ansi)]
		public delegate void ImgTransferCallback(IntPtr data, int count);
	}
}
