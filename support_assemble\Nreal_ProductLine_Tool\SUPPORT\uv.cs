﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.Threading;
using System.Windows.Forms;

namespace Nreal_ProductLine_Tool.SUPPORT
{
	// Token: 0x02000025 RID: 37
	public partial class uv : FormDefault
	{
		// Token: 0x060002C3 RID: 707 RVA: 0x0001843C File Offset: 0x0001663C
		public uv(Entrance entrance, int sequence, string title)
		{
			this.sequence = sequence;
			this.entrance = entrance;
			this.title = title;
			this.InitializeComponent();
			base.TopLevel = false;
			base.FormBorderStyle = FormBorderStyle.None;
			this.Dock = DockStyle.Fill;
			this.uv_operate_sem = new SemaphoreSlim(0, 1);
			Thread thread = new Thread(new ThreadStart(this.uv_operate));
			thread.Start();
		}

		// Token: 0x060002C4 RID: 708 RVA: 0x000184B4 File Offset: 0x000166B4
		public override string GetName()
		{
			return this.title;
		}

		// Token: 0x060002C5 RID: 709 RVA: 0x000184CC File Offset: 0x000166CC
		public override void Start()
		{
			base.Show();
		}

		// Token: 0x060002C6 RID: 710 RVA: 0x000184D8 File Offset: 0x000166D8
		private void uv_operate()
		{
			while (!uv.isExited)
			{
				this.uv_operate_sem.Wait();
				Thread.Sleep(Configure.uv_delay_time);
				ZMotionController.StartUV();
				Thread.Sleep(Configure.uv_operate_time);
				ZMotionController.StopUV();
				Thread.Sleep(100);
				ZMotionController.StartUV();
				Thread.Sleep(100);
				ZMotionController.StopUV();
			}
		}

		// Token: 0x040002DB RID: 731
		public static bool isExited;

		// Token: 0x040002DC RID: 732
		private Entrance entrance;

		// Token: 0x040002DD RID: 733
		private int sequence;

		// Token: 0x040002DE RID: 734
		private int type;

		// Token: 0x040002DF RID: 735
		public string title;

		// Token: 0x040002E0 RID: 736
		protected SemaphoreSlim uv_operate_sem;
	}
}
