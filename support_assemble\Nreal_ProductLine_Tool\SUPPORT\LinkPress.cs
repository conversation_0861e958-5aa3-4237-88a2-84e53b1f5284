﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.Threading;
using System.Timers;
using System.Windows.Forms;

namespace Nreal_ProductLine_Tool.SUPPORT
{
	// Token: 0x02000015 RID: 21
	public partial class LinkPress : FormDefault
	{
		// Token: 0x060000A1 RID: 161 RVA: 0x0000D7F8 File Offset: 0x0000B9F8
		public LinkPress(Entrance entrance, int sequence, string title)
		{
			this.sequence = sequence;
			this.entrance = entrance;
			this.title = title;
			this.InitializeComponent();
			base.TopLevel = false;
			base.FormBorderStyle = FormBorderStyle.None;
			this.Dock = DockStyle.Fill;
			this.vacu_operate_sem = new SemaphoreSlim(0, 1);
			Thread thread = new Thread(new ThreadStart(this.vacu_operate));
			thread.Start();
			this.axis_operate_sem = new SemaphoreSlim(0, 1);
			Thread thread2 = new Thread(new ThreadStart(this.axis_operate));
			thread2.Start();
			this.pressure_value_sem = new SemaphoreSlim(0, 1);
			Thread thread3 = new Thread(new ThreadStart(this.pressure_value_operate));
			thread3.Start();
			this.dist_value_sem = new SemaphoreSlim(0, 1);
			Thread thread4 = new Thread(new ThreadStart(this.dist_value_operate));
			thread4.Start();
			Thread.Sleep(100);
			this.show_timer.Elapsed += this.Show_Timer_Elapsed;
			this.show_timer.Interval = 200.0;
			this.show_timer.Enabled = false;
			this.dist_timer.Elapsed += this.dist_Timer_Elapsed;
			this.dist_timer.Interval = 200.0;
			this.dist_timer.Enabled = false;
		}

		// Token: 0x060000A2 RID: 162 RVA: 0x0000D9B0 File Offset: 0x0000BBB0
		public override string GetName()
		{
			return this.title;
		}

		// Token: 0x060000A3 RID: 163 RVA: 0x0000D9C8 File Offset: 0x0000BBC8
		public override void Start()
		{
			this.motion_type = 0;
			LinkPress.timer_stop = false;
			LinkPress.isExcepExited = false;
			this.StopPress = false;
			this.StopDist = false;
			this.press_btn_handled = false;
			this.link_press_btn.Enabled = false;
			Colimator.serialPort = Colimator.serialPort1;
			Colimator.TurnOnColimator();
			Colimator.serialPort = Colimator.serialPort2;
			Colimator.TurnOnColimator();
			this.show_timer.Enabled = true;
			this.dist_timer.Enabled = true;
			GlobalData.distance[0] = -1.0;
			this.link_press_status_label.Text = "等待关门信号";
			this.time_label.Text = "";
			this.vacum_val_label.Text = "";
			this.VacuFinished = false;
			this.vacu_operate_sem.Release();
			base.Show();
			this.InitStatus();
			Logs.WriteInfo("Configure.phrase2_x_point1_min:" + Configure.phrase2_x_point1_min.ToString(), true);
			Logs.WriteInfo("Configure.phrase2_x_point1_max:" + Configure.phrase2_x_point1_max.ToString(), true);
		}

		// Token: 0x060000A4 RID: 164 RVA: 0x0000DAE0 File Offset: 0x0000BCE0
		public override void ExcepStop()
		{
			bool flag = this.press_btn_handled;
			if (flag)
			{
				this.distance_captured = true;
				this.StopDist = true;
				this.StopPress = true;
			}
			Logs.WriteInfo("linkpress ExcepStop", true);
			LinkPress.isExcepExited = true;
			LinkPress.timer_stop = false;
			this.dist_timer.Enabled = false;
			this.show_timer.Enabled = false;
			Colimator.serialPort = Colimator.serialPort2;
			Colimator.TurnOffColimator();
			Colimator.serialPort = Colimator.serialPort1;
			Colimator.TurnOffColimator();
			this.stopAxis();
			ZMotionController.VacuummeterPumpClose();
			Thread.Sleep(100);
			Logs.WriteInfo("关闭抽气阀", true);
			ZMotionController.GasReleaseClose();
			Thread.Sleep(100);
			Logs.WriteInfo("打开放气阀", true);
			ZMotionController.VacuummeterGasOutOpen();
			Thread.Sleep(Configure.vacuum_delay_time3);
			Logs.WriteInfo("关闭放气阀", true);
			ZMotionController.VacuummeterGasOutClose();
		}

		// Token: 0x060000A5 RID: 165 RVA: 0x0000DBC4 File Offset: 0x0000BDC4
		private void vacu_operate()
		{
			while (!LinkPress.isExited)
			{
				this.vacu_operate_sem.Wait();
				this.motion_type = 1;
				this.axis_operate_sem.Release();
				Logs.WriteInfo("开始抽真空", true);
				this.link_press_status_label.Invoke(new MethodInvoker(delegate()
				{
					this.link_press_status_label.Text = "开始抽真空";
					this.vacu_setting_time_label.Text = (Configure.vacuummeter_pump_time.ToString() ?? "");
				}));
				Logs.WriteInfo("打开真空泵", true);
				ZMotionController.VacuummeterPumpOpen();
				Thread.Sleep(100);
				Logs.WriteInfo("打开排气阀", true);
				ZMotionController.GasReleaseOpen();
				int count_down = Configure.vacuummeter_pump_time;
				double vacuummeter_val = 10000.0;
				while (count_down > 0)
				{
					Vacuummeter.getVacuummeter(ref vacuummeter_val);
					Logs.WriteInfo("真空值:" + vacuummeter_val.ToString(), true);
					base.Invoke(new MethodInvoker(delegate()
					{
						this.time_label.Text = (count_down.ToString() ?? "");
						this.vacum_val_label.Text = (vacuummeter_val.ToString() ?? "");
					}));
					int count_down2 = count_down;
					count_down = count_down2 - 1;
					Thread.Sleep(1000);
				}
				while (!LinkPress.isExcepExited)
				{
					bool flag = vacuummeter_val < Configure.vacuummeter_value;
					if (flag)
					{
						break;
					}
					Vacuummeter.getVacuummeter(ref vacuummeter_val);
					GlobalData.vacuummeter_value = vacuummeter_val;
					base.Invoke(new MethodInvoker(delegate()
					{
						this.vacum_val_label.Text = (vacuummeter_val.ToString() ?? "");
					}));
					Thread.Sleep(20);
				}
				bool flag2 = LinkPress.isExcepExited;
				if (!flag2)
				{
					Logs.WriteInfo("抽真空完毕，开始下压", true);
					this.VacuFinished = true;
					this.link_press_status_label.Invoke(new MethodInvoker(delegate()
					{
						this.link_press_status_label.Text = "开始判断准直并下压";
					}));
				}
			}
		}

		// Token: 0x060000A6 RID: 166 RVA: 0x0000DD80 File Offset: 0x0000BF80
		private void pressure_value_operate()
		{
			while (!LinkPress.isExited)
			{
				Logs.WriteInfo("pressure_value_operate", true);
				this.pressure_value_sem.Wait();
				double num = 0.0;
				PressSensor.getPressValue(ref num);
				while (!this.StopPress)
				{
					double num2 = 0.0;
					PressSensor.getPressValue(ref num2);
					GlobalData.pressure_val = num2;
					Logs.WriteInfo("getPressValue:" + num2.ToString(), true);
					bool flag = num2 - num > Configure.pressure_val;
					if (flag)
					{
						this.press_captured = true;
						this.stopAxis();
						Logs.WriteInfo("压力传感器值大于设定值，当前" + num2.ToString() + " 设定值:" + Configure.pressure_val.ToString(), true);
						break;
					}
					Thread.Sleep(50);
				}
			}
		}

		// Token: 0x060000A7 RID: 167 RVA: 0x0000DE68 File Offset: 0x0000C068
		private void stopAxis()
		{
			bool flag = true;
			while (flag)
			{
				int num;
				int num2;
				GSC_Controller.GetAxisStatus(out num, out num2, out flag);
				bool flag2 = !flag;
				if (flag2)
				{
					break;
				}
				GSC_Controller.StopE();
				Thread.Sleep(10);
			}
		}

		// Token: 0x060000A8 RID: 168 RVA: 0x0000DEA8 File Offset: 0x0000C0A8
		private void dist_value_operate()
		{
			Logs.WriteInfo("厚度目标值" + Configure.distance_max.ToString() + "，offset" + Configure.distance_offset.ToString(), true);
			while (!LinkPress.isExited)
			{
				this.dist_value_sem.Wait();
				Logs.WriteInfo("dist", true);
				Thread.Sleep(100);
				while (!this.StopDist)
				{
					bool flag = GlobalData.distance[0] != 0.0 && GlobalData.distance[0] != -1.0 && GlobalData.distance[0] < Configure.distance_max + Configure.distance_stop_before_thresh;
					if (flag)
					{
						bool flag2 = true;
						int num;
						int num2;
						GSC_Controller.GetAxisStatus(out num, out num2, out flag2);
						bool flag3 = !flag2 || this.press_captured;
						if (flag3)
						{
							Logs.WriteInfo("轴已经停止", true);
							break;
						}
						Logs.WriteInfo("距离小于设定值上方" + Configure.distance_stop_before_thresh.ToString() + "，当前" + GlobalData.distance[0].ToString(), true);
						this.stopAxis();
						Logs.WriteInfo("轴停止运动", true);
						Thread.Sleep(Configure.vacuum_delay_time2);
						Logs.WriteInfo("打开放气阀", true);
						ZMotionController.VacuummeterGasOutOpen();
						Thread.Sleep(Configure.vacuum_delay_time3);
						this.second_button.Invoke(new MethodInvoker(delegate()
						{
							this.second_button.Enabled = true;
							this.second_button.BackColor = Color.Green;
						}));
						while (!this.gas_release_finished)
						{
							Thread.Sleep(100);
							bool stopDist = this.StopDist;
							if (stopDist)
							{
								break;
							}
						}
						bool stopDist2 = this.StopDist;
						if (stopDist2)
						{
							Logs.WriteInfo("Dist重新开始", true);
						}
						else
						{
							Thread.Sleep(100);
							int num3 = MotionController.SetSpeedPressure();
							bool flag4 = num3 != 0;
							if (flag4)
							{
								MotionController.SetSpeedPressure();
							}
							GSC_Controller.GetAxisStatus(out num, out num2, out flag2);
							Logs.WriteInfo("当前坐标：" + num2.ToString(), true);
							int num4 = (int)(GlobalData.distance[0] - Configure.distance_max - Configure.distance_offset);
							num4 = Math.Min(num4, num2 - Configure.zDest3);
							Logs.WriteInfo("重新设置运行行程" + num4.ToString(), true);
							GSC_Controller.MoveRel("2", -num4);
							bool flag5 = this.press_captured;
							if (flag5)
							{
								this.distance_captured = true;
								break;
							}
							Thread.Sleep(20);
							while (GlobalData.distance[0] > Configure.distance_max)
							{
								int num5 = 2;
								bool flag6 = GlobalData.distance[0] - Configure.distance_max < 3.0;
								if (flag6)
								{
									break;
								}
								GSC_Controller.MoveRel("2", -num5);
								Logs.WriteInfo("当前厚度值" + GlobalData.distance[0].ToString(), true);
								GSC_Controller.GetAxisStatus(out num, out num2, out flag2);
								Logs.WriteInfo("z轴位置：" + num2.ToString(), true);
								bool flag7 = this.press_captured || num2 < Configure.zDest3;
								if (flag7)
								{
									this.distance_captured = true;
									break;
								}
							}
							this.distance_captured = true;
							Logs.WriteInfo("距离小于设定值，当前" + GlobalData.distance[0].ToString() + " 设定值:" + Configure.distance_max.ToString(), true);
							Thread.Sleep(100);
						}
					}
				}
			}
		}

		// Token: 0x060000A9 RID: 169 RVA: 0x0000E22C File Offset: 0x0000C42C
		private void axis_operate()
		{
			while (!LinkPress.isExited)
			{
				this.axis_operate_sem.Wait();
				Logs.WriteInfo("axis move", true);
				int i;
				for (i = 0; i < 10; i++)
				{
					bool flag = !MotionController.running;
					if (flag)
					{
						break;
					}
					Thread.Sleep(200);
				}
				bool flag2 = i == 10;
				if (flag2)
				{
					MessageBox.Show("当前电机繁忙，请重新开始");
				}
				else
				{
					MotionController.running = true;
					bool flag3 = this.motion_type == 1;
					if (flag3)
					{
						Logs.WriteInfo("x开始复位，z到zDest2", true);
						int num;
						int num2;
						bool flag4;
						GSC_Controller.GetAxisStatus(out num, out num2, out flag4);
						bool flag5 = flag4;
						if (flag5)
						{
							Logs.WriteInfo("轴状态复位", true);
							GSC_Controller.Stop("1");
							GSC_Controller.Stop("2");
							Thread.Sleep(50);
						}
						GSC_Controller.MoveBothAbs(GlobalData.x_zero_positon, Configure.zDest2);
						MotionController.running = false;
					}
					else
					{
						bool flag6 = this.motion_type == 2;
						if (flag6)
						{
							this.distance_captured = false;
							this.press_captured = false;
							Logs.WriteInfo("begin move zDest1", true);
							GSC_Controller.MoveAbs("2", Configure.zDest1);
							Logs.WriteInfo("axis move zDest1", true);
							Thread.Sleep(100);
							MotionController.SetSpeedPressure();
							Thread.Sleep(100);
							Logs.WriteInfo("pressure begin", true);
							this.pressure_value_sem.Release();
							Logs.WriteInfo("distance begin", true);
							this.dist_value_sem.Release();
							GSC_Controller.MoveAbs("2", Configure.zDest3);
							int num3;
							int num4;
							bool flag7;
							GSC_Controller.GetAxisStatus(out num3, out num4, out flag7);
							bool flag8 = num4 == Configure.zDest1;
							if (flag8)
							{
								Logs.WriteInfo("重新发送移动命令", true);
								GSC_Controller.MoveAbs("2", Configure.zDest3);
							}
							Logs.WriteInfo("z轴停止完成", true);
							while (!this.distance_captured)
							{
								Thread.Sleep(100);
							}
							GSC_Controller.GetAxisStatus(out num3, out num4, out flag7);
							GlobalData.link_press_z = num4;
							MotionController.running = false;
							this.StopPress = true;
							this.StopDist = true;
							bool flag9 = LinkPress.isExcepExited;
							if (!flag9)
							{
								int num5 = 0;
								double num6 = 0.0;
								PressSensor.getPressValue(ref num6);
								bool flag10 = num6 < Configure.presure_val_min || num6 > Configure.presure_val_max;
								if (flag10)
								{
									num5 = 1;
									MessageBox.Show("压力值不在规格内，判定NG");
								}
								bool flag11 = GlobalData.distance[0] < Configure.thickness_val_min || num6 > Configure.thickness_val_max;
								if (flag11)
								{
									num5 = 1;
									MessageBox.Show("胶厚不在规格内，判定NG");
								}
								bool flag12 = num5 > 0;
								if (flag12)
								{
									GlobalData.result = "False";
									Db.dataSaveIntoDB();
									base.Invoke(new MethodInvoker(delegate()
									{
										this.dist_timer.Enabled = false;
										base.Hide();
										this.entrance.Finish(this.sequence, 1);
									}));
								}
								else
								{
									this.link_press_status_label.Invoke(new MethodInvoker(delegate()
									{
										this.link_press_status_label.Text = "压合结束，开始破真空";
									}));
									base.Invoke(new MethodInvoker(delegate()
									{
										this.dist_timer.Enabled = false;
										base.Hide();
										this.entrance.Finish(this.sequence, 0);
									}));
								}
							}
						}
					}
				}
			}
		}

		// Token: 0x060000AA RID: 170 RVA: 0x0000E534 File Offset: 0x0000C734
		private void p2_operate()
		{
			this.p2_x_label.Text = GlobalData.colimatorOrigDataP2[0].ToString("F4");
			this.p2_y_label.Text = GlobalData.colimatorOrigDataP2[1].ToString("F4");
			bool flag = GlobalData.colimatorOrigDataP2[0] < Configure.phrase2_x_point2_min || GlobalData.colimatorOrigDataP2[0] > Configure.phrase2_x_point2_max;
			if (flag)
			{
				this.p2_x_label.BackColor = this.fail;
			}
			else
			{
				this.p2_x_label.BackColor = this.pass;
			}
			bool flag2 = GlobalData.colimatorOrigDataP2[1] < Configure.phrase2_y_point2_min || GlobalData.colimatorOrigDataP2[1] > Configure.phrase2_y_point2_max;
			if (flag2)
			{
				this.p2_y_label.BackColor = this.fail;
			}
			else
			{
				this.p2_y_label.BackColor = this.pass;
			}
		}

		// Token: 0x060000AB RID: 171 RVA: 0x0000E618 File Offset: 0x0000C818
		private void InitStatus()
		{
			this.p1_x_label.Text = "";
			this.p1_y_label.Text = "";
			this.p3_x_label.Text = "";
			this.p3_y_label.Text = "";
			this.p13_avgx_label.Text = "";
			this.p13_avgy_label.Text = "";
			this.p2_x_label.Text = "";
			this.p2_y_label.Text = "";
			this.p1_x_label.BackColor = this.gray;
			this.p1_y_label.BackColor = this.gray;
			this.p3_x_label.BackColor = this.gray;
			this.p3_y_label.BackColor = this.gray;
			this.p13_avgx_label.BackColor = this.gray;
			this.p13_avgy_label.BackColor = this.gray;
			this.p2_x_label.BackColor = this.gray;
			this.p2_y_label.BackColor = this.gray;
			this.coli_diff_label.BackColor = this.gray;
			this.coli_diff_label.Text = "";
		}

		// Token: 0x060000AC RID: 172 RVA: 0x0000E764 File Offset: 0x0000C964
		private int FindEffectPoints()
		{
			int num = 0;
			bool flag = GlobalData.colimatorOrigDataP13[0] > Configure.x_except_min && GlobalData.colimatorOrigDataP13[0] < Configure.x_except_max && GlobalData.colimatorOrigDataP13[1] > Configure.y_except_min && GlobalData.colimatorOrigDataP13[1] < Configure.y_except_max;
			if (flag)
			{
				num++;
				GlobalData.phrase4_colimatorPoint1[0] = GlobalData.colimatorOrigDataP13[0];
				GlobalData.phrase4_colimatorPoint1[1] = GlobalData.colimatorOrigDataP13[1];
			}
			else
			{
				GlobalData.phrase4_colimatorPoint1[0] = Configure.x_except_max;
				GlobalData.phrase4_colimatorPoint1[1] = Configure.y_except_max;
			}
			bool flag2 = GlobalData.colimatorOrigDataP13[2] > Configure.x_except_min && GlobalData.colimatorOrigDataP13[2] < Configure.x_except_max && GlobalData.colimatorOrigDataP13[3] > Configure.y_except_min && GlobalData.colimatorOrigDataP13[3] < Configure.y_except_max;
			if (flag2)
			{
				num++;
				GlobalData.phrase4_colimatorPoint3[0] = GlobalData.colimatorOrigDataP13[2];
				GlobalData.phrase4_colimatorPoint3[1] = GlobalData.colimatorOrigDataP13[3];
			}
			else
			{
				GlobalData.phrase4_colimatorPoint3[0] = Configure.x_except_max;
				GlobalData.phrase4_colimatorPoint3[1] = Configure.y_except_max;
			}
			bool flag3 = GlobalData.colimatorOrigDataP13[4] > Configure.x_except_min && GlobalData.colimatorOrigDataP13[4] < Configure.x_except_max && GlobalData.colimatorOrigDataP13[5] > Configure.y_except_min && GlobalData.colimatorOrigDataP13[5] < Configure.y_except_max;
			if (flag3)
			{
				bool flag4 = num < 2;
				if (flag4)
				{
					bool flag5 = GlobalData.phrase4_colimatorPoint1[0] == Configure.x_except_max;
					if (flag5)
					{
						GlobalData.phrase4_colimatorPoint1[0] = GlobalData.colimatorOrigDataP13[4];
						GlobalData.phrase4_colimatorPoint1[1] = GlobalData.colimatorOrigDataP13[5];
					}
					bool flag6 = GlobalData.phrase4_colimatorPoint3[0] == Configure.x_except_max;
					if (flag6)
					{
						GlobalData.phrase4_colimatorPoint3[0] = GlobalData.colimatorOrigDataP13[4];
						GlobalData.phrase4_colimatorPoint3[1] = GlobalData.colimatorOrigDataP13[5];
					}
				}
				else
				{
					bool flag7 = GlobalData.phrase4_colimatorPoint1[0] > GlobalData.phrase4_colimatorPoint3[0];
					if (flag7)
					{
						bool flag8 = GlobalData.phrase4_colimatorPoint1[0] > GlobalData.colimatorOrigDataP13[4];
						if (flag8)
						{
							GlobalData.phrase4_colimatorPoint1[0] = GlobalData.colimatorOrigDataP13[4];
							GlobalData.phrase4_colimatorPoint1[1] = GlobalData.colimatorOrigDataP13[5];
						}
					}
					else
					{
						bool flag9 = GlobalData.phrase4_colimatorPoint3[0] > GlobalData.colimatorOrigDataP13[4];
						if (flag9)
						{
							GlobalData.phrase4_colimatorPoint3[0] = GlobalData.colimatorOrigDataP13[4];
							GlobalData.phrase4_colimatorPoint3[1] = GlobalData.colimatorOrigDataP13[5];
						}
					}
				}
				num++;
			}
			return num;
		}

		// Token: 0x060000AD RID: 173 RVA: 0x0000E9BC File Offset: 0x0000CBBC
		private void link_press_btn_Click(object sender, EventArgs e)
		{
			this.second_button.Enabled = false;
			this.gas_release_finished = false;
			bool @checked = this.checkBox1.Checked;
			if (@checked)
			{
				ZMotionController.GasReleaseClose();
				Thread.Sleep(Configure.vacuum_delay_time1);
			}
			Logs.WriteInfo("关闭抽气阀", true);
			ZMotionController.GasReleaseClose();
			this.link_press_btn.Enabled = false;
			this.press_btn_handled = true;
			this.link_press_status_label.Text = "电机移动";
			this.motion_type = 2;
			this.axis_operate_sem.Release();
		}

		// Token: 0x060000AE RID: 174 RVA: 0x0000EA4A File Offset: 0x0000CC4A
		private void dist_Timer_Elapsed(object sender, ElapsedEventArgs e)
		{
			this.dist_val_label.Invoke(new MethodInvoker(delegate()
			{
				this.dist_val_label.Text = (GlobalData.distance[0].ToString() ?? "");
			}));
		}

		// Token: 0x060000AF RID: 175 RVA: 0x0000EA65 File Offset: 0x0000CC65
		private void second_button_Click(object sender, EventArgs e)
		{
			this.gas_release_finished = true;
			this.second_button.BackColor = Color.Gray;
			this.second_button.Enabled = false;
		}

		// Token: 0x060000B0 RID: 176 RVA: 0x0000EA90 File Offset: 0x0000CC90
		private void Show_Timer_Elapsed(object sender, ElapsedEventArgs e)
		{
			bool flag = !LinkPress.timer_stop;
			if (flag)
			{
				base.Invoke(new MethodInvoker(delegate()
				{
					this.InitStatus();
					int num = this.FindEffectPoints();
					bool flag2 = num == 0;
					if (flag2)
					{
						this.colimator_p13_status_label.Text = "当前没有有效点";
						this.colimator_p13_status_label.ForeColor = Color.Red;
						this.p2_operate();
					}
					else
					{
						this.colimator_p13_status_label.Text = "";
						this.colimator_p13_status_label.ForeColor = Color.Gray;
						bool flag3 = num == 1;
						if (flag3)
						{
							bool flag4 = GlobalData.phrase4_colimatorPoint1[0] != Configure.x_except_max;
							if (flag4)
							{
								GlobalData.phrase4_colimatorPoint3[0] = GlobalData.phrase4_colimatorPoint1[0];
								GlobalData.phrase4_colimatorPoint3[1] = GlobalData.phrase4_colimatorPoint1[1];
							}
							else
							{
								GlobalData.phrase4_colimatorPoint1[0] = GlobalData.phrase4_colimatorPoint3[0];
								GlobalData.phrase4_colimatorPoint1[1] = GlobalData.phrase4_colimatorPoint3[1];
							}
						}
						this.p1_x_label.Text = GlobalData.phrase4_colimatorPoint1[0].ToString("F4");
						this.p1_y_label.Text = GlobalData.phrase4_colimatorPoint1[1].ToString("F4");
						this.p3_x_label.Text = GlobalData.phrase4_colimatorPoint3[0].ToString("F4");
						this.p3_y_label.Text = GlobalData.phrase4_colimatorPoint3[1].ToString("F4");
						bool flag5 = GlobalData.phrase4_colimatorPoint1[0] < Configure.phrase2_x_point1_min || GlobalData.phrase4_colimatorPoint1[0] > Configure.phrase2_x_point1_max;
						if (flag5)
						{
							this.p1_x_label.BackColor = this.fail;
						}
						else
						{
							this.p1_x_label.BackColor = this.pass;
						}
						bool flag6 = GlobalData.phrase4_colimatorPoint1[1] < Configure.phrase2_y_point1_min || GlobalData.phrase4_colimatorPoint1[1] > Configure.phrase2_y_point1_max;
						if (flag6)
						{
							this.p1_y_label.BackColor = this.fail;
						}
						else
						{
							this.p1_y_label.BackColor = this.pass;
						}
						bool flag7 = GlobalData.phrase4_colimatorPoint3[0] < Configure.phrase2_x_point3_min || GlobalData.phrase4_colimatorPoint3[0] > Configure.phrase2_x_point3_max;
						if (flag7)
						{
							this.p3_x_label.BackColor = this.fail;
						}
						else
						{
							this.p3_x_label.BackColor = this.pass;
						}
						bool flag8 = GlobalData.phrase4_colimatorPoint3[1] < Configure.phrase2_y_point3_min || GlobalData.phrase4_colimatorPoint3[1] > Configure.phrase2_y_point3_max;
						if (flag8)
						{
							this.p3_y_label.BackColor = this.fail;
						}
						else
						{
							this.p3_y_label.BackColor = this.pass;
						}
						GlobalData.phrase4_colimatorPoint13Avg[0] = (GlobalData.phrase4_colimatorPoint1[0] + GlobalData.phrase4_colimatorPoint3[0]) / 2.0;
						GlobalData.phrase4_colimatorPoint13Avg[1] = (GlobalData.phrase4_colimatorPoint1[1] + GlobalData.phrase4_colimatorPoint3[1]) / 2.0;
						this.p13_avgx_label.Text = GlobalData.phrase4_colimatorPoint13Avg[0].ToString("F4");
						this.p13_avgy_label.Text = GlobalData.phrase4_colimatorPoint13Avg[1].ToString("F4");
						bool flag9 = GlobalData.phrase4_colimatorPoint13Avg[0] < Configure.phrase2_x_point13_min || GlobalData.phrase4_colimatorPoint13Avg[0] > Configure.phrase2_x_point13_max;
						if (flag9)
						{
							this.p13_avgx_label.BackColor = this.fail;
						}
						else
						{
							this.p13_avgx_label.BackColor = this.pass;
						}
						bool flag10 = GlobalData.phrase4_colimatorPoint13Avg[1] < Configure.phrase2_y_point13_min || GlobalData.phrase4_colimatorPoint13Avg[1] > Configure.phrase2_y_point13_max;
						if (flag10)
						{
							this.p13_avgy_label.BackColor = this.fail;
						}
						else
						{
							this.p13_avgy_label.BackColor = this.pass;
						}
						this.p2_operate();
						double x = GlobalData.phrase4_colimatorPoint13Avg[0] - GlobalData.colimatorOrigDataP2[0];
						double x2 = GlobalData.phrase4_colimatorPoint13Avg[1] - GlobalData.colimatorOrigDataP2[1];
						double num2 = Math.Pow(x, 2.0) + Math.Pow(x2, 2.0);
						num2 = Math.Sqrt(num2);
						this.coli_diff_label.Text = Math.Round(num2, 3).ToString();
						GlobalData.phrase4_colimatorDiff = num2;
						bool flag11 = num2 > Configure.diff_min && num2 < Configure.diff_max;
						if (flag11)
						{
							this.coli_diff_label.BackColor = Color.Green;
						}
						else
						{
							this.coli_diff_label.BackColor = Color.Red;
						}
						bool flag12 = this.p1_x_label.BackColor == this.pass && this.p1_y_label.BackColor == this.pass && this.p3_x_label.BackColor == this.pass && this.p3_y_label.BackColor == this.pass && this.p13_avgx_label.BackColor == this.pass && this.p13_avgy_label.BackColor == this.pass && this.p2_x_label.BackColor == this.pass && this.p2_y_label.BackColor == this.pass;
						if (flag12)
						{
							bool flag13 = this.VacuFinished && !this.press_btn_handled;
							if (flag13)
							{
								this.link_press_btn.Enabled = true;
							}
						}
					}
				}));
			}
			else
			{
				this.show_timer.Enabled = false;
			}
		}

		// Token: 0x040001CC RID: 460
		private Timer show_timer = new Timer();

		// Token: 0x040001CD RID: 461
		public static bool timer_stop;

		// Token: 0x040001CE RID: 462
		private Entrance entrance;

		// Token: 0x040001CF RID: 463
		private int sequence;

		// Token: 0x040001D0 RID: 464
		private int type;

		// Token: 0x040001D1 RID: 465
		public string title;

		// Token: 0x040001D2 RID: 466
		protected SemaphoreSlim axis_operate_sem;

		// Token: 0x040001D3 RID: 467
		protected SemaphoreSlim pressure_value_sem;

		// Token: 0x040001D4 RID: 468
		public SemaphoreSlim dist_value_sem;

		// Token: 0x040001D5 RID: 469
		public static bool isExited;

		// Token: 0x040001D6 RID: 470
		public static bool isExcepExited;

		// Token: 0x040001D7 RID: 471
		private int motion_type = 0;

		// Token: 0x040001D8 RID: 472
		private bool StopPress;

		// Token: 0x040001D9 RID: 473
		private bool StopDist;

		// Token: 0x040001DA RID: 474
		private bool VacuFinished = false;

		// Token: 0x040001DB RID: 475
		private Color fail = Color.OrangeRed;

		// Token: 0x040001DC RID: 476
		private Color pass = Color.Green;

		// Token: 0x040001DD RID: 477
		private Color gray = Color.Gray;

		// Token: 0x040001DE RID: 478
		private bool press_btn_handled = false;

		// Token: 0x040001DF RID: 479
		private bool distance_captured;

		// Token: 0x040001E0 RID: 480
		private bool press_captured;

		// Token: 0x040001E1 RID: 481
		private bool gas_release_finished = false;

		// Token: 0x040001E2 RID: 482
		protected SemaphoreSlim vacu_operate_sem;

		// Token: 0x040001E3 RID: 483
		private Timer dist_timer = new Timer();
	}
}
