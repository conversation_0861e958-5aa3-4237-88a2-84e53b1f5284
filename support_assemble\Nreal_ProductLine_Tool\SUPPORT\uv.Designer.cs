﻿namespace Nreal_ProductLine_Tool.SUPPORT
{
	// Token: 0x02000025 RID: 37
	public partial class uv : global::Nreal_ProductLine_Tool.SUPPORT.FormDefault
	{
		// Token: 0x060002C7 RID: 711 RVA: 0x00018540 File Offset: 0x00016740
		protected override void Dispose(bool disposing)
		{
			bool flag = disposing && this.components != null;
			if (flag)
			{
				this.components.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x060002C8 RID: 712 RVA: 0x00018577 File Offset: 0x00016777
		private void InitializeComponent()
		{
			this.components = new global::System.ComponentModel.Container();
			base.AutoScaleMode = global::System.Windows.Forms.AutoScaleMode.Font;
			base.ClientSize = new global::System.Drawing.Size(800, 450);
			this.Text = "uv";
		}

		// Token: 0x040002E1 RID: 737
		private global::System.ComponentModel.IContainer components = null;
	}
}
