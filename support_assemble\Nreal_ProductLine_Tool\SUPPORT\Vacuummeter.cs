﻿using System;
using System.IO.Ports;
using Modbus.Device;

namespace Nreal_ProductLine_Tool.SUPPORT
{
	// Token: 0x02000012 RID: 18
	internal class Vacuummeter
	{
		// Token: 0x06000081 RID: 129 RVA: 0x0000A8C8 File Offset: 0x00008AC8
		public static int Init()
		{
			Logs.WriteInfo("Vacuummeter init port:" + Configure.vacuummeter_SerialPort, true);
			try
			{
				Vacuummeter.serialPort = new SerialPort(Configure.vacuummeter_SerialPort, 9600, 0, 8, 1);
				Vacuummeter.serialPort.Open();
				Vacuummeter.master = ModbusSerialMaster.CreateRtu(Vacuummeter.serialPort);
				Vacuummeter.inited = true;
				Logs.WriteInfo("Vacuummeter init success", true);
			}
			catch (Exception ex)
			{
				Console.WriteLine("发生错误: " + ex.Message);
				return -1;
			}
			return 0;
		}

		// Token: 0x06000082 RID: 130 RVA: 0x0000A964 File Offset: 0x00008B64
		public static int uninit()
		{
			bool flag = !Vacuummeter.inited;
			int result;
			if (flag)
			{
				result = 0;
			}
			else
			{
				try
				{
					Vacuummeter.serialPort.Close();
					Vacuummeter.inited = false;
				}
				catch (Exception ex)
				{
					Console.WriteLine("发生错误: " + ex.Message);
					return -1;
				}
				result = 0;
			}
			return result;
		}

		// Token: 0x06000083 RID: 131 RVA: 0x0000A9CC File Offset: 0x00008BCC
		public static int getVacuummeter(ref double val)
		{
			bool flag = !Vacuummeter.inited;
			int result;
			if (flag)
			{
				result = -1;
			}
			else
			{
				try
				{
					ushort[] array = Vacuummeter.master.ReadHoldingRegisters(Vacuummeter.slaveAddress, Vacuummeter.startAddress, Vacuummeter.numberOfRegisters);
					bool flag2 = array != null;
					if (flag2)
					{
						for (int i = 0; i < array.Length; i++)
						{
						}
						int num = array[0] >> 8;
						num -= 48;
						int num2 = (int)(array[0] & 255);
						num2 -= 48;
						double num3 = (double)(num * 10 + num2) / 10.0;
						int num4 = array[1] >> 8;
						int num5 = (int)((array[1] & 255) - 48);
						bool flag3 = num4 == 45;
						if (flag3)
						{
							num5 = -num5;
						}
						num3 *= Math.Pow(10.0, (double)num5);
						val = num3;
					}
				}
				catch (Exception ex)
				{
					return -1;
				}
				result = 0;
			}
			return result;
		}

		// Token: 0x04000184 RID: 388
		private static byte slaveAddress = 1;

		// Token: 0x04000185 RID: 389
		private static ushort startAddress = 107;

		// Token: 0x04000186 RID: 390
		private static ushort numberOfRegisters = 2;

		// Token: 0x04000187 RID: 391
		private static IModbusSerialMaster master;

		// Token: 0x04000188 RID: 392
		private static SerialPort serialPort = null;

		// Token: 0x04000189 RID: 393
		private static bool inited = false;
	}
}
