﻿using System;
using System.Collections.Generic;
using ParamManager;

namespace Nreal_ProductLine_Tool.SUPPORT
{
	// Token: 0x02000010 RID: 16
	internal class Configure
	{
		// Token: 0x06000079 RID: 121 RVA: 0x00009B4E File Offset: 0x00007D4E
		public static void Init_test(string sysFilePath)
		{
			Logs.WriteDebug("just for test " + sysFilePath, true);
		}

		// Token: 0x0600007A RID: 122 RVA: 0x00009B64 File Offset: 0x00007D64
		public static void UpdateStationDataFunc(string stationNo, string projectName, string mode)
		{
			string text = string.Concat(new string[]
			{
				" #站号->",
				stationNo,
				" #项目->",
				projectName,
				" #模式->",
				mode
			});
		}

		// Token: 0x0600007B RID: 123 RVA: 0x00009BA4 File Offset: 0x00007DA4
		public static void Init()
		{
			PM pm = new PM();
			pm.updateStationData += new PM.UpdateStationData(Configure.UpdateStationDataFunc);
			pm.InitAll(true, 42, 0);
			Configure.Para_get(ref pm);
		}

		// Token: 0x0600007C RID: 124 RVA: 0x00009BE0 File Offset: 0x00007DE0
		public static int Init_Local()
		{
			PM pm = new PM();
			pm.updateStationData += new PM.UpdateStationData(Configure.UpdateStationDataFunc);
			int num = pm.ReadConfigFile();
			bool flag = num != 0;
			int result;
			if (flag)
			{
				result = num;
			}
			else
			{
				Configure.Para_get(ref pm);
				result = 0;
			}
			return result;
		}

		// Token: 0x0600007D RID: 125 RVA: 0x00009C28 File Offset: 0x00007E28
		public static void Para_get(ref PM pm)
		{
			Configure.sn_length = pm.Vi("Product#sn_length");
			Configure.mes_resource = pm.Vs("Product#mes_resource");
			Configure.StationNO = pm.Vs("Product#StationNO");
			Configure.camera_exposure_time = pm.Vi("Product#camera_exposure_time");
			Configure.camera_gain = pm.Vi("Product#camera_gain");
			Configure.ySpeedHigh = pm.Vi("Motion#ySpeedHigh");
			Configure.zSpeedHigh = pm.Vi("Motion#zSpeedHigh");
			Configure.ySpeedLow = pm.Vi("Motion#ySpeedLow");
			Configure.pressureZSpeed = pm.Vi("Motion#pressureZSpeed");
			Configure.pressureZ2Speed = pm.Vi("Motion#pressureZ2Speed");
			Configure.zSpeed3 = pm.Vi("Motion#zSpeed3");
			Configure.yDest1 = pm.Vi("Motion#yDest1");
			Configure.yDest2 = pm.Vi("Motion#yDest2");
			Configure.zDest1 = pm.Vi("Motion#zDest1");
			Configure.zDest2 = pm.Vi("Motion#zDest2");
			Configure.zDest3 = pm.Vi("Motion#zDest3");
			Configure.Colimator_Use = pm.Vb("Colimator#Use");
			Configure.Colimator_SerialPort1 = pm.Vs("Colimator#SerialPort1");
			Configure.Colimator_SerialPort2 = pm.Vs("Colimator#SerialPort2");
			Configure.vacuummeter_SerialPort = pm.Vs("Product#vacuummeter_SerialPort");
			Configure.press_sensor_SerialPort = pm.Vs("Product#press_sensor_SerialPort");
			Configure.vacuummeter_door_close_in = pm.Vi("IO#vacuummeter_door_close_in");
			Configure.uv_in = pm.Vi("IO#uv_in");
			Configure.vacuummeter_pump_out = pm.Vi("IO#vacuummeter_pump_out");
			Configure.gas_release_out = pm.Vi("IO#gas_release_out");
			Configure.gas_out = pm.Vi("IO#gas_out");
			Configure.uv_out = pm.Vi("IO#uv_out");
			Configure.x_point1_max = pm.Vd("Verify#x_point1_max");
			Configure.x_point1_min = pm.Vd("Verify#x_point1_min");
			Configure.y_point1_max = pm.Vd("Verify#y_point1_max");
			Configure.y_point1_min = pm.Vd("Verify#y_point1_min");
			Configure.x_point2_max = pm.Vd("Verify#x_point2_max");
			Configure.x_point2_min = pm.Vd("Verify#x_point2_min");
			Configure.y_point2_max = pm.Vd("Verify#y_point2_max");
			Configure.y_point2_min = pm.Vd("Verify#y_point2_min");
			Configure.x_point3_max = pm.Vd("Verify#x_point3_max");
			Configure.x_point3_min = pm.Vd("Verify#x_point3_min");
			Configure.y_point3_max = pm.Vd("Verify#y_point3_max");
			Configure.y_point3_min = pm.Vd("Verify#y_point3_min");
			Configure.x_point13_max = pm.Vd("Verify#x_point13_max");
			Configure.x_point13_min = pm.Vd("Verify#x_point13_min");
			Configure.y_point13_max = pm.Vd("Verify#y_point13_max");
			Configure.y_point13_min = pm.Vd("Verify#y_point13_min");
			Configure.x_except_min = pm.Vd("Verify#x_except_min");
			Configure.x_except_max = pm.Vd("Verify#x_except_max");
			Configure.y_except_min = pm.Vd("Verify#y_except_min");
			Configure.y_except_max = pm.Vd("Verify#y_except_max");
			Configure.diff_min = pm.Vd("Verify#diff_min");
			Configure.diff_max = pm.Vd("Verify#diff_max");
			Configure.pressure_val = pm.Vd("Verify#pressure_val");
			Configure.vacuummeter_value = pm.Vd("Verify#vacuummeter_value");
			Configure.angle_max = pm.Vd("Verify#angle_max");
			Configure.line_diff_max = pm.Vd("Verify#line_diff_max");
			Configure.circle_center_diff_max = pm.Vd("Verify#circle_center_diff_max");
			Configure.uv_delay_time = pm.Vi("Product#uv_delay_time");
			Configure.vacuum_delay_time1 = pm.Vi("Product#vacuum_delay_time1");
			Configure.vacuum_delay_time2 = pm.Vi("Product#vacuum_delay_time2");
			Configure.vacuum_delay_time3 = pm.Vi("Product#vacuum_delay_time3");
			Configure.left_thresh = pm.Vi("Product#left_thresh");
			Configure.right_thresh = pm.Vi("Product#right_thresh");
			Configure.x_offset_max = pm.Vd("Verify#x_offset_max");
			Configure.y_offset_max = pm.Vd("Verify#y_offset_max");
			Configure.preCheck_x_offset_max = pm.Vi("Verify#preCheck_x_offset_max");
			Configure.preCheck_y_offset_max = pm.Vi("Verify#preCheck_y_offset_max");
			Configure.x_offset_compensatoin = pm.Vd("Product#x_offset_compensatoin");
			Configure.y_offset_compensatoin = pm.Vd("Product#y_offset_compensatoin");
			Configure.center_offset_compensatoin = pm.Vd("Product#center_offset_compensatoin");
			Configure.roi_x = pm.Vi("Product#roi_x");
			Configure.roi_y = pm.Vi("Product#roi_y");
			Configure.roi_width = pm.Vi("Product#roi_width");
			Configure.preUV_x_point1_max = pm.Vd("Verify#preUV_x_point1_max");
			Configure.preUV_x_point1_min = pm.Vd("Verify#preUV_x_point1_min");
			Configure.preUV_y_point1_max = pm.Vd("Verify#preUV_y_point1_max");
			Configure.preUV_y_point1_min = pm.Vd("Verify#preUV_y_point1_min");
			Configure.preUV_x_point2_max = pm.Vd("Verify#preUV_x_point2_max");
			Configure.preUV_x_point2_min = pm.Vd("Verify#preUV_x_point2_min");
			Configure.preUV_y_point2_max = pm.Vd("Verify#preUV_y_point2_max");
			Configure.preUV_y_point2_min = pm.Vd("Verify#preUV_y_point2_min");
			Configure.preUV_x_point3_max = pm.Vd("Verify#preUV_x_point3_max");
			Configure.preUV_x_point3_min = pm.Vd("Verify#preUV_x_point3_min");
			Configure.preUV_y_point3_max = pm.Vd("Verify#preUV_y_point3_max");
			Configure.preUV_y_point3_min = pm.Vd("Verify#preUV_y_point3_min");
			Configure.preUV_x_point13_max = pm.Vd("Verify#preUV_x_point13_max");
			Configure.preUV_x_point13_min = pm.Vd("Verify#preUV_x_point13_min");
			Configure.preUV_y_point13_max = pm.Vd("Verify#preUV_y_point13_max");
			Configure.preUV_y_point13_min = pm.Vd("Verify#preUV_y_point13_min");
			Configure.afterUV_x_point1_max = pm.Vd("Verify#afterUV_x_point1_max");
			Configure.afterUV_x_point1_min = pm.Vd("Verify#afterUV_x_point1_min");
			Configure.afterUV_y_point1_max = pm.Vd("Verify#afterUV_y_point1_max");
			Configure.afterUV_y_point1_min = pm.Vd("Verify#afterUV_y_point1_min");
			Configure.afterUV_x_point2_max = pm.Vd("Verify#afterUV_x_point2_max");
			Configure.afterUV_x_point2_min = pm.Vd("Verify#afterUV_x_point2_min");
			Configure.afterUV_y_point2_max = pm.Vd("Verify#afterUV_y_point2_max");
			Configure.afterUV_y_point2_min = pm.Vd("Verify#afterUV_y_point2_min");
			Configure.afterUV_x_point3_max = pm.Vd("Verify#afterUV_x_point3_max");
			Configure.afterUV_x_point3_min = pm.Vd("Verify#afterUV_x_point3_min");
			Configure.afterUV_y_point3_max = pm.Vd("Verify#afterUV_y_point3_max");
			Configure.afterUV_y_point3_min = pm.Vd("Verify#afterUV_y_point3_min");
			Configure.afterUV_x_point13_max = pm.Vd("Verify#afterUV_x_point13_max");
			Configure.afterUV_x_point13_min = pm.Vd("Verify#afterUV_x_point13_min");
			Configure.afterUV_y_point13_max = pm.Vd("Verify#afterUV_y_point13_max");
			Configure.afterUV_y_point13_min = pm.Vd("Verify#afterUV_y_point13_min");
			Configure.preUV_diff_min = pm.Vd("Verify#preUV_diff_min");
			Configure.preUV_diff_max = pm.Vd("Verify#preUV_diff_max");
			Configure.afterUV_diff_min = pm.Vd("Verify#afterUV_diff_min");
			Configure.afterUV_diff_max = pm.Vd("Verify#afterUV_diff_max");
			Configure.distance_ip_addr = pm.Vs("Product#distance_ip_addr");
			Configure.distance_max = pm.Vd("Verify#distance_max");
			Configure.distance_offset = pm.Vd("Product#distance_offset");
			Configure.distance_stop_before_thresh = pm.Vd("Product#distance_stop_before_thresh");
			Configure.phrase2_x_point1_max = pm.Vd("Verify#phrase2_x_point1_max");
			Configure.phrase2_x_point1_min = pm.Vd("Verify#phrase2_x_point1_min");
			Configure.phrase2_y_point1_max = pm.Vd("Verify#phrase2_y_point1_max");
			Configure.phrase2_y_point1_min = pm.Vd("Verify#phrase2_y_point1_min");
			Configure.phrase2_x_point2_max = pm.Vd("Verify#phrase2_x_point2_max");
			Configure.phrase2_x_point2_min = pm.Vd("Verify#phrase2_x_point2_min");
			Configure.phrase2_y_point2_max = pm.Vd("Verify#phrase2_y_point2_max");
			Configure.phrase2_y_point2_min = pm.Vd("Verify#phrase2_y_point2_min");
			Configure.phrase2_x_point3_max = pm.Vd("Verify#phrase2_x_point3_max");
			Configure.phrase2_x_point3_min = pm.Vd("Verify#phrase2_x_point3_min");
			Configure.phrase2_y_point3_max = pm.Vd("Verify#phrase2_y_point3_max");
			Configure.phrase2_y_point3_min = pm.Vd("Verify#phrase2_y_point3_min");
			Configure.phrase2_x_point13_max = pm.Vd("Verify#phrase2_x_point13_max");
			Configure.phrase2_x_point13_min = pm.Vd("Verify#phrase2_x_point13_min");
			Configure.phrase2_y_point13_max = pm.Vd("Verify#phrase2_y_point13_max");
			Configure.phrase2_y_point13_min = pm.Vd("Verify#phrase2_y_point13_min");
			Configure.preCheck_LT_roi_x = pm.Vi("Product#preCheck_LT_roi_x");
			Configure.preCheck_LT_roi_y = pm.Vi("Product#preCheck_LT_roi_y");
			Configure.preCheck_LT_roi_width = pm.Vi("Product#preCheck_LT_roi_width");
			Configure.preCheck_LT_roi_height = pm.Vi("Product#preCheck_LT_roi_height");
			Configure.preCheck_RT_roi_x = pm.Vi("Product#preCheck_RT_roi_x");
			Configure.preCheck_RT_roi_y = pm.Vi("Product#preCheck_RT_roi_y");
			Configure.preCheck_RT_roi_width = pm.Vi("Product#preCheck_RT_roi_width");
			Configure.preCheck_RT_roi_height = pm.Vi("Product#preCheck_RT_roi_height");
			Configure.preCheck_LB_roi_x = pm.Vi("Product#preCheck_LB_roi_x");
			Configure.preCheck_LB_roi_y = pm.Vi("Product#preCheck_LB_roi_y");
			Configure.preCheck_LB_roi_width = pm.Vi("Product#preCheck_LB_roi_width");
			Configure.preCheck_LB_roi_height = pm.Vi("Product#preCheck_LB_roi_height");
			Configure.preCheck_RB_roi_x = pm.Vi("Product#preCheck_RB_roi_x");
			Configure.preCheck_RB_roi_y = pm.Vi("Product#preCheck_RB_roi_y");
			Configure.preCheck_RB_roi_width = pm.Vi("Product#preCheck_RB_roi_width");
			Configure.preCheck_RB_roi_height = pm.Vi("Product#preCheck_RB_roi_height");
			Configure.preCheck_RB_roi_width = pm.Vi("Product#preCheck_RB_roi_width");
			Configure.preCheck_RB_roi_height = pm.Vi("Product#preCheck_RB_roi_height");
			Configure.preCheck_camera_max_Xdiff = pm.Vi("Verify#preCheck_camera_max_Xdiff");
			Configure.preCheck_camera_max_Ydiff = pm.Vi("Verify#preCheck_camera_max_Ydiff");
			Configure.preCheck_center_minX = pm.Vi("Verify#preCheck_center_minX");
			Configure.preCheck_center_maxX = pm.Vi("Verify#preCheck_center_maxX");
			Configure.preCheck_center_minY = pm.Vi("Verify#preCheck_center_minY");
			Configure.preCheck_center_maxY = pm.Vi("Verify#preCheck_center_maxY");
			Configure.preCheck_x_point1_min = pm.Vd("Verify#preCheck_x_point1_min");
			Configure.preCheck_x_point1_max = pm.Vd("Verify#preCheck_x_point1_max");
			Configure.preCheck_y_point1_min = pm.Vd("Verify#preCheck_y_point1_min");
			Configure.preCheck_y_point1_max = pm.Vd("Verify#preCheck_y_point1_max");
			Configure.preCheck_d_point1_min = pm.Vd("Verify#preCheck_d_point1_min");
			Configure.preCheck_d_point1_max = pm.Vd("Verify#preCheck_d_point1_max");
			Configure.preCheck_x_point2_min = pm.Vd("Verify#preCheck_x_point2_min");
			Configure.preCheck_x_point2_max = pm.Vd("Verify#preCheck_x_point2_max");
			Configure.preCheck_y_point2_min = pm.Vd("Verify#preCheck_y_point2_min");
			Configure.preCheck_y_point2_max = pm.Vd("Verify#preCheck_y_point2_max");
			Configure.preCheck_d_point2_min = pm.Vd("Verify#preCheck_d_point2_min");
			Configure.preCheck_d_point2_max = pm.Vd("Verify#preCheck_d_point2_max");
			Configure.distance_sensor_enable = pm.Vb("Product#distance_sensor_enable");
			Configure.presure_val_min = pm.Vd("Verify#presure_val_min");
			Configure.presure_val_max = pm.Vd("Verify#presure_val_max");
			Configure.thickness_val_min = pm.Vd("Verify#thickness_val_min");
			Configure.thickness_val_max = pm.Vd("Verify#thickness_val_max");
			Configure.uv_thickness_val_min = pm.Vd("Verify#uv_thickness_val_min");
			Configure.uv_thickness_val_max = pm.Vd("Verify#uv_thickness_val_max");
			Configure.thickness_refractivity = pm.Vd("Product#thickness_refractivity");
		}

		// Token: 0x0400009D RID: 157
		public List<string> database = new List<string>();

		// Token: 0x0400009E RID: 158
		public static string StationNO;

		// Token: 0x0400009F RID: 159
		public static string mes_resource = "NONE";

		// Token: 0x040000A0 RID: 160
		public static int sn_length;

		// Token: 0x040000A1 RID: 161
		public static int prism_sn_length;

		// Token: 0x040000A2 RID: 162
		public static int camera_exposure_time;

		// Token: 0x040000A3 RID: 163
		public static int camera_gain;

		// Token: 0x040000A4 RID: 164
		public static int ySpeedHigh;

		// Token: 0x040000A5 RID: 165
		public static int zSpeedHigh;

		// Token: 0x040000A6 RID: 166
		public static int ySpeedLow;

		// Token: 0x040000A7 RID: 167
		public static int pressureZSpeed;

		// Token: 0x040000A8 RID: 168
		public static int pressureZ2Speed;

		// Token: 0x040000A9 RID: 169
		public static int zSpeed3;

		// Token: 0x040000AA RID: 170
		public static int zDest3;

		// Token: 0x040000AB RID: 171
		public static int yDest1;

		// Token: 0x040000AC RID: 172
		public static int yDest2;

		// Token: 0x040000AD RID: 173
		public static int zDest1;

		// Token: 0x040000AE RID: 174
		public static int zDest2;

		// Token: 0x040000AF RID: 175
		public static bool Colimator_Use;

		// Token: 0x040000B0 RID: 176
		public static string Colimator_SerialPort1;

		// Token: 0x040000B1 RID: 177
		public static string Colimator_SerialPort2;

		// Token: 0x040000B2 RID: 178
		public static double x_point1_max;

		// Token: 0x040000B3 RID: 179
		public static double x_point1_min;

		// Token: 0x040000B4 RID: 180
		public static double y_point1_max;

		// Token: 0x040000B5 RID: 181
		public static double y_point1_min;

		// Token: 0x040000B6 RID: 182
		public static double x_point2_max;

		// Token: 0x040000B7 RID: 183
		public static double x_point2_min;

		// Token: 0x040000B8 RID: 184
		public static double y_point2_max;

		// Token: 0x040000B9 RID: 185
		public static double y_point2_min;

		// Token: 0x040000BA RID: 186
		public static double x_point3_max;

		// Token: 0x040000BB RID: 187
		public static double x_point3_min;

		// Token: 0x040000BC RID: 188
		public static double y_point3_max;

		// Token: 0x040000BD RID: 189
		public static double y_point3_min;

		// Token: 0x040000BE RID: 190
		public static double x_point13_max;

		// Token: 0x040000BF RID: 191
		public static double x_point13_min;

		// Token: 0x040000C0 RID: 192
		public static double y_point13_max;

		// Token: 0x040000C1 RID: 193
		public static double y_point13_min;

		// Token: 0x040000C2 RID: 194
		public static double x_except_min;

		// Token: 0x040000C3 RID: 195
		public static double x_except_max;

		// Token: 0x040000C4 RID: 196
		public static double y_except_min;

		// Token: 0x040000C5 RID: 197
		public static double y_except_max;

		// Token: 0x040000C6 RID: 198
		public static double phrase2_x_point1_max;

		// Token: 0x040000C7 RID: 199
		public static double phrase2_x_point1_min;

		// Token: 0x040000C8 RID: 200
		public static double phrase2_y_point1_max;

		// Token: 0x040000C9 RID: 201
		public static double phrase2_y_point1_min;

		// Token: 0x040000CA RID: 202
		public static double phrase2_x_point2_max;

		// Token: 0x040000CB RID: 203
		public static double phrase2_x_point2_min;

		// Token: 0x040000CC RID: 204
		public static double phrase2_y_point2_max;

		// Token: 0x040000CD RID: 205
		public static double phrase2_y_point2_min;

		// Token: 0x040000CE RID: 206
		public static double phrase2_x_point3_max;

		// Token: 0x040000CF RID: 207
		public static double phrase2_x_point3_min;

		// Token: 0x040000D0 RID: 208
		public static double phrase2_y_point3_max;

		// Token: 0x040000D1 RID: 209
		public static double phrase2_y_point3_min;

		// Token: 0x040000D2 RID: 210
		public static double phrase2_x_point13_max;

		// Token: 0x040000D3 RID: 211
		public static double phrase2_x_point13_min;

		// Token: 0x040000D4 RID: 212
		public static double phrase2_y_point13_max;

		// Token: 0x040000D5 RID: 213
		public static double phrase2_y_point13_min;

		// Token: 0x040000D6 RID: 214
		public static double preUV_x_point1_max;

		// Token: 0x040000D7 RID: 215
		public static double preUV_x_point1_min;

		// Token: 0x040000D8 RID: 216
		public static double preUV_y_point1_max;

		// Token: 0x040000D9 RID: 217
		public static double preUV_y_point1_min;

		// Token: 0x040000DA RID: 218
		public static double preUV_x_point2_max;

		// Token: 0x040000DB RID: 219
		public static double preUV_x_point2_min;

		// Token: 0x040000DC RID: 220
		public static double preUV_y_point2_max;

		// Token: 0x040000DD RID: 221
		public static double preUV_y_point2_min;

		// Token: 0x040000DE RID: 222
		public static double preUV_x_point3_max;

		// Token: 0x040000DF RID: 223
		public static double preUV_x_point3_min;

		// Token: 0x040000E0 RID: 224
		public static double preUV_y_point3_max;

		// Token: 0x040000E1 RID: 225
		public static double preUV_y_point3_min;

		// Token: 0x040000E2 RID: 226
		public static double preUV_x_point13_max;

		// Token: 0x040000E3 RID: 227
		public static double preUV_x_point13_min;

		// Token: 0x040000E4 RID: 228
		public static double preUV_y_point13_max;

		// Token: 0x040000E5 RID: 229
		public static double preUV_y_point13_min;

		// Token: 0x040000E6 RID: 230
		public static double preUV_x_except_min;

		// Token: 0x040000E7 RID: 231
		public static double preUV_x_except_max;

		// Token: 0x040000E8 RID: 232
		public static double preUV_y_except_min;

		// Token: 0x040000E9 RID: 233
		public static double preUV_y_except_max;

		// Token: 0x040000EA RID: 234
		public static double afterUV_x_point1_max;

		// Token: 0x040000EB RID: 235
		public static double afterUV_x_point1_min;

		// Token: 0x040000EC RID: 236
		public static double afterUV_y_point1_max;

		// Token: 0x040000ED RID: 237
		public static double afterUV_y_point1_min;

		// Token: 0x040000EE RID: 238
		public static double afterUV_x_point2_max;

		// Token: 0x040000EF RID: 239
		public static double afterUV_x_point2_min;

		// Token: 0x040000F0 RID: 240
		public static double afterUV_y_point2_max;

		// Token: 0x040000F1 RID: 241
		public static double afterUV_y_point2_min;

		// Token: 0x040000F2 RID: 242
		public static double afterUV_x_point3_max;

		// Token: 0x040000F3 RID: 243
		public static double afterUV_x_point3_min;

		// Token: 0x040000F4 RID: 244
		public static double afterUV_y_point3_max;

		// Token: 0x040000F5 RID: 245
		public static double afterUV_y_point3_min;

		// Token: 0x040000F6 RID: 246
		public static double afterUV_x_point13_max;

		// Token: 0x040000F7 RID: 247
		public static double afterUV_x_point13_min;

		// Token: 0x040000F8 RID: 248
		public static double afterUV_y_point13_max;

		// Token: 0x040000F9 RID: 249
		public static double afterUV_y_point13_min;

		// Token: 0x040000FA RID: 250
		public static double afterUV_x_except_min;

		// Token: 0x040000FB RID: 251
		public static double afterUV_x_except_max;

		// Token: 0x040000FC RID: 252
		public static double afterUV_y_except_min;

		// Token: 0x040000FD RID: 253
		public static double afterUV_y_except_max;

		// Token: 0x040000FE RID: 254
		public static double diff_min;

		// Token: 0x040000FF RID: 255
		public static double diff_max;

		// Token: 0x04000100 RID: 256
		public static double preUV_diff_min;

		// Token: 0x04000101 RID: 257
		public static double preUV_diff_max;

		// Token: 0x04000102 RID: 258
		public static double afterUV_diff_min;

		// Token: 0x04000103 RID: 259
		public static double afterUV_diff_max;

		// Token: 0x04000104 RID: 260
		public static double angle_max;

		// Token: 0x04000105 RID: 261
		public static double line_diff_max;

		// Token: 0x04000106 RID: 262
		public static double circle_center_diff_max;

		// Token: 0x04000107 RID: 263
		public static int vacuummeter_door_close_in;

		// Token: 0x04000108 RID: 264
		public static int uv_in;

		// Token: 0x04000109 RID: 265
		public static int vacuummeter_pump_out;

		// Token: 0x0400010A RID: 266
		public static int gas_release_out;

		// Token: 0x0400010B RID: 267
		public static int gas_out;

		// Token: 0x0400010C RID: 268
		public static int uv_out;

		// Token: 0x0400010D RID: 269
		public static int vacuummeter_pump_time;

		// Token: 0x0400010E RID: 270
		public static double vacuummeter_value;

		// Token: 0x0400010F RID: 271
		public static double pressure_val;

		// Token: 0x04000110 RID: 272
		public static double pisition_val;

		// Token: 0x04000111 RID: 273
		public static int uv_delay_time;

		// Token: 0x04000112 RID: 274
		public static int uv_operate_time;

		// Token: 0x04000113 RID: 275
		public static string vacuummeter_SerialPort;

		// Token: 0x04000114 RID: 276
		public static string press_sensor_SerialPort;

		// Token: 0x04000115 RID: 277
		public static int uv_time;

		// Token: 0x04000116 RID: 278
		public static int vacuum_delay_time1;

		// Token: 0x04000117 RID: 279
		public static int vacuum_delay_time2;

		// Token: 0x04000118 RID: 280
		public static int vacuum_delay_time3;

		// Token: 0x04000119 RID: 281
		public static int left_thresh;

		// Token: 0x0400011A RID: 282
		public static int right_thresh;

		// Token: 0x0400011B RID: 283
		public static double x_offset_max;

		// Token: 0x0400011C RID: 284
		public static double y_offset_max;

		// Token: 0x0400011D RID: 285
		public static double x_offset_compensatoin;

		// Token: 0x0400011E RID: 286
		public static double y_offset_compensatoin;

		// Token: 0x0400011F RID: 287
		public static double center_offset_compensatoin;

		// Token: 0x04000120 RID: 288
		public static int preCheck_x_offset_max;

		// Token: 0x04000121 RID: 289
		public static int preCheck_y_offset_max;

		// Token: 0x04000122 RID: 290
		public static int roi_x;

		// Token: 0x04000123 RID: 291
		public static int roi_y;

		// Token: 0x04000124 RID: 292
		public static int roi_width;

		// Token: 0x04000125 RID: 293
		public static string distance_ip_addr;

		// Token: 0x04000126 RID: 294
		public static double distance_max;

		// Token: 0x04000127 RID: 295
		public static double distance_offset;

		// Token: 0x04000128 RID: 296
		public static double distance_stop_before_thresh;

		// Token: 0x04000129 RID: 297
		public static int preCheck_LT_roi_x;

		// Token: 0x0400012A RID: 298
		public static int preCheck_LT_roi_y;

		// Token: 0x0400012B RID: 299
		public static int preCheck_LT_roi_width;

		// Token: 0x0400012C RID: 300
		public static int preCheck_LT_roi_height;

		// Token: 0x0400012D RID: 301
		public static int preCheck_RT_roi_x;

		// Token: 0x0400012E RID: 302
		public static int preCheck_RT_roi_y;

		// Token: 0x0400012F RID: 303
		public static int preCheck_RT_roi_width;

		// Token: 0x04000130 RID: 304
		public static int preCheck_RT_roi_height;

		// Token: 0x04000131 RID: 305
		public static int preCheck_LB_roi_x;

		// Token: 0x04000132 RID: 306
		public static int preCheck_LB_roi_y;

		// Token: 0x04000133 RID: 307
		public static int preCheck_LB_roi_width;

		// Token: 0x04000134 RID: 308
		public static int preCheck_LB_roi_height;

		// Token: 0x04000135 RID: 309
		public static int preCheck_RB_roi_x;

		// Token: 0x04000136 RID: 310
		public static int preCheck_RB_roi_y;

		// Token: 0x04000137 RID: 311
		public static int preCheck_RB_roi_width;

		// Token: 0x04000138 RID: 312
		public static int preCheck_RB_roi_height;

		// Token: 0x04000139 RID: 313
		public static int preCheck_camera_max_Xdiff;

		// Token: 0x0400013A RID: 314
		public static int preCheck_camera_max_Ydiff;

		// Token: 0x0400013B RID: 315
		public static int preCheck_center_minX;

		// Token: 0x0400013C RID: 316
		public static int preCheck_center_maxX;

		// Token: 0x0400013D RID: 317
		public static int preCheck_center_minY;

		// Token: 0x0400013E RID: 318
		public static int preCheck_center_maxY;

		// Token: 0x0400013F RID: 319
		public static double preCheck_x_point1_min;

		// Token: 0x04000140 RID: 320
		public static double preCheck_x_point1_max;

		// Token: 0x04000141 RID: 321
		public static double preCheck_y_point1_min;

		// Token: 0x04000142 RID: 322
		public static double preCheck_y_point1_max;

		// Token: 0x04000143 RID: 323
		public static double preCheck_d_point1_min;

		// Token: 0x04000144 RID: 324
		public static double preCheck_d_point1_max;

		// Token: 0x04000145 RID: 325
		public static double preCheck_x_point2_min;

		// Token: 0x04000146 RID: 326
		public static double preCheck_x_point2_max;

		// Token: 0x04000147 RID: 327
		public static double preCheck_y_point2_min;

		// Token: 0x04000148 RID: 328
		public static double preCheck_y_point2_max;

		// Token: 0x04000149 RID: 329
		public static double preCheck_d_point2_min;

		// Token: 0x0400014A RID: 330
		public static double preCheck_d_point2_max;

		// Token: 0x0400014B RID: 331
		public static bool distance_sensor_enable;

		// Token: 0x0400014C RID: 332
		public static double presure_val_min;

		// Token: 0x0400014D RID: 333
		public static double presure_val_max;

		// Token: 0x0400014E RID: 334
		public static double thickness_val_min;

		// Token: 0x0400014F RID: 335
		public static double thickness_val_max;

		// Token: 0x04000150 RID: 336
		public static double uv_thickness_val_min;

		// Token: 0x04000151 RID: 337
		public static double uv_thickness_val_max;

		// Token: 0x04000152 RID: 338
		public static double thickness_refractivity;
	}
}
