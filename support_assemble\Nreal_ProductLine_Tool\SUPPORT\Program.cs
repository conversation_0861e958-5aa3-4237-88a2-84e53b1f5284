﻿using System;
using System.Windows.Forms;

namespace Nreal_ProductLine_Tool.SUPPORT
{
	// Token: 0x0200001F RID: 31
	internal static class Program
	{
		// Token: 0x06000146 RID: 326 RVA: 0x00018024 File Offset: 0x00016224
		[STAThread]
		private static void Main(string[] args)
		{
			Application.EnableVisualStyles();
			Application.SetCompatibleTextRenderingDefault(false);
			bool flag = args.Length == 10;
			if (flag)
			{
				LOGINFO.mode = args[0];
				LOGINFO.user = args[1];
				LOGINFO.rname = args[2];
				LOGINFO.lname = args[3];
				LOGINFO.pname = args[4];
				LOGINFO.configMode = args[5];
				LOGINFO.dbWrite = args[6];
				LOGINFO.mesEnable = args[7];
				LOGINFO.pass = args[8];
				Application.Run(new main());
			}
			bool flag2 = args.Length == 0;
			if (flag2)
			{
				LOGINFO.user = "test";
				LOGINFO.mode = "offline";
				LOGINFO.configMode = "local";
				LOGINFO.dbWrite = "false";
				LOGINFO.mesEnable = "false";
				LOGINFO.scan = "false";
				LOGINFO.pname = "GINA";
				LOGINFO.lname = "G1";
				Application.Run(new Entrance());
			}
		}
	}
}
