﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
	<configSections>
		<section name="log4net" type="log4net.Config.Log4NetConfigurationSectionHandler,log4net"/>
	</configSections>
	<log4net>
		<appender name="mylog4" type="log4net.Appender.RollingFileAppender">
			<!--是否追加到文件:日志文件路径,文件夹不存在则新建 -->
			<param name="File" value="Logs\" />
			<!--是否追加到文件-->
			<param name="AppendToFile" value="true" />
			<!--最多产生的日志文件，-1不限-->
			<param name="MaxSizeRollBackups" value="-1" />
			<!--最小锁定模型以允许多个进程可以写入同一个文件-->
			<param name="lockingModel"  type="log4net.Appender.FileAppender+MinimalLock" />
			<!--每个文件的大小-->
			<param name="MaximumFileSize" value="10MB" />
			<!--产生多个日志文件，以什么方式生成 (日期[Date],文件大小[Size],混合[Composite])-->
			<param name="RollingStyle" value="Size" />
			<!--此处按日期产生文件夹-->
			<param name="DatePattern" value="yyyy-MM-dd" />
			<!--是否只写到一个文件之中-->
			<param name="StaticLogFileName" value="false" />
			<!--记录的格式。一般用log4net.Layout.PatternLayout布局-->
			<layout type="log4net.Layout.PatternLayout">
				<param name="ConversionPattern" value="%-5p %d %m%n" />
			</layout>
		</appender>
		<logger name="mylog4">
			<level value="all" />
			<appender-ref ref="mylog4" />
		</logger>
		<root>
			<level value="all" />
		</root>
	</log4net>
</configuration>